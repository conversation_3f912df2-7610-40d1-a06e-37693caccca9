# 迷宫密封-转子系统技术实现详细分析

## 核心算法实现

### 1. 密封力计算算法

#### 密封系数计算
```python
def calculate_seal_coefficients(self, omega, eM):
    """计算密封系数 K, D, tau_M, mf"""
    Ra, Rv = self.calculate_reynolds_numbers(omega)
    
    # 基础刚度和阻尼 (简化计算)
    K_base = self.delta_p * self.L / (self.Cr**2) * 1e-6  # 简化刚度
    D_base = self.mu * self.L / self.Cr * 1e3  # 简化阻尼
    
    # 偏心率影响
    eccentricity_factor = (1 - eM**2)**self.n
    
    # 密封刚度和阻尼
    K = K_base * eccentricity_factor
    D = D_base * eccentricity_factor
    
    # 流体惯性系数
    tau_M = self.tau0 * (1 - eM**2)**self.b
    
    # 有效质量 (简化)
    mf_eff = self.rho * np.pi * self.Rs1**2 * self.L * 0.1
    
    return K, D, tau_M, mf_eff
```

#### 雷诺数计算
```python
def calculate_reynolds_numbers(self, omega):
    """计算雷诺数"""
    # 轴向雷诺数
    v_axial = np.sqrt(2 * self.delta_p / self.rho)
    Ra = self.rho * v_axial * self.Cr / self.mu
    
    # 周向雷诺数
    v_circumferential = omega * self.Rs1
    Rv = self.rho * v_circumferential * self.Cr / self.mu
    
    return Ra, Rv
```

### 2. 系统动力学方程求解

#### 状态空间表示
系统状态向量: `[x, y, vx, vy]`
- x, y: 水平和垂直位移
- vx, vy: 水平和垂直速度

#### 动力学方程
```python
def system_equations(self, state, t, omega):
    """系统动力学方程 (方程14)"""
    x, y, vx, vy = state
    
    # 计算各种力
    Fsx, Fsy = self.calculate_seal_force(x, y, vx, vy, 0, 0, omega, t)
    Fx_friction, Fy_friction = self.calculate_coupling_friction_force(vx, vy)
    
    # 不平衡力
    F_unbalance_x = self.mp * self.rp * omega**2 * np.cos(omega * t)
    F_unbalance_y = self.mp * self.rp * omega**2 * np.sin(omega * t)
    
    # 重力
    F_gravity_y = -self.m * self.g
    
    # 总外力
    Fx_total = Fsx + F_unbalance_x + Fx_friction
    Fy_total = Fsy + F_unbalance_y + F_gravity_y + Fy_friction
    
    # 运动方程
    ax = (Fx_total - self.De * vx - self.Ke * x) / self.m
    ay = (Fy_total - self.De * vy - self.Ke * y) / self.m
    
    return [vx, vy, ax, ay]
```

### 3. 数值积分方法

使用SciPy的`odeint`函数，基于LSODA算法：
- 自适应步长控制
- 刚性和非刚性问题自动切换
- 高精度数值积分

```python
def solve_system(self, omega, t_span, initial_conditions):
    """求解系统动力学方程"""
    t = np.linspace(0, t_span, int(t_span * 1000))  # 1000 Hz采样
    
    def equations(state, t):
        return self.system_equations(state, t, omega)
    
    solution = odeint(equations, initial_conditions, t)
    return t, solution
```

## 分析方法实现

### 1. 动态特性系数分析

#### 系数计算公式
基于方程15的实现：
```python
def calculate_dynamic_coefficients(self, omega_range):
    """计算动态特性系数随转速的变化"""
    K1_list, K2_list, D1_list, D2_list = [], [], [], []
    
    for omega in omega_range:
        eM = 0.1  # 假设偏心率
        K, D, tau_M, mf_eff = self.calculate_seal_coefficients(omega, eM)
        
        # 根据方程15计算系数
        K1 = self.Ke + K - tau_M**2 * omega**2 * mf_eff / self.mf * omega**2
        K2 = tau_M * D / self.mf * omega
        D1 = self.De + D / (self.mf * omega)
        D2 = 2 * tau_M * mf_eff / self.mf
        
        K1_list.append(K1)
        K2_list.append(K2)
        D1_list.append(D1)
        D2_list.append(D2)
    
    return np.array(K1_list), np.array(K2_list), np.array(D1_list), np.array(D2_list)
```

### 2. 分岔图分析

#### 分岔点识别方法
```python
def plot_bifurcation_diagram(self, omega_range, delta_p_values=None):
    """绘制分岔图"""
    for omega in omega_range:
        # 求解系统
        t, solution = self.solve_system(omega, t_span, initial_conditions)
        x, y, vx, vy = solution.T
        
        # 取后半段数据避免瞬态影响
        steady_start = int(len(t) * 0.5)
        x_steady = x[steady_start:]
        y_steady = y[steady_start:]
        
        # 计算最大位移
        displacement = np.sqrt(x_steady**2 + y_steady**2)
        max_displacement = np.max(displacement)
        max_displacements.append(max_displacement)
```

### 3. 频谱分析

#### FFT实现
```python
def frequency_analysis(self, omega, delta_p_values=None, t_span=5.0):
    """频谱分析"""
    # 求解系统
    t, solution = self.solve_system(omega, t_span, initial_conditions)
    x, y, vx, vy = solution.T
    
    # 取稳态部分
    steady_start = int(len(t) * 0.6)
    x_steady = x[steady_start:]
    
    # FFT分析
    dt = t[1] - t[0]
    fs = 1 / dt
    n = len(x_steady)
    
    # 计算FFT
    fft_x = np.fft.fft(x_steady)
    freqs = np.fft.fftfreq(n, dt)
    
    # 只取正频率部分
    positive_freqs = freqs[:n//2]
    magnitude = np.abs(fft_x[:n//2]) * 2 / n
```

## 参数影响分析

### 1. 轴向压降影响
通过改变`self.delta_p`参数，分析其对系统特性的影响：
- 线性影响动态特性系数
- 影响分岔点位置
- 可抑制一阶临界转速偏移

### 2. 密封间隙影响
通过改变`self.Cr`参数：
- 非线性影响系统稳定性
- 存在最优间隙值
- 过大或过小都不利于稳定性

### 3. 密封长度影响
通过改变`self.L`参数：
- 提高密封效果
- 但会使不稳定转速提前
- 需要权衡密封性能和稳定性

## 可视化实现

### 1. 多子图布局
使用matplotlib的subplot功能创建复合图表：
```python
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
```

### 2. 高质量图表输出
- DPI设置为300，确保高分辨率
- 使用`bbox_inches='tight'`优化布局
- 支持多种图表类型：线图、散点图、频谱图、轨道图

### 3. 中文字体支持
```python
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
```

## 性能优化

### 1. 计算优化
- 使用NumPy向量化操作
- 避免不必要的重复计算
- 合理设置采样频率和时间跨度

### 2. 内存管理
- 及时释放大型数组
- 使用生成器减少内存占用
- 分批处理大规模参数扫描

### 3. 数值稳定性
- 限制偏心率范围避免奇异值
- 使用适当的数值精度
- 处理除零和溢出情况

## 验证和测试

### 1. 单元测试
- 基本功能测试
- 参数边界测试
- 数值精度验证

### 2. 集成测试
- 完整分析流程测试
- 多参数组合测试
- 长时间运行稳定性测试

### 3. 结果验证
- 与论文结果对比
- 物理合理性检查
- 数值收敛性验证

## 扩展性设计

### 1. 模块化架构
- 清晰的类结构设计
- 功能模块独立
- 易于添加新的分析方法

### 2. 参数化设计
- 所有物理参数可配置
- 支持参数文件输入
- 批量参数分析支持

### 3. 接口设计
- 标准化的函数接口
- 一致的数据格式
- 良好的错误处理机制

这种技术实现确保了项目的科学性、准确性和可扩展性，为转子动力学研究提供了可靠的数值分析工具。
