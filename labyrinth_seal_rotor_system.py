"""
迷宫密封-转子系统非线性动力学分析
考虑联轴器内摩擦的影响

基于论文: "Nonlinear dynamics analysis of labyrinth seal-rotor system considering internal friction in coupling"
作者: <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import odeint
import matplotlib.patches as patches
from mpl_toolkits.mplot3d import Axes3D
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class LabyrinthSealRotorSystem:
    """迷宫密封-转子系统类"""

    def __init__(self):
        """初始化系统参数"""
        # 基本结构参数 (Table 1)
        self.Rs1 = 0.25  # 密封半径 (m)
        self.Cr = 0.0001  # 径向间隙 (m)
        self.h = 0.003  # 齿高 (m)
        self.L = 0.05  # 密封长度 (m)
        self.delta_p = 0.2e6  # 轴向压降 (Pa)
        self.theta = 20 * np.pi / 180  # 齿形压力角 (rad)
        self.E = 210e9  # 弹性模量 (Pa)
        self.w = 50  # 联轴器齿数
        self.coupling_modulus = 1  # 联轴器模量

        # 转子参数
        self.m = 100  # 转子质量 (kg)
        self.mf = 10  # 密封盘质量 (kg)
        self.Ke = 1e6  # 转子刚度 (N/m)
        self.De = 100  # 转子外阻尼 (N·s/m)
        self.mp = 0.1  # 不平衡质量 (kg)
        self.rp = 0.001  # 不平衡半径 (m)
        self.g = 9.81  # 重力加速度 (m/s²)

        # 流体参数
        self.mu = 1.8e-5  # 动力粘度 (Pa·s)
        self.rho = 1.225  # 密度 (kg/m³)
        self.R_gas = 287  # 气体常数 (J/kg·K)
        self.T = 300  # 温度 (K)

        # 经验系数
        self.n = 0.5
        self.b = 0.5
        self.tau0 = 0.5
        self.zeta_m = 0.1
        self.sigma_m = 0.1
        self.mu1 = 0.6
        self.mu2 = 0.8
        self.m0 = 0.5
        self.n0 = 0.5

        # 联轴器摩擦参数
        self.mu_f = 0.1  # 摩擦系数
        self.Mf = 1000  # 允许扭矩 (N·m)
        self.rf = 0.1  # 节圆半径 (m)

    def calculate_reynolds_numbers(self, omega):
        """计算雷诺数"""
        # 轴向雷诺数
        v_axial = np.sqrt(2 * self.delta_p / self.rho)
        Ra = self.rho * v_axial * self.Cr / self.mu

        # 周向雷诺数
        v_circumferential = omega * self.Rs1
        Rv = self.rho * v_circumferential * self.Cr / self.mu

        return Ra, Rv

    def calculate_intermediate_variables(self, Ra, Rv):
        """计算中间变量Em, Bm, Tm"""
        Em = 1 / (1 + self.zeta_m**2 * self.sigma_m)
        Bm = 2 * Rv * Em / (Ra**2 * (1 + Rv**2 / Ra**2))
        Tm = np.pi * self.Rs1 * self.delta_p / (self.L * self.mu * np.sqrt(2 * self.delta_p / self.rho))
        return Em, Bm, Tm

    def calculate_seal_coefficients(self, omega, eM):
        """计算密封系数 K, D, tau_M, mf"""
        Ra, Rv = self.calculate_reynolds_numbers(omega)

        # 简化的密封系数计算 (基于经验公式)
        # 这些系数需要根据实际情况调整

        # 基础刚度和阻尼 (简化计算)
        K_base = self.delta_p * self.L / (self.Cr**2) * 1e-6  # 简化刚度
        D_base = self.mu * self.L / self.Cr * 1e3  # 简化阻尼

        # 偏心率影响
        eccentricity_factor = (1 - eM**2)**self.n

        # 密封刚度和阻尼
        K = K_base * eccentricity_factor
        D = D_base * eccentricity_factor

        # 流体惯性系数
        tau_M = self.tau0 * (1 - eM**2)**self.b

        # 有效质量 (简化)
        mf_eff = self.rho * np.pi * self.Rs1**2 * self.L * 0.1  # 简化有效质量

        return K, D, tau_M, mf_eff

    def calculate_seal_force(self, x, y, vx, vy, ax, ay, omega, t):
        """计算密封力 (方程1)"""
        # 计算偏心率
        eM = np.sqrt(x**2 + y**2) / self.Cr
        eM = min(eM, 0.9)  # 限制偏心率

        # 获取密封系数
        K, D, tau_M, mf_eff = self.calculate_seal_coefficients(omega, eM)

        # 密封力系数矩阵 (方程1)
        K_matrix = np.array([[K - mf_eff * tau_M**2 / self.mf * omega**2, -tau_M * omega * D / self.mf],
                            [tau_M * omega * D / self.mf, K - mf_eff * tau_M**2 / self.mf * omega**2]])

        D_matrix = np.array([[D, -2 * tau_M * mf_eff * omega / self.mf],
                            [2 * tau_M * mf_eff * omega / self.mf, D]])

        M_matrix = np.array([[mf_eff, 0],
                            [0, mf_eff]])

        # 位移、速度、加速度向量
        displacement = np.array([x, y])
        velocity = np.array([vx, vy])
        acceleration = np.array([ax, ay])

        # 计算密封力
        Fs = -K_matrix @ displacement - D_matrix @ velocity - M_matrix @ acceleration

        return Fs[0], Fs[1]

    def calculate_coupling_friction_force(self, vx, vy):
        """计算联轴器内摩擦力 (方程11)"""
        # 相对速度
        v_rel = np.sqrt(vx**2 + vy**2)

        if v_rel < 1e-10:
            return 0, 0

        # 摩擦力大小 (方程11)
        F_friction_magnitude = 2 * self.mu_f * self.Mf / (np.pi * np.cos(self.theta))

        # 摩擦力方向与相对速度相反
        Fx_friction = -F_friction_magnitude * vx / v_rel
        Fy_friction = -F_friction_magnitude * vy / v_rel

        return Fx_friction, Fy_friction

    def system_equations(self, state, t, omega):
        """系统动力学方程 (方程14)"""
        x, y, vx, vy = state

        # 计算加速度 (先假设为0，用于密封力计算)
        ax_temp, ay_temp = 0, 0

        # 计算密封力
        Fsx, Fsy = self.calculate_seal_force(x, y, vx, vy, ax_temp, ay_temp, omega, t)

        # 计算联轴器内摩擦力
        Fx_friction, Fy_friction = self.calculate_coupling_friction_force(vx, vy)

        # 不平衡力
        F_unbalance_x = self.mp * self.rp * omega**2 * np.cos(omega * t)
        F_unbalance_y = self.mp * self.rp * omega**2 * np.sin(omega * t)

        # 重力
        F_gravity_y = -self.m * self.g

        # 总外力
        Fx_total = Fsx + F_unbalance_x + Fx_friction
        Fy_total = Fsy + F_unbalance_y + F_gravity_y + Fy_friction

        # 运动方程 (方程12简化形式)
        ax = (Fx_total - self.De * vx - self.Ke * x) / self.m
        ay = (Fy_total - self.De * vy - self.Ke * y) / self.m

        return [vx, vy, ax, ay]

    def solve_system(self, omega, t_span, initial_conditions):
        """求解系统动力学方程"""
        t = np.linspace(0, t_span, int(t_span * 1000))  # 1000 Hz采样

        def equations(state, t):
            return self.system_equations(state, t, omega)

        solution = odeint(equations, initial_conditions, t)

        return t, solution

    def calculate_dynamic_coefficients(self, omega_range):
        """计算动态特性系数随转速的变化 (图4)"""
        K1_list, K2_list, D1_list, D2_list = [], [], [], []

        for omega in omega_range:
            eM = 0.1  # 假设偏心率
            K, D, tau_M, mf_eff = self.calculate_seal_coefficients(omega, eM)

            # 根据方程15计算系数
            K1 = self.Ke + K - tau_M**2 * omega**2 * mf_eff / self.mf * omega**2
            K2 = tau_M * D / self.mf * omega
            D1 = self.De + D / (self.mf * omega)
            D2 = 2 * tau_M * mf_eff / self.mf

            K1_list.append(K1)
            K2_list.append(K2)
            D1_list.append(D1)
            D2_list.append(D2)

        return np.array(K1_list), np.array(K2_list), np.array(D1_list), np.array(D2_list)

    def plot_dynamic_coefficients_vs_speed(self, omega_range):
        """绘制动态特性系数随转速变化图 (图4)"""
        K1, K2, D1, D2 = self.calculate_dynamic_coefficients(omega_range)

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))

        ax1.plot(omega_range, K1, 'b-', linewidth=2, label='K1 (直接刚度)')
        ax1.set_xlabel('转速 (rad/s)')
        ax1.set_ylabel('K1 (N/m)')
        ax1.grid(True)
        ax1.legend()
        ax1.set_title('直接刚度 K1 vs 转速')

        ax2.plot(omega_range, K2, 'r-', linewidth=2, label='K2 (交叉刚度)')
        ax2.set_xlabel('转速 (rad/s)')
        ax2.set_ylabel('K2 (N/m)')
        ax2.grid(True)
        ax2.legend()
        ax2.set_title('交叉刚度 K2 vs 转速')

        ax3.plot(omega_range, D1, 'g-', linewidth=2, label='D1 (直接阻尼)')
        ax3.set_xlabel('转速 (rad/s)')
        ax3.set_ylabel('D1 (N·s/m)')
        ax3.grid(True)
        ax3.legend()
        ax3.set_title('直接阻尼 D1 vs 转速')

        ax4.plot(omega_range, D2, 'm-', linewidth=2, label='D2 (交叉阻尼)')
        ax4.set_xlabel('转速 (rad/s)')
        ax4.set_ylabel('D2 (N·s/m)')
        ax4.grid(True)
        ax4.legend()
        ax4.set_title('交叉阻尼 D2 vs 转速')

        plt.tight_layout()
        plt.savefig('dynamic_coefficients_vs_speed.png', dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_pressure_drop_effect(self, pressure_drops, omega=340):
        """分析轴向压降对系统特性的影响 (图5)"""
        K1_list, K2_list, D1_list, D2_list = [], [], [], []

        original_delta_p = self.delta_p

        for dp in pressure_drops:
            self.delta_p = dp
            eM = 0.1  # 假设偏心率
            K, D, tau_M, mf_eff = self.calculate_seal_coefficients(omega, eM)

            # 计算系数
            K1 = self.Ke + K - tau_M**2 * omega**2 * mf_eff / self.mf * omega**2
            K2 = tau_M * D / self.mf * omega
            D1 = self.De + D / (self.mf * omega)
            D2 = 2 * tau_M * mf_eff / self.mf

            K1_list.append(K1)
            K2_list.append(K2)
            D1_list.append(D1)
            D2_list.append(D2)

        self.delta_p = original_delta_p  # 恢复原值

        # 绘图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))

        pressure_drops_mpa = np.array(pressure_drops) / 1e6

        ax1.plot(pressure_drops_mpa, K1_list, 'b-o', linewidth=2, markersize=6)
        ax1.set_xlabel('轴向压降 (MPa)')
        ax1.set_ylabel('K1 (N/m)')
        ax1.grid(True)
        ax1.set_title('直接刚度 K1 vs 轴向压降')

        ax2.plot(pressure_drops_mpa, K2_list, 'r-o', linewidth=2, markersize=6)
        ax2.set_xlabel('轴向压降 (MPa)')
        ax2.set_ylabel('K2 (N/m)')
        ax2.grid(True)
        ax2.set_title('交叉刚度 K2 vs 轴向压降')

        ax3.plot(pressure_drops_mpa, D1_list, 'g-o', linewidth=2, markersize=6)
        ax3.set_xlabel('轴向压降 (MPa)')
        ax3.set_ylabel('D1 (N·s/m)')
        ax3.grid(True)
        ax3.set_title('直接阻尼 D1 vs 轴向压降')

        ax4.plot(pressure_drops_mpa, D2_list, 'm-o', linewidth=2, markersize=6)
        ax4.set_xlabel('轴向压降 (MPa)')
        ax4.set_ylabel('D2 (N·s/m)')
        ax4.grid(True)
        ax4.set_title('交叉阻尼 D2 vs 轴向压降')

        plt.tight_layout()
        plt.savefig('pressure_drop_effect.png', dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_seal_force_time_history(self, omega=340, t_span=1.0, delta_p_values=None):
        """分析密封力时间历程 (图6)"""
        if delta_p_values is None:
            delta_p_values = [0.2e6, 0.4e6, 0.6e6]

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

        original_delta_p = self.delta_p

        for i, dp in enumerate(delta_p_values):
            self.delta_p = dp

            # 初始条件
            initial_conditions = [0.0001, 0.0001, 0, 0]  # [x, y, vx, vy]

            # 求解系统
            t, solution = self.solve_system(omega, t_span, initial_conditions)
            x, y, vx, vy = solution.T

            # 计算密封力
            Fsx_list, Fsy_list = [], []
            for j in range(len(t)):
                ax_temp, ay_temp = 0, 0  # 简化
                Fsx, Fsy = self.calculate_seal_force(x[j], y[j], vx[j], vy[j], ax_temp, ay_temp, omega, t[j])
                Fsx_list.append(Fsx)
                Fsy_list.append(Fsy)

            label = f'Δp = {dp/1e6:.1f} MPa'
            ax1.plot(t, Fsx_list, linewidth=2, label=label)
            ax2.plot(t, Fsy_list, linewidth=2, label=label)

        self.delta_p = original_delta_p  # 恢复原值

        ax1.set_xlabel('时间 (s)')
        ax1.set_ylabel('水平密封力 Fsx (N)')
        ax1.grid(True)
        ax1.legend()
        ax1.set_title('水平密封力时间历程')

        ax2.set_xlabel('时间 (s)')
        ax2.set_ylabel('垂直密封力 Fsy (N)')
        ax2.grid(True)
        ax2.legend()
        ax2.set_title('垂直密封力时间历程')

        plt.tight_layout()
        plt.savefig('seal_force_time_history.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_bifurcation_diagram(self, omega_range, delta_p_values=None):
        """绘制分岔图 (图7)"""
        if delta_p_values is None:
            delta_p_values = [0.2e6, 0.6e6]

        fig, axes = plt.subplots(1, len(delta_p_values), figsize=(15, 6))
        if len(delta_p_values) == 1:
            axes = [axes]

        original_delta_p = self.delta_p

        for idx, dp in enumerate(delta_p_values):
            self.delta_p = dp

            max_displacements = []

            for omega in omega_range:
                # 初始条件
                initial_conditions = [0.0001, 0.0001, 0, 0]

                # 求解系统 (较短时间用于分岔分析)
                t_span = 2.0
                t, solution = self.solve_system(omega, t_span, initial_conditions)
                x, y, vx, vy = solution.T

                # 取后半段数据避免瞬态影响
                steady_start = int(len(t) * 0.5)
                x_steady = x[steady_start:]
                y_steady = y[steady_start:]

                # 计算最大位移
                displacement = np.sqrt(x_steady**2 + y_steady**2)
                max_displacement = np.max(displacement)
                max_displacements.append(max_displacement)

            axes[idx].plot(omega_range, max_displacements, 'b-', linewidth=2)
            axes[idx].set_xlabel('转速 (rad/s)')
            axes[idx].set_ylabel('最大位移 (m)')
            axes[idx].grid(True)
            axes[idx].set_title(f'分岔图 (Δp = {dp/1e6:.1f} MPa)')
            axes[idx].set_yscale('log')

        self.delta_p = original_delta_p  # 恢复原值

        plt.tight_layout()
        plt.savefig('bifurcation_diagram.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_orbit_and_time_history(self, omega_values, delta_p=0.6e6, t_span=2.0):
        """绘制轨道图和时间历程 (图9)"""
        fig = plt.figure(figsize=(16, 12))

        original_delta_p = self.delta_p
        self.delta_p = delta_p

        for i, omega in enumerate(omega_values):
            # 初始条件
            initial_conditions = [0.0001, 0.0001, 0, 0]

            # 求解系统
            t, solution = self.solve_system(omega, t_span, initial_conditions)
            x, y, vx, vy = solution.T

            # 取稳态部分
            steady_start = int(len(t) * 0.5)
            t_steady = t[steady_start:]
            x_steady = x[steady_start:]
            y_steady = y[steady_start:]

            # 时间历程图
            ax_time = plt.subplot(2, len(omega_values), i + 1)
            ax_time.plot(t_steady, x_steady, 'b-', linewidth=1.5, label='x')
            ax_time.plot(t_steady, y_steady, 'r-', linewidth=1.5, label='y')
            ax_time.set_xlabel('时间 (s)')
            ax_time.set_ylabel('位移 (m)')
            ax_time.grid(True)
            ax_time.legend()
            ax_time.set_title(f'时间历程 (ω = {omega} rad/s)')

            # 轨道图
            ax_orbit = plt.subplot(2, len(omega_values), i + 1 + len(omega_values))
            ax_orbit.plot(x_steady, y_steady, 'b-', linewidth=1.5)
            ax_orbit.set_xlabel('x (m)')
            ax_orbit.set_ylabel('y (m)')
            ax_orbit.grid(True)
            ax_orbit.set_title(f'轨道图 (ω = {omega} rad/s)')
            ax_orbit.axis('equal')

        self.delta_p = original_delta_p

        plt.tight_layout()
        plt.savefig('orbit_and_time_history.png', dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_clearance_effect(self, clearance_values, omega=340):
        """分析密封间隙对系统特性的影响 (图10)"""
        K1_list, K2_list, D1_list, D2_list = [], [], [], []

        original_Cr = self.Cr

        for cr in clearance_values:
            self.Cr = cr
            eM = 0.1  # 假设偏心率
            K, D, tau_M, mf_eff = self.calculate_seal_coefficients(omega, eM)

            # 计算系数
            K1 = self.Ke + K - tau_M**2 * omega**2 * mf_eff / self.mf * omega**2
            K2 = tau_M * D / self.mf * omega
            D1 = self.De + D / (self.mf * omega)
            D2 = 2 * tau_M * mf_eff / self.mf

            K1_list.append(K1)
            K2_list.append(K2)
            D1_list.append(D1)
            D2_list.append(D2)

        self.Cr = original_Cr  # 恢复原值

        # 绘图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))

        clearance_mm = np.array(clearance_values) * 1000  # 转换为mm

        ax1.plot(clearance_mm, K1_list, 'b-o', linewidth=2, markersize=6)
        ax1.set_xlabel('密封间隙 (mm)')
        ax1.set_ylabel('K1 (N/m)')
        ax1.grid(True)
        ax1.set_title('直接刚度 K1 vs 密封间隙')

        ax2.plot(clearance_mm, K2_list, 'r-o', linewidth=2, markersize=6)
        ax2.set_xlabel('密封间隙 (mm)')
        ax2.set_ylabel('K2 (N/m)')
        ax2.grid(True)
        ax2.set_title('交叉刚度 K2 vs 密封间隙')

        ax3.plot(clearance_mm, D1_list, 'g-o', linewidth=2, markersize=6)
        ax3.set_xlabel('密封间隙 (mm)')
        ax3.set_ylabel('D1 (N·s/m)')
        ax3.grid(True)
        ax3.set_title('直接阻尼 D1 vs 密封间隙')

        ax4.plot(clearance_mm, D2_list, 'm-o', linewidth=2, markersize=6)
        ax4.set_xlabel('密封间隙 (mm)')
        ax4.set_ylabel('D2 (N·s/m)')
        ax4.grid(True)
        ax4.set_title('交叉阻尼 D2 vs 密封间隙')

        plt.tight_layout()
        plt.savefig('clearance_effect.png', dpi=300, bbox_inches='tight')
        plt.show()

    def analyze_length_effect(self, length_values, omega=340):
        """分析密封长度对系统特性的影响 (图13)"""
        K1_list, K2_list, D1_list, D2_list = [], [], [], []

        original_L = self.L

        for length in length_values:
            self.L = length
            eM = 0.1  # 假设偏心率
            K, D, tau_M, mf_eff = self.calculate_seal_coefficients(omega, eM)

            # 计算系数
            K1 = self.Ke + K - tau_M**2 * omega**2 * mf_eff / self.mf * omega**2
            K2 = tau_M * D / self.mf * omega
            D1 = self.De + D / (self.mf * omega)
            D2 = 2 * tau_M * mf_eff / self.mf

            K1_list.append(K1)
            K2_list.append(K2)
            D1_list.append(D1)
            D2_list.append(D2)

        self.L = original_L  # 恢复原值

        # 绘图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))

        length_mm = np.array(length_values) * 1000  # 转换为mm

        ax1.plot(length_mm, K1_list, 'b-o', linewidth=2, markersize=6)
        ax1.set_xlabel('密封长度 (mm)')
        ax1.set_ylabel('K1 (N/m)')
        ax1.grid(True)
        ax1.set_title('直接刚度 K1 vs 密封长度')

        ax2.plot(length_mm, K2_list, 'r-o', linewidth=2, markersize=6)
        ax2.set_xlabel('密封长度 (mm)')
        ax2.set_ylabel('K2 (N/m)')
        ax2.grid(True)
        ax2.set_title('交叉刚度 K2 vs 密封长度')

        ax3.plot(length_mm, D1_list, 'g-o', linewidth=2, markersize=6)
        ax3.set_xlabel('密封长度 (mm)')
        ax3.set_ylabel('D1 (N·s/m)')
        ax3.grid(True)
        ax3.set_title('直接阻尼 D1 vs 密封长度')

        ax4.plot(length_mm, D2_list, 'm-o', linewidth=2, markersize=6)
        ax4.set_xlabel('密封长度 (mm)')
        ax4.set_ylabel('D2 (N·s/m)')
        ax4.grid(True)
        ax4.set_title('交叉阻尼 D2 vs 密封长度')

        plt.tight_layout()
        plt.savefig('length_effect.png', dpi=300, bbox_inches='tight')
        plt.show()

    def frequency_analysis(self, omega, delta_p_values=None, t_span=5.0):
        """频谱分析 (图8)"""
        if delta_p_values is None:
            delta_p_values = [0.2e6, 0.6e6]

        original_delta_p = self.delta_p

        fig, axes = plt.subplots(len(delta_p_values), 1, figsize=(12, 8))
        if len(delta_p_values) == 1:
            axes = [axes]

        for idx, dp in enumerate(delta_p_values):
            self.delta_p = dp

            # 初始条件
            initial_conditions = [0.0001, 0.0001, 0, 0]

            # 求解系统
            t, solution = self.solve_system(omega, t_span, initial_conditions)
            x, y, vx, vy = solution.T

            # 取稳态部分
            steady_start = int(len(t) * 0.6)
            x_steady = x[steady_start:]

            # FFT分析
            dt = t[1] - t[0]
            fs = 1 / dt
            n = len(x_steady)

            # 计算FFT
            fft_x = np.fft.fft(x_steady)
            freqs = np.fft.fftfreq(n, dt)

            # 只取正频率部分
            positive_freqs = freqs[:n//2]
            magnitude = np.abs(fft_x[:n//2]) * 2 / n

            # 绘制频谱
            axes[idx].semilogy(positive_freqs, magnitude, 'b-', linewidth=1.5)
            axes[idx].set_xlabel('频率 (Hz)')
            axes[idx].set_ylabel('幅值')
            axes[idx].grid(True)
            axes[idx].set_title(f'频谱分析 (Δp = {dp/1e6:.1f} MPa, ω = {omega} rad/s)')
            axes[idx].set_xlim(0, 100)  # 限制频率范围

            # 标记转频
            fr = omega / (2 * np.pi)
            axes[idx].axvline(fr, color='r', linestyle='--', label=f'转频 fr = {fr:.1f} Hz')
            axes[idx].legend()

        self.delta_p = original_delta_p

        plt.tight_layout()
        plt.savefig('frequency_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()


def main():
    """主程序 - 复现论文中的所有分析"""
    print("=" * 60)
    print("迷宫密封-转子系统非线性动力学分析")
    print("考虑联轴器内摩擦的影响")
    print("=" * 60)

    # 创建系统实例
    system = LabyrinthSealRotorSystem()

    print("\n系统参数:")
    print(f"密封半径: {system.Rs1*1000:.1f} mm")
    print(f"径向间隙: {system.Cr*1000:.2f} mm")
    print(f"密封长度: {system.L*1000:.1f} mm")
    print(f"轴向压降: {system.delta_p/1e6:.1f} MPa")
    print(f"转子质量: {system.m:.1f} kg")
    print(f"转子刚度: {system.Ke/1e6:.1f} MN/m")

    # 1. 动态特性系数随转速变化分析 (图4)
    print("\n1. 分析动态特性系数随转速变化...")
    omega_range = np.linspace(100, 600, 50)
    system.plot_dynamic_coefficients_vs_speed(omega_range)

    # 2. 轴向压降对系统特性的影响 (图5)
    print("\n2. 分析轴向压降对系统特性的影响...")
    pressure_drops = np.linspace(0.2e6, 0.8e6, 7)
    system.analyze_pressure_drop_effect(pressure_drops)

    # 3. 密封力时间历程分析 (图6)
    print("\n3. 分析密封力时间历程...")
    system.analyze_seal_force_time_history()

    # 4. 分岔图分析 (图7)
    print("\n4. 绘制分岔图...")
    omega_range_bifurcation = np.linspace(200, 500, 30)
    system.plot_bifurcation_diagram(omega_range_bifurcation)

    # 5. 频谱分析 (图8)
    print("\n5. 进行频谱分析...")
    system.frequency_analysis(omega=340)

    # 6. 轨道图和时间历程 (图9)
    print("\n6. 绘制轨道图和时间历程...")
    omega_values = [340, 580, 760, 1100]
    system.plot_orbit_and_time_history(omega_values)

    # 7. 密封间隙影响分析 (图10)
    print("\n7. 分析密封间隙对系统特性的影响...")
    clearance_values = np.linspace(0.6e-3, 1.2e-3, 7)  # 0.6-1.2 mm
    system.analyze_clearance_effect(clearance_values)

    # 8. 密封长度影响分析 (图13)
    print("\n8. 分析密封长度对系统特性的影响...")
    length_values = np.linspace(0.03, 0.07, 5)  # 30-70 mm
    system.analyze_length_effect(length_values)

    print("\n=" * 60)
    print("所有分析完成！结果图片已保存到当前目录。")
    print("=" * 60)


def demo_single_case():
    """演示单个案例的详细分析"""
    print("\n" + "=" * 50)
    print("单个案例详细分析演示")
    print("=" * 50)

    system = LabyrinthSealRotorSystem()

    # 设置参数
    omega = 340  # rad/s
    t_span = 3.0  # 时间跨度
    initial_conditions = [0.0001, 0.0001, 0, 0]  # 初始条件

    print(f"\n分析参数:")
    print(f"转速: {omega} rad/s ({omega/(2*np.pi):.1f} Hz)")
    print(f"轴向压降: {system.delta_p/1e6:.1f} MPa")
    print(f"密封间隙: {system.Cr*1000:.2f} mm")
    print(f"密封长度: {system.L*1000:.1f} mm")

    # 求解系统
    print("\n正在求解系统动力学方程...")
    t, solution = system.solve_system(omega, t_span, initial_conditions)
    x, y, vx, vy = solution.T

    # 计算一些关键指标
    displacement = np.sqrt(x**2 + y**2)
    max_displacement = np.max(displacement)
    rms_displacement = np.sqrt(np.mean(displacement**2))

    print(f"\n结果分析:")
    print(f"最大位移: {max_displacement*1000:.4f} mm")
    print(f"RMS位移: {rms_displacement*1000:.4f} mm")

    # 绘制结果
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

    # 时间历程
    ax1.plot(t, x*1000, 'b-', linewidth=1.5, label='x')
    ax1.plot(t, y*1000, 'r-', linewidth=1.5, label='y')
    ax1.set_xlabel('时间 (s)')
    ax1.set_ylabel('位移 (mm)')
    ax1.grid(True)
    ax1.legend()
    ax1.set_title('位移时间历程')

    # 轨道图
    ax2.plot(x*1000, y*1000, 'b-', linewidth=1.5)
    ax2.set_xlabel('x (mm)')
    ax2.set_ylabel('y (mm)')
    ax2.grid(True)
    ax2.set_title('轨道图')
    ax2.axis('equal')

    # 速度时间历程
    ax3.plot(t, vx, 'g-', linewidth=1.5, label='vx')
    ax3.plot(t, vy, 'm-', linewidth=1.5, label='vy')
    ax3.set_xlabel('时间 (s)')
    ax3.set_ylabel('速度 (m/s)')
    ax3.grid(True)
    ax3.legend()
    ax3.set_title('速度时间历程')

    # 位移幅值
    ax4.plot(t, displacement*1000, 'k-', linewidth=1.5)
    ax4.set_xlabel('时间 (s)')
    ax4.set_ylabel('位移幅值 (mm)')
    ax4.grid(True)
    ax4.set_title('位移幅值')

    plt.tight_layout()
    plt.savefig('single_case_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 计算密封力
    print("\n计算密封力...")
    Fsx_list, Fsy_list = [], []
    for i in range(len(t)):
        ax_temp, ay_temp = 0, 0  # 简化
        Fsx, Fsy = system.calculate_seal_force(x[i], y[i], vx[i], vy[i], ax_temp, ay_temp, omega, t[i])
        Fsx_list.append(Fsx)
        Fsy_list.append(Fsy)

    Fsx_array = np.array(Fsx_list)
    Fsy_array = np.array(Fsy_list)

    print(f"最大水平密封力: {np.max(np.abs(Fsx_array)):.2f} N")
    print(f"最大垂直密封力: {np.max(np.abs(Fsy_array)):.2f} N")

    # 绘制密封力
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

    ax1.plot(t, Fsx_array, 'b-', linewidth=1.5)
    ax1.set_xlabel('时间 (s)')
    ax1.set_ylabel('水平密封力 Fsx (N)')
    ax1.grid(True)
    ax1.set_title('水平密封力时间历程')

    ax2.plot(t, Fsy_array, 'r-', linewidth=1.5)
    ax2.set_xlabel('时间 (s)')
    ax2.set_ylabel('垂直密封力 Fsy (N)')
    ax2.grid(True)
    ax2.set_title('垂直密封力时间历程')

    plt.tight_layout()
    plt.savefig('seal_force_single_case.png', dpi=300, bbox_inches='tight')
    plt.show()


if __name__ == "__main__":
    # 选择运行模式
    print("请选择运行模式:")
    print("1. 完整分析 (复现论文所有图表)")
    print("2. 单个案例演示")
    print("3. 退出")

    choice = input("\n请输入选择 (1/2/3): ").strip()

    if choice == "1":
        main()
    elif choice == "2":
        demo_single_case()
    elif choice == "3":
        print("程序退出。")
    else:
        print("无效选择，运行单个案例演示...")
        demo_single_case()
