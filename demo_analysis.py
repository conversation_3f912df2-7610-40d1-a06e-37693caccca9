"""
自动演示迷宫密封-转子系统分析
"""

import numpy as np
import matplotlib.pyplot as plt
from labyrinth_seal_rotor_system import LabyrinthSealRotorSystem

def run_demo():
    """运行演示分析"""
    print("=" * 60)
    print("迷宫密封-转子系统非线性动力学分析演示")
    print("考虑联轴器内摩擦的影响")
    print("=" * 60)
    
    # 创建系统实例
    system = LabyrinthSealRotorSystem()
    
    print("\n系统参数:")
    print(f"密封半径: {system.Rs1*1000:.1f} mm")
    print(f"径向间隙: {system.Cr*1000:.2f} mm")
    print(f"密封长度: {system.L*1000:.1f} mm")
    print(f"轴向压降: {system.delta_p/1e6:.1f} MPa")
    print(f"转子质量: {system.m:.1f} kg")
    print(f"转子刚度: {system.Ke/1e6:.1f} MN/m")
    
    # 1. 动态特性系数随转速变化分析
    print("\n1. 分析动态特性系数随转速变化...")
    omega_range = np.linspace(100, 600, 20)
    system.plot_dynamic_coefficients_vs_speed(omega_range)
    
    # 2. 轴向压降对系统特性的影响
    print("\n2. 分析轴向压降对系统特性的影响...")
    pressure_drops = np.linspace(0.2e6, 0.8e6, 5)
    system.analyze_pressure_drop_effect(pressure_drops)
    
    # 3. 密封力时间历程分析
    print("\n3. 分析密封力时间历程...")
    system.analyze_seal_force_time_history(omega=340, t_span=2.0)
    
    # 4. 分岔图分析
    print("\n4. 绘制分岔图...")
    omega_range_bifurcation = np.linspace(200, 500, 15)
    system.plot_bifurcation_diagram(omega_range_bifurcation)
    
    # 5. 频谱分析
    print("\n5. 进行频谱分析...")
    system.frequency_analysis(omega=340, t_span=3.0)
    
    # 6. 轨道图和时间历程
    print("\n6. 绘制轨道图和时间历程...")
    omega_values = [340, 580, 760]
    system.plot_orbit_and_time_history(omega_values, t_span=1.5)
    
    # 7. 密封间隙影响分析
    print("\n7. 分析密封间隙对系统特性的影响...")
    clearance_values = np.linspace(0.6e-3, 1.2e-3, 5)  # 0.6-1.2 mm
    system.analyze_clearance_effect(clearance_values)
    
    # 8. 密封长度影响分析
    print("\n8. 分析密封长度对系统特性的影响...")
    length_values = np.linspace(0.03, 0.07, 5)  # 30-70 mm
    system.analyze_length_effect(length_values)
    
    print("\n=" * 60)
    print("演示分析完成！结果图片已保存到当前目录。")
    print("=" * 60)

def single_case_demo():
    """单个案例详细演示"""
    print("\n" + "=" * 50)
    print("单个案例详细分析")
    print("=" * 50)
    
    system = LabyrinthSealRotorSystem()
    
    # 设置参数
    omega = 340  # rad/s
    t_span = 2.0  # 时间跨度
    initial_conditions = [0.0001, 0.0001, 0, 0]  # 初始条件
    
    print(f"\n分析参数:")
    print(f"转速: {omega} rad/s ({omega/(2*np.pi):.1f} Hz)")
    print(f"轴向压降: {system.delta_p/1e6:.1f} MPa")
    print(f"密封间隙: {system.Cr*1000:.2f} mm")
    print(f"密封长度: {system.L*1000:.1f} mm")
    
    # 求解系统
    print("\n正在求解系统动力学方程...")
    t, solution = system.solve_system(omega, t_span, initial_conditions)
    x, y, vx, vy = solution.T
    
    # 计算关键指标
    displacement = np.sqrt(x**2 + y**2)
    max_displacement = np.max(displacement)
    rms_displacement = np.sqrt(np.mean(displacement**2))
    
    print(f"\n结果分析:")
    print(f"最大位移: {max_displacement*1000:.4f} mm")
    print(f"RMS位移: {rms_displacement*1000:.4f} mm")
    
    # 绘制详细结果
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    
    # 时间历程
    axes[0,0].plot(t, x*1000, 'b-', linewidth=1.5, label='x')
    axes[0,0].plot(t, y*1000, 'r-', linewidth=1.5, label='y')
    axes[0,0].set_xlabel('时间 (s)')
    axes[0,0].set_ylabel('位移 (mm)')
    axes[0,0].grid(True)
    axes[0,0].legend()
    axes[0,0].set_title('位移时间历程')
    
    # 轨道图
    axes[0,1].plot(x*1000, y*1000, 'b-', linewidth=1.5)
    axes[0,1].set_xlabel('x (mm)')
    axes[0,1].set_ylabel('y (mm)')
    axes[0,1].grid(True)
    axes[0,1].set_title('轨道图')
    axes[0,1].axis('equal')
    
    # 速度时间历程
    axes[0,2].plot(t, vx, 'g-', linewidth=1.5, label='vx')
    axes[0,2].plot(t, vy, 'm-', linewidth=1.5, label='vy')
    axes[0,2].set_xlabel('时间 (s)')
    axes[0,2].set_ylabel('速度 (m/s)')
    axes[0,2].grid(True)
    axes[0,2].legend()
    axes[0,2].set_title('速度时间历程')
    
    # 位移幅值
    axes[1,0].plot(t, displacement*1000, 'k-', linewidth=1.5)
    axes[1,0].set_xlabel('时间 (s)')
    axes[1,0].set_ylabel('位移幅值 (mm)')
    axes[1,0].grid(True)
    axes[1,0].set_title('位移幅值')
    
    # 相图 (x vs vx)
    axes[1,1].plot(x*1000, vx, 'b-', linewidth=1.5)
    axes[1,1].set_xlabel('x (mm)')
    axes[1,1].set_ylabel('vx (m/s)')
    axes[1,1].grid(True)
    axes[1,1].set_title('相图 (x-vx)')
    
    # 频谱分析
    dt = t[1] - t[0]
    n = len(x)
    fft_x = np.fft.fft(x)
    freqs = np.fft.fftfreq(n, dt)
    positive_freqs = freqs[:n//2]
    magnitude = np.abs(fft_x[:n//2]) * 2 / n
    
    axes[1,2].semilogy(positive_freqs, magnitude, 'b-', linewidth=1.5)
    axes[1,2].set_xlabel('频率 (Hz)')
    axes[1,2].set_ylabel('幅值')
    axes[1,2].grid(True)
    axes[1,2].set_title('频谱分析')
    axes[1,2].set_xlim(0, 100)
    
    # 标记转频
    fr = omega / (2 * np.pi)
    axes[1,2].axvline(fr, color='r', linestyle='--', label=f'转频 {fr:.1f} Hz')
    axes[1,2].legend()
    
    plt.tight_layout()
    plt.savefig('detailed_single_case_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 计算和显示密封力
    print("\n计算密封力...")
    Fsx_list, Fsy_list = [], []
    for i in range(0, len(t), 10):  # 每10个点取一个，减少计算量
        ax_temp, ay_temp = 0, 0
        Fsx, Fsy = system.calculate_seal_force(x[i], y[i], vx[i], vy[i], ax_temp, ay_temp, omega, t[i])
        Fsx_list.append(Fsx)
        Fsy_list.append(Fsy)
    
    t_reduced = t[::10]
    Fsx_array = np.array(Fsx_list)
    Fsy_array = np.array(Fsy_list)
    
    print(f"最大水平密封力: {np.max(np.abs(Fsx_array)):.2f} N")
    print(f"最大垂直密封力: {np.max(np.abs(Fsy_array)):.2f} N")
    
    # 绘制密封力
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    ax1.plot(t_reduced, Fsx_array, 'b-', linewidth=1.5)
    ax1.set_xlabel('时间 (s)')
    ax1.set_ylabel('水平密封力 Fsx (N)')
    ax1.grid(True)
    ax1.set_title('水平密封力时间历程')
    
    ax2.plot(t_reduced, Fsy_array, 'r-', linewidth=1.5)
    ax2.set_xlabel('时间 (s)')
    ax2.set_ylabel('垂直密封力 Fsy (N)')
    ax2.grid(True)
    ax2.set_title('垂直密封力时间历程')
    
    plt.tight_layout()
    plt.savefig('detailed_seal_force_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("选择演示模式:")
    print("1. 完整分析演示")
    print("2. 单个案例详细分析")
    
    try:
        choice = "2"  # 默认选择单个案例演示
        
        if choice == "1":
            run_demo()
        else:
            single_case_demo()
            
    except KeyboardInterrupt:
        print("\n用户中断程序。")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        import traceback
        traceback.print_exc()
