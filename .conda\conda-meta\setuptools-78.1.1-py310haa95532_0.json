{"build": "py310haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["python >=3.10,<3.11.0a0"], "extracted_package_dir": "C:\\Users\\<USER>\\anaconda3\\pkgs\\setuptools-78.1.1-py310haa95532_0", "features": "", "files": ["Lib/site-packages/_distutils_hack/__init__.py", "Lib/site-packages/_distutils_hack/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/_distutils_hack/__pycache__/override.cpython-310.pyc", "Lib/site-packages/_distutils_hack/override.py", "Lib/site-packages/distutils-precedence.pth", "Lib/site-packages/pkg_resources/__init__.py", "Lib/site-packages/pkg_resources/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pkg_resources/api_tests.txt", "Lib/site-packages/pkg_resources/py.typed", "Lib/site-packages/pkg_resources/tests/__init__.py", "Lib/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-310.pyc", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "Lib/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "Lib/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "Lib/site-packages/pkg_resources/tests/test_find_distributions.py", "Lib/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "Lib/site-packages/pkg_resources/tests/test_markers.py", "Lib/site-packages/pkg_resources/tests/test_pkg_resources.py", "Lib/site-packages/pkg_resources/tests/test_resources.py", "Lib/site-packages/pkg_resources/tests/test_working_set.py", "Lib/site-packages/setuptools-78.1.1-py3.10.egg-info/PKG-INFO", "Lib/site-packages/setuptools-78.1.1-py3.10.egg-info/SOURCES.txt", "Lib/site-packages/setuptools-78.1.1-py3.10.egg-info/dependency_links.txt", "Lib/site-packages/setuptools-78.1.1-py3.10.egg-info/entry_points.txt", "Lib/site-packages/setuptools-78.1.1-py3.10.egg-info/requires.txt", "Lib/site-packages/setuptools-78.1.1-py3.10.egg-info/top_level.txt", "Lib/site-packages/setuptools/__init__.py", "Lib/site-packages/setuptools/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_core_metadata.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_entry_points.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_imp.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_importlib.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_itertools.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_normalization.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_path.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_reqs.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_shutil.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/_static.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/archive_util.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/build_meta.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/depends.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/discovery.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/dist.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/errors.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/extension.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/glob.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/installer.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/launch.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/logging.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/modified.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/monkey.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/msvc.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/namespaces.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/package_index.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/sandbox.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/unicode_utils.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/version.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/warnings.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/wheel.cpython-310.pyc", "Lib/site-packages/setuptools/__pycache__/windows_support.cpython-310.pyc", "Lib/site-packages/setuptools/_core_metadata.py", "Lib/site-packages/setuptools/_distutils/__init__.py", "Lib/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_log.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/core.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/debug.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/dist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/errors.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/extension.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/log.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/version.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/_log.py", "Lib/site-packages/setuptools/_distutils/_macos_compat.py", "Lib/site-packages/setuptools/_distutils/_modified.py", "Lib/site-packages/setuptools/_distutils/_msvccompiler.py", "Lib/site-packages/setuptools/_distutils/archive_util.py", "Lib/site-packages/setuptools/_distutils/ccompiler.py", "Lib/site-packages/setuptools/_distutils/cmd.py", "Lib/site-packages/setuptools/_distutils/command/__init__.py", "Lib/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/command/_framework_compat.py", "Lib/site-packages/setuptools/_distutils/command/bdist.py", "Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py", "Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py", "Lib/site-packages/setuptools/_distutils/command/build.py", "Lib/site-packages/setuptools/_distutils/command/build_clib.py", "Lib/site-packages/setuptools/_distutils/command/build_ext.py", "Lib/site-packages/setuptools/_distutils/command/build_py.py", "Lib/site-packages/setuptools/_distutils/command/build_scripts.py", "Lib/site-packages/setuptools/_distutils/command/check.py", "Lib/site-packages/setuptools/_distutils/command/clean.py", "Lib/site-packages/setuptools/_distutils/command/config.py", "Lib/site-packages/setuptools/_distutils/command/install.py", "Lib/site-packages/setuptools/_distutils/command/install_data.py", "Lib/site-packages/setuptools/_distutils/command/install_egg_info.py", "Lib/site-packages/setuptools/_distutils/command/install_headers.py", "Lib/site-packages/setuptools/_distutils/command/install_lib.py", "Lib/site-packages/setuptools/_distutils/command/install_scripts.py", "Lib/site-packages/setuptools/_distutils/command/sdist.py", "Lib/site-packages/setuptools/_distutils/compat/__init__.py", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/numpy.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compat/numpy.py", "Lib/site-packages/setuptools/_distutils/compat/py39.py", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/base.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/cygwin.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/errors.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/msvc.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/unix.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/zos.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/base.py", "Lib/site-packages/setuptools/_distutils/compilers/C/cygwin.py", "Lib/site-packages/setuptools/_distutils/compilers/C/errors.py", "Lib/site-packages/setuptools/_distutils/compilers/C/msvc.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_base.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_cygwin.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_mingw.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_msvc.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_unix.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_base.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_cygwin.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_mingw.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_msvc.py", "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_unix.py", "Lib/site-packages/setuptools/_distutils/compilers/C/unix.py", "Lib/site-packages/setuptools/_distutils/compilers/C/zos.py", "Lib/site-packages/setuptools/_distutils/core.py", "Lib/site-packages/setuptools/_distutils/cygwinccompiler.py", "Lib/site-packages/setuptools/_distutils/debug.py", "Lib/site-packages/setuptools/_distutils/dep_util.py", "Lib/site-packages/setuptools/_distutils/dir_util.py", "Lib/site-packages/setuptools/_distutils/dist.py", "Lib/site-packages/setuptools/_distutils/errors.py", "Lib/site-packages/setuptools/_distutils/extension.py", "Lib/site-packages/setuptools/_distutils/fancy_getopt.py", "Lib/site-packages/setuptools/_distutils/file_util.py", "Lib/site-packages/setuptools/_distutils/filelist.py", "Lib/site-packages/setuptools/_distutils/log.py", "Lib/site-packages/setuptools/_distutils/spawn.py", "Lib/site-packages/setuptools/_distutils/sysconfig.py", "Lib/site-packages/setuptools/_distutils/tests/__init__.py", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/__init__.py", "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-310.pyc", "Lib/site-packages/setuptools/_distutils/tests/compat/py39.py", "Lib/site-packages/setuptools/_distutils/tests/support.py", "Lib/site-packages/setuptools/_distutils/tests/test_archive_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "Lib/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "Lib/site-packages/setuptools/_distutils/tests/test_build.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_clib.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_ext.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_py.py", "Lib/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "Lib/site-packages/setuptools/_distutils/tests/test_check.py", "Lib/site-packages/setuptools/_distutils/tests/test_clean.py", "Lib/site-packages/setuptools/_distutils/tests/test_cmd.py", "Lib/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "Lib/site-packages/setuptools/_distutils/tests/test_core.py", "Lib/site-packages/setuptools/_distutils/tests/test_dir_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_dist.py", "Lib/site-packages/setuptools/_distutils/tests/test_extension.py", "Lib/site-packages/setuptools/_distutils/tests/test_file_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_filelist.py", "Lib/site-packages/setuptools/_distutils/tests/test_install.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_data.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_headers.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_lib.py", "Lib/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "Lib/site-packages/setuptools/_distutils/tests/test_log.py", "Lib/site-packages/setuptools/_distutils/tests/test_modified.py", "Lib/site-packages/setuptools/_distutils/tests/test_sdist.py", "Lib/site-packages/setuptools/_distutils/tests/test_spawn.py", "Lib/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "Lib/site-packages/setuptools/_distutils/tests/test_text_file.py", "Lib/site-packages/setuptools/_distutils/tests/test_util.py", "Lib/site-packages/setuptools/_distutils/tests/test_version.py", "Lib/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "Lib/site-packages/setuptools/_distutils/tests/unix_compat.py", "Lib/site-packages/setuptools/_distutils/text_file.py", "Lib/site-packages/setuptools/_distutils/unixccompiler.py", "Lib/site-packages/setuptools/_distutils/util.py", "Lib/site-packages/setuptools/_distutils/version.py", "Lib/site-packages/setuptools/_distutils/versionpredicate.py", "Lib/site-packages/setuptools/_distutils/zosccompiler.py", "Lib/site-packages/setuptools/_entry_points.py", "Lib/site-packages/setuptools/_imp.py", "Lib/site-packages/setuptools/_importlib.py", "Lib/site-packages/setuptools/_itertools.py", "Lib/site-packages/setuptools/_normalization.py", "Lib/site-packages/setuptools/_path.py", "Lib/site-packages/setuptools/_reqs.py", "Lib/site-packages/setuptools/_shutil.py", "Lib/site-packages/setuptools/_static.py", "Lib/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/autocommand/__init__.py", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/autocommand/autoasync.py", "Lib/site-packages/setuptools/_vendor/autocommand/autocommand.py", "Lib/site-packages/setuptools/_vendor/autocommand/automain.py", "Lib/site-packages/setuptools/_vendor/autocommand/autoparse.py", "Lib/site-packages/setuptools/_vendor/autocommand/errors.py", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/backports/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "Lib/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/inflect/__init__.py", "Lib/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/inflect/compat/py38.py", "Lib/site-packages/setuptools/_vendor/inflect/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco/context.py", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "Lib/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "Lib/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "Lib/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "Lib/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py", "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/more_itertools/more.py", "Lib/site-packages/setuptools/_vendor/more_itertools/more.pyi", "Lib/site-packages/setuptools/_vendor/more_itertools/py.typed", "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py", "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/packaging/__init__.py", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/_elffile.py", "Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py", "Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py", "Lib/site-packages/setuptools/_vendor/packaging/_parser.py", "Lib/site-packages/setuptools/_vendor/packaging/_structures.py", "Lib/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "Lib/site-packages/setuptools/_vendor/packaging/markers.py", "Lib/site-packages/setuptools/_vendor/packaging/metadata.py", "Lib/site-packages/setuptools/_vendor/packaging/py.typed", "Lib/site-packages/setuptools/_vendor/packaging/requirements.py", "Lib/site-packages/setuptools/_vendor/packaging/specifiers.py", "Lib/site-packages/setuptools/_vendor/packaging/tags.py", "Lib/site-packages/setuptools/_vendor/packaging/utils.py", "Lib/site-packages/setuptools/_vendor/packaging/version.py", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "Lib/site-packages/setuptools/_vendor/platformdirs/__init__.py", "Lib/site-packages/setuptools/_vendor/platformdirs/__main__.py", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/platformdirs/android.py", "Lib/site-packages/setuptools/_vendor/platformdirs/api.py", "Lib/site-packages/setuptools/_vendor/platformdirs/macos.py", "Lib/site-packages/setuptools/_vendor/platformdirs/py.typed", "Lib/site-packages/setuptools/_vendor/platformdirs/unix.py", "Lib/site-packages/setuptools/_vendor/platformdirs/version.py", "Lib/site-packages/setuptools/_vendor/platformdirs/windows.py", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/tomli/__init__.py", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/tomli/_parser.py", "Lib/site-packages/setuptools/_vendor/tomli/_re.py", "Lib/site-packages/setuptools/_vendor/tomli/_types.py", "Lib/site-packages/setuptools/_vendor/tomli/py.typed", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/typeguard/__init__.py", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/typeguard/_checkers.py", "Lib/site-packages/setuptools/_vendor/typeguard/_config.py", "Lib/site-packages/setuptools/_vendor/typeguard/_decorators.py", "Lib/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "Lib/site-packages/setuptools/_vendor/typeguard/_functions.py", "Lib/site-packages/setuptools/_vendor/typeguard/_importhook.py", "Lib/site-packages/setuptools/_vendor/typeguard/_memo.py", "Lib/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "Lib/site-packages/setuptools/_vendor/typeguard/_suppression.py", "Lib/site-packages/setuptools/_vendor/typeguard/_transformer.py", "Lib/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "Lib/site-packages/setuptools/_vendor/typeguard/_utils.py", "Lib/site-packages/setuptools/_vendor/typeguard/py.typed", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/typing_extensions.py", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt", "Lib/site-packages/setuptools/_vendor/wheel/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/__main__.py", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_bdist_wheel.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/_bdist_wheel.py", "Lib/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "Lib/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/cli/convert.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/pack.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/tags.py", "Lib/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "Lib/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "Lib/site-packages/setuptools/_vendor/wheel/metadata.py", "Lib/site-packages/setuptools/_vendor/wheel/util.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.APACHE", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.BSD", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "Lib/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "Lib/site-packages/setuptools/_vendor/wheel/wheelfile.py", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "Lib/site-packages/setuptools/_vendor/zipp/__init__.py", "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-310.pyc", "Lib/site-packages/setuptools/_vendor/zipp/compat/py310.py", "Lib/site-packages/setuptools/_vendor/zipp/glob.py", "Lib/site-packages/setuptools/archive_util.py", "Lib/site-packages/setuptools/build_meta.py", "Lib/site-packages/setuptools/cli-32.exe", "Lib/site-packages/setuptools/cli-64.exe", "Lib/site-packages/setuptools/cli-arm64.exe", "Lib/site-packages/setuptools/cli.exe", "Lib/site-packages/setuptools/command/__init__.py", "Lib/site-packages/setuptools/command/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/alias.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/build.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_clib.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_ext.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/build_py.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/develop.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/dist_info.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/easy_install.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/egg_info.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/install.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_lib.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/install_scripts.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/rotate.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/saveopts.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/sdist.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/setopt.cpython-310.pyc", "Lib/site-packages/setuptools/command/__pycache__/test.cpython-310.pyc", "Lib/site-packages/setuptools/command/_requirestxt.py", "Lib/site-packages/setuptools/command/alias.py", "Lib/site-packages/setuptools/command/bdist_egg.py", "Lib/site-packages/setuptools/command/bdist_rpm.py", "Lib/site-packages/setuptools/command/bdist_wheel.py", "Lib/site-packages/setuptools/command/build.py", "Lib/site-packages/setuptools/command/build_clib.py", "Lib/site-packages/setuptools/command/build_ext.py", "Lib/site-packages/setuptools/command/build_py.py", "Lib/site-packages/setuptools/command/develop.py", "Lib/site-packages/setuptools/command/dist_info.py", "Lib/site-packages/setuptools/command/easy_install.py", "Lib/site-packages/setuptools/command/editable_wheel.py", "Lib/site-packages/setuptools/command/egg_info.py", "Lib/site-packages/setuptools/command/install.py", "Lib/site-packages/setuptools/command/install_egg_info.py", "Lib/site-packages/setuptools/command/install_lib.py", "Lib/site-packages/setuptools/command/install_scripts.py", "Lib/site-packages/setuptools/command/launcher manifest.xml", "Lib/site-packages/setuptools/command/rotate.py", "Lib/site-packages/setuptools/command/saveopts.py", "Lib/site-packages/setuptools/command/sdist.py", "Lib/site-packages/setuptools/command/setopt.py", "Lib/site-packages/setuptools/command/test.py", "Lib/site-packages/setuptools/compat/__init__.py", "Lib/site-packages/setuptools/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py310.cpython-310.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py311.cpython-310.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py312.cpython-310.pyc", "Lib/site-packages/setuptools/compat/__pycache__/py39.cpython-310.pyc", "Lib/site-packages/setuptools/compat/py310.py", "Lib/site-packages/setuptools/compat/py311.py", "Lib/site-packages/setuptools/compat/py312.py", "Lib/site-packages/setuptools/compat/py39.py", "Lib/site-packages/setuptools/config/NOTICE", "Lib/site-packages/setuptools/config/__init__.py", "Lib/site-packages/setuptools/config/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-310.pyc", "Lib/site-packages/setuptools/config/__pycache__/expand.cpython-310.pyc", "Lib/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-310.pyc", "Lib/site-packages/setuptools/config/__pycache__/setupcfg.cpython-310.pyc", "Lib/site-packages/setuptools/config/_apply_pyprojecttoml.py", "Lib/site-packages/setuptools/config/_validate_pyproject/NOTICE", "Lib/site-packages/setuptools/config/_validate_pyproject/__init__.py", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-310.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-310.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-310.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-310.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-310.pyc", "Lib/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "Lib/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "Lib/site-packages/setuptools/config/_validate_pyproject/formats.py", "Lib/site-packages/setuptools/config/distutils.schema.json", "Lib/site-packages/setuptools/config/expand.py", "Lib/site-packages/setuptools/config/pyprojecttoml.py", "Lib/site-packages/setuptools/config/setupcfg.py", "Lib/site-packages/setuptools/config/setuptools.schema.json", "Lib/site-packages/setuptools/depends.py", "Lib/site-packages/setuptools/discovery.py", "Lib/site-packages/setuptools/dist.py", "Lib/site-packages/setuptools/errors.py", "Lib/site-packages/setuptools/extension.py", "Lib/site-packages/setuptools/glob.py", "Lib/site-packages/setuptools/gui-32.exe", "Lib/site-packages/setuptools/gui-64.exe", "Lib/site-packages/setuptools/gui-arm64.exe", "Lib/site-packages/setuptools/gui.exe", "Lib/site-packages/setuptools/installer.py", "Lib/site-packages/setuptools/launch.py", "Lib/site-packages/setuptools/logging.py", "Lib/site-packages/setuptools/modified.py", "Lib/site-packages/setuptools/monkey.py", "Lib/site-packages/setuptools/msvc.py", "Lib/site-packages/setuptools/namespaces.py", "Lib/site-packages/setuptools/package_index.py", "Lib/site-packages/setuptools/sandbox.py", "Lib/site-packages/setuptools/script (dev).tmpl", "Lib/site-packages/setuptools/script.tmpl", "Lib/site-packages/setuptools/tests/__init__.py", "Lib/site-packages/setuptools/tests/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/contexts.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/environment.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/fixtures.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/namespaces.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/server.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_depends.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_develop.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_dist.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_extern.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_glob.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_logging.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/text.cpython-310.pyc", "Lib/site-packages/setuptools/tests/__pycache__/textwrap.cpython-310.pyc", "Lib/site-packages/setuptools/tests/compat/__init__.py", "Lib/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-310.pyc", "Lib/site-packages/setuptools/tests/compat/py39.py", "Lib/site-packages/setuptools/tests/config/__init__.py", "Lib/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/downloads/__init__.py", "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-310.pyc", "Lib/site-packages/setuptools/tests/config/downloads/preload.py", "Lib/site-packages/setuptools/tests/config/setupcfg_examples.txt", "Lib/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "Lib/site-packages/setuptools/tests/config/test_expand.py", "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "Lib/site-packages/setuptools/tests/config/test_setupcfg.py", "Lib/site-packages/setuptools/tests/contexts.py", "Lib/site-packages/setuptools/tests/environment.py", "Lib/site-packages/setuptools/tests/fixtures.py", "Lib/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "Lib/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "Lib/site-packages/setuptools/tests/integration/__init__.py", "Lib/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-310.pyc", "Lib/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-310.pyc", "Lib/site-packages/setuptools/tests/integration/helpers.py", "Lib/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "Lib/site-packages/setuptools/tests/mod_with_constant.py", "Lib/site-packages/setuptools/tests/namespaces.py", "Lib/site-packages/setuptools/tests/script-with-bom.py", "Lib/site-packages/setuptools/tests/server.py", "Lib/site-packages/setuptools/tests/test_archive_util.py", "Lib/site-packages/setuptools/tests/test_bdist_deprecations.py", "Lib/site-packages/setuptools/tests/test_bdist_egg.py", "Lib/site-packages/setuptools/tests/test_bdist_wheel.py", "Lib/site-packages/setuptools/tests/test_build.py", "Lib/site-packages/setuptools/tests/test_build_clib.py", "Lib/site-packages/setuptools/tests/test_build_ext.py", "Lib/site-packages/setuptools/tests/test_build_meta.py", "Lib/site-packages/setuptools/tests/test_build_py.py", "Lib/site-packages/setuptools/tests/test_config_discovery.py", "Lib/site-packages/setuptools/tests/test_core_metadata.py", "Lib/site-packages/setuptools/tests/test_depends.py", "Lib/site-packages/setuptools/tests/test_develop.py", "Lib/site-packages/setuptools/tests/test_dist.py", "Lib/site-packages/setuptools/tests/test_dist_info.py", "Lib/site-packages/setuptools/tests/test_distutils_adoption.py", "Lib/site-packages/setuptools/tests/test_easy_install.py", "Lib/site-packages/setuptools/tests/test_editable_install.py", "Lib/site-packages/setuptools/tests/test_egg_info.py", "Lib/site-packages/setuptools/tests/test_extern.py", "Lib/site-packages/setuptools/tests/test_find_packages.py", "Lib/site-packages/setuptools/tests/test_find_py_modules.py", "Lib/site-packages/setuptools/tests/test_glob.py", "Lib/site-packages/setuptools/tests/test_install_scripts.py", "Lib/site-packages/setuptools/tests/test_logging.py", "Lib/site-packages/setuptools/tests/test_manifest.py", "Lib/site-packages/setuptools/tests/test_namespaces.py", "Lib/site-packages/setuptools/tests/test_packageindex.py", "Lib/site-packages/setuptools/tests/test_sandbox.py", "Lib/site-packages/setuptools/tests/test_sdist.py", "Lib/site-packages/setuptools/tests/test_setopt.py", "Lib/site-packages/setuptools/tests/test_setuptools.py", "Lib/site-packages/setuptools/tests/test_shutil_wrapper.py", "Lib/site-packages/setuptools/tests/test_unicode_utils.py", "Lib/site-packages/setuptools/tests/test_virtualenv.py", "Lib/site-packages/setuptools/tests/test_warnings.py", "Lib/site-packages/setuptools/tests/test_wheel.py", "Lib/site-packages/setuptools/tests/test_windows_wrappers.py", "Lib/site-packages/setuptools/tests/text.py", "Lib/site-packages/setuptools/tests/textwrap.py", "Lib/site-packages/setuptools/unicode_utils.py", "Lib/site-packages/setuptools/version.py", "Lib/site-packages/setuptools/warnings.py", "Lib/site-packages/setuptools/wheel.py", "Lib/site-packages/setuptools/windows_support.py", ".nonadmin"], "fn": "setuptools-78.1.1-py310haa95532_0.conda", "legacy_bz2_md5": "f55be2922dbc5d3416cd1c82e7ccb7a9", "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\Users\\<USER>\\anaconda3\\pkgs\\setuptools-78.1.1-py310haa95532_0", "type": 1}, "md5": "954300d358ac0fde8822083634117f92", "name": "setuptools", "package_tarball_full_path": "C:\\Users\\<USER>\\anaconda3\\pkgs\\setuptools-78.1.1-py310haa95532_0.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/_distutils_hack/__init__.py", "path_type": "hardlink", "sha256": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "sha256_in_prefix": "df81e6bcba34ee3e3952f776551fb669143b9490fdd6c4caeb32609f97e985b4", "size_in_bytes": 6755}, {"_path": "Lib/site-packages/_distutils_hack/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "e1dbddc3f8be26d61652c4742f0a97375f425bd49d27e24ec56716f45ffa26db", "sha256_in_prefix": "e1dbddc3f8be26d61652c4742f0a97375f425bd49d27e24ec56716f45ffa26db", "size_in_bytes": 8195}, {"_path": "Lib/site-packages/_distutils_hack/__pycache__/override.cpython-310.pyc", "path_type": "hardlink", "sha256": "c02a279964bb3bdb40679ee5afc7ffc5238d51cce11ea3d7bfa06acdbd30c975", "sha256_in_prefix": "c02a279964bb3bdb40679ee5afc7ffc5238d51cce11ea3d7bfa06acdbd30c975", "size_in_bytes": 189}, {"_path": "Lib/site-packages/_distutils_hack/override.py", "path_type": "hardlink", "sha256": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "sha256_in_prefix": "12efecf8d17a5486780aa774b5b6c0e70b56932d8864f35df1eb7a18bb759b3a", "size_in_bytes": 44}, {"_path": "Lib/site-packages/distutils-precedence.pth", "path_type": "hardlink", "sha256": "ab406aa05439fe87070cde36180433193568432f11d04f0f762f374b8a9302f5", "sha256_in_prefix": "ab406aa05439fe87070cde36180433193568432f11d04f0f762f374b8a9302f5", "size_in_bytes": 152}, {"_path": "Lib/site-packages/pkg_resources/__init__.py", "path_type": "hardlink", "sha256": "fab87b5ce9d3c5d1ae0beffd140caee43eacf012f552c05e87152d8fb6be215a", "sha256_in_prefix": "fab87b5ce9d3c5d1ae0beffd140caee43eacf012f552c05e87152d8fb6be215a", "size_in_bytes": 126203}, {"_path": "Lib/site-packages/pkg_resources/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "739e5e9b5903f7c741028dd1c876dc99f299f37e7ec50f0ee76cc71755a5d4bd", "sha256_in_prefix": "739e5e9b5903f7c741028dd1c876dc99f299f37e7ec50f0ee76cc71755a5d4bd", "size_in_bytes": 115651}, {"_path": "Lib/site-packages/pkg_resources/api_tests.txt", "path_type": "hardlink", "sha256": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "sha256_in_prefix": "5c476fcb88a01c7aeadaa34734c1e795f3ba5d240a36a3b22c76e5e907297c02", "size_in_bytes": 12595}, {"_path": "Lib/site-packages/pkg_resources/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "19c47e4eb4284d2429250fd83a9cfbf178cfcdb2abc179f10210d5d1441db962", "sha256_in_prefix": "19c47e4eb4284d2429250fd83a9cfbf178cfcdb2abc179f10210d5d1441db962", "size_in_bytes": 142}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_find_distributions.cpython-310.pyc", "path_type": "hardlink", "sha256": "390f3499b95626f73773ae9e2bd93d85742190ac5eaa5526b2f7ae5bce03d4c5", "sha256_in_prefix": "390f3499b95626f73773ae9e2bd93d85742190ac5eaa5526b2f7ae5bce03d4c5", "size_in_bytes": 2343}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_integration_zope_interface.cpython-310.pyc", "path_type": "hardlink", "sha256": "1fc61e1e3e59d1903ffde1cf3f1559dc79f0c14b68e51a786a9faefd1b58edf7", "sha256_in_prefix": "1fc61e1e3e59d1903ffde1cf3f1559dc79f0c14b68e51a786a9faefd1b58edf7", "size_in_bytes": 1544}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_markers.cpython-310.pyc", "path_type": "hardlink", "sha256": "4439073d0ff630364640ecce1b1ec43315d300e7c42909b0102725b8b59a2ae9", "sha256_in_prefix": "4439073d0ff630364640ecce1b1ec43315d300e7c42909b0102725b8b59a2ae9", "size_in_bytes": 482}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_pkg_resources.cpython-310.pyc", "path_type": "hardlink", "sha256": "09b46d627082ff458500c48eecc5202bbe7431805ad06068c4a96c07de99f139", "sha256_in_prefix": "09b46d627082ff458500c48eecc5202bbe7431805ad06068c4a96c07de99f139", "size_in_bytes": 15256}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_resources.cpython-310.pyc", "path_type": "hardlink", "sha256": "27109463e7ade3b29e0825ed7e4624cc4ba7352c4e592daa435b89ae4793cccb", "sha256_in_prefix": "27109463e7ade3b29e0825ed7e4624cc4ba7352c4e592daa435b89ae4793cccb", "size_in_bytes": 28141}, {"_path": "Lib/site-packages/pkg_resources/tests/__pycache__/test_working_set.cpython-310.pyc", "path_type": "hardlink", "sha256": "bf056b3fc6ade7611a4621aab6f2f3d0fb0fe6324ea5e6d09810cdef1ba78b22", "sha256_in_prefix": "bf056b3fc6ade7611a4621aab6f2f3d0fb0fe6324ea5e6d09810cdef1ba78b22", "size_in_bytes": 8330}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/__pycache__/setup.cpython-310.pyc", "path_type": "hardlink", "sha256": "2043b7dcec568e91250d65efedc8e55514fb6832c765b580772d9a4906d42e48", "sha256_in_prefix": "2043b7dcec568e91250d65efedc8e55514fb6832c765b580772d9a4906d42e48", "size_in_bytes": 272}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.cfg", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py", "path_type": "hardlink", "sha256": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "sha256_in_prefix": "d55a1b84065b31beccf667e16ff344f0fc03b2fba4a162ecf5a5004b4a5885ef", "size_in_bytes": 105}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package-zip/my-test-package.zip", "path_type": "hardlink", "sha256": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "sha256_in_prefix": "01845c437f4655e3cf9cc4fc4e49cfd607431f22675e1b611129a90239f34822", "size_in_bytes": 1809}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/PKG-INFO", "path_type": "hardlink", "sha256": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "sha256_in_prefix": "26f5aff48a363c0b98c04130d9f056e1073962f75b92c729297d6498bceca079", "size_in_bytes": 187}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/SOURCES.txt", "path_type": "hardlink", "sha256": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "sha256_in_prefix": "e029641fc793a2f66b755ac916c56ec5d6cc105fbe941552b8aa270c03c4e497", "size_in_bytes": 208}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/top_level.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_unpacked-egg/my_test_package-1.0-py3.7.egg/EGG-INFO/zip-safe", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pkg_resources/tests/data/my-test-package_zipped-egg/my_test_package-1.0-py3.7.egg", "path_type": "hardlink", "sha256": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "sha256_in_prefix": "65394c1b18d11a2283364880d9cef98db407d93588b5e3f4d22ac5f60bdccdba", "size_in_bytes": 843}, {"_path": "Lib/site-packages/pkg_resources/tests/test_find_distributions.py", "path_type": "hardlink", "sha256": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "sha256_in_prefix": "53dd5ca2fe4bd423802162cdab75f2e29954eff327384d56b5732eea2576c1a3", "size_in_bytes": 1972}, {"_path": "Lib/site-packages/pkg_resources/tests/test_integration_zope_interface.py", "path_type": "hardlink", "sha256": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "sha256_in_prefix": "9f35682b9e7b29940dd15dc3210d6c55e6823a0b782a997e08e0c05ac3bba667", "size_in_bytes": 1652}, {"_path": "Lib/site-packages/pkg_resources/tests/test_markers.py", "path_type": "hardlink", "sha256": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "sha256_in_prefix": "d28aca83b50c0dfedf9ee350bd130e73e105f4400ffc94d09e4e26b4681b5b9d", "size_in_bytes": 241}, {"_path": "Lib/site-packages/pkg_resources/tests/test_pkg_resources.py", "path_type": "hardlink", "sha256": "e4cb786c94212c22fc8fc702e3a52fdf6369d977354d3c4b19ac087c44f9e459", "sha256_in_prefix": "e4cb786c94212c22fc8fc702e3a52fdf6369d977354d3c4b19ac087c44f9e459", "size_in_bytes": 17111}, {"_path": "Lib/site-packages/pkg_resources/tests/test_resources.py", "path_type": "hardlink", "sha256": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "sha256_in_prefix": "2b42ea300506a5143da546fd2b4bf223b19eb2fb6542f4c7d3be26f84d95425a", "size_in_bytes": 31252}, {"_path": "Lib/site-packages/pkg_resources/tests/test_working_set.py", "path_type": "hardlink", "sha256": "951b46256222c52c123126e31e047178911088b3115dccf7c7324bdaa2fb7976", "sha256_in_prefix": "951b46256222c52c123126e31e047178911088b3115dccf7c7324bdaa2fb7976", "size_in_bytes": 8602}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.10.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "b8abe3cc375d9768729904d2c5da6985061c34977a9507dfda1cd32bba8190c2", "sha256_in_prefix": "b8abe3cc375d9768729904d2c5da6985061c34977a9507dfda1cd32bba8190c2", "size_in_bytes": 6688}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.10.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "cfded7934597a4a900d7a0f110a44dff6486e4f7b4c8450b08ce803d31a09433", "sha256_in_prefix": "cfded7934597a4a900d7a0f110a44dff6486e4f7b4c8450b08ce803d31a09433", "size_in_bytes": 24294}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.10.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.10.egg-info/entry_points.txt", "path_type": "hardlink", "sha256": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "sha256_in_prefix": "ce482d8697ff15af4d544f69e85293dd793d0d1d5f680711538728820b15ee30", "size_in_bytes": 2449}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.10.egg-info/requires.txt", "path_type": "hardlink", "sha256": "cd186b9559d56bb22f53a7730a6ffdd8b0c2a342bbf97705c98b547553d584e3", "sha256_in_prefix": "cd186b9559d56bb22f53a7730a6ffdd8b0c2a342bbf97705c98b547553d584e3", "size_in_bytes": 1231}, {"_path": "Lib/site-packages/setuptools-78.1.1-py3.10.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "sha256_in_prefix": "77dc8bdfdbff5bbaa62830d21fab13e1b1348ff2ecd4cdcfd7ad4e1a076c9b88", "size_in_bytes": 41}, {"_path": "Lib/site-packages/setuptools/__init__.py", "path_type": "hardlink", "sha256": "010b0c791156cfd090f5a06d71291b0780e7f2ddb0f3af863eb8a4969a008dec", "sha256_in_prefix": "010b0c791156cfd090f5a06d71291b0780e7f2ddb0f3af863eb8a4969a008dec", "size_in_bytes": 10406}, {"_path": "Lib/site-packages/setuptools/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "93fef745a95cb192046b43255a9bdf5bb17b6ca6c2f307c6332ce78b642af7c8", "sha256_in_prefix": "93fef745a95cb192046b43255a9bdf5bb17b6ca6c2f307c6332ce78b642af7c8", "size_in_bytes": 11047}, {"_path": "Lib/site-packages/setuptools/__pycache__/_core_metadata.cpython-310.pyc", "path_type": "hardlink", "sha256": "eeab22e62820d54fedd3f2af5a3cefdb467f8f02e196261fde8051d8da2b44be", "sha256_in_prefix": "eeab22e62820d54fedd3f2af5a3cefdb467f8f02e196261fde8051d8da2b44be", "size_in_bytes": 9389}, {"_path": "Lib/site-packages/setuptools/__pycache__/_entry_points.cpython-310.pyc", "path_type": "hardlink", "sha256": "8c837e01e31282a792ec6bfd33fa0d9a1bc726be8e4eceef6862451ba191f723", "sha256_in_prefix": "8c837e01e31282a792ec6bfd33fa0d9a1bc726be8e4eceef6862451ba191f723", "size_in_bytes": 3218}, {"_path": "Lib/site-packages/setuptools/__pycache__/_imp.cpython-310.pyc", "path_type": "hardlink", "sha256": "167eb3c6c16ce2071c8bc53860b956442fb8bd0a908fc8804dbc5a45a2e5ac6c", "sha256_in_prefix": "167eb3c6c16ce2071c8bc53860b956442fb8bd0a908fc8804dbc5a45a2e5ac6c", "size_in_bytes": 2054}, {"_path": "Lib/site-packages/setuptools/__pycache__/_importlib.cpython-310.pyc", "path_type": "hardlink", "sha256": "5642fd64817c4797abf76c7b0684eaae64557a15e2c29070f2fc6c972eb53d2b", "sha256_in_prefix": "5642fd64817c4797abf76c7b0684eaae64557a15e2c29070f2fc6c972eb53d2b", "size_in_bytes": 310}, {"_path": "Lib/site-packages/setuptools/__pycache__/_itertools.cpython-310.pyc", "path_type": "hardlink", "sha256": "56531ddda28c570af3a4e254622184307e4547ef4a1b5839a96144b3b22d1c4d", "sha256_in_prefix": "56531ddda28c570af3a4e254622184307e4547ef4a1b5839a96144b3b22d1c4d", "size_in_bytes": 851}, {"_path": "Lib/site-packages/setuptools/__pycache__/_normalization.cpython-310.pyc", "path_type": "hardlink", "sha256": "e8d86d4ee2ad37d9678cb065c0dcb46d9fcb8fd9a5a08273f6d02975cd3d96c3", "sha256_in_prefix": "e8d86d4ee2ad37d9678cb065c0dcb46d9fcb8fd9a5a08273f6d02975cd3d96c3", "size_in_bytes": 5534}, {"_path": "Lib/site-packages/setuptools/__pycache__/_path.cpython-310.pyc", "path_type": "hardlink", "sha256": "7c5971793ea202b541ced806f873341ddf04988eab2523f5f9225ce52a13d12a", "sha256_in_prefix": "7c5971793ea202b541ced806f873341ddf04988eab2523f5f9225ce52a13d12a", "size_in_bytes": 2874}, {"_path": "Lib/site-packages/setuptools/__pycache__/_reqs.cpython-310.pyc", "path_type": "hardlink", "sha256": "02a68e34c9a72376f328f33ac593c7424839fa62094956d6c0fcf5999eb83442", "sha256_in_prefix": "02a68e34c9a72376f328f33ac593c7424839fa62094956d6c0fcf5999eb83442", "size_in_bytes": 1610}, {"_path": "Lib/site-packages/setuptools/__pycache__/_shutil.cpython-310.pyc", "path_type": "hardlink", "sha256": "fad3a7d29abc4508474ec32c5b6971ab49cde695b625435dbd94ab0ab7d9c18f", "sha256_in_prefix": "fad3a7d29abc4508474ec32c5b6971ab49cde695b625435dbd94ab0ab7d9c18f", "size_in_bytes": 1700}, {"_path": "Lib/site-packages/setuptools/__pycache__/_static.cpython-310.pyc", "path_type": "hardlink", "sha256": "3f135a41b4db41a55b418f38da84013257e37b52212bade104f31ef5ac3d050b", "sha256_in_prefix": "3f135a41b4db41a55b418f38da84013257e37b52212bade104f31ef5ac3d050b", "size_in_bytes": 5141}, {"_path": "Lib/site-packages/setuptools/__pycache__/archive_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "c094a495964e88667a9d4bfd8724149a615219e88de73756617124d7df76b042", "sha256_in_prefix": "c094a495964e88667a9d4bfd8724149a615219e88de73756617124d7df76b042", "size_in_bytes": 6170}, {"_path": "Lib/site-packages/setuptools/__pycache__/build_meta.cpython-310.pyc", "path_type": "hardlink", "sha256": "44bbbd8ff345d3aab25c6f56175c2e24e8fc576906b44952eb5960523d08d0dc", "sha256_in_prefix": "44bbbd8ff345d3aab25c6f56175c2e24e8fc576906b44952eb5960523d08d0dc", "size_in_bytes": 18344}, {"_path": "Lib/site-packages/setuptools/__pycache__/depends.cpython-310.pyc", "path_type": "hardlink", "sha256": "eb0c34dd0dc3a9e813924252c8fc4f0a6a48f256dfde3c94f905b11a0fd865a7", "sha256_in_prefix": "eb0c34dd0dc3a9e813924252c8fc4f0a6a48f256dfde3c94f905b11a0fd865a7", "size_in_bytes": 5430}, {"_path": "Lib/site-packages/setuptools/__pycache__/discovery.cpython-310.pyc", "path_type": "hardlink", "sha256": "57a90fdf42d4a6c1cfaec194001f31b5b15eca57ed112a3e6dd7ef3a7aec1948", "sha256_in_prefix": "57a90fdf42d4a6c1cfaec194001f31b5b15eca57ed112a3e6dd7ef3a7aec1948", "size_in_bytes": 21156}, {"_path": "Lib/site-packages/setuptools/__pycache__/dist.cpython-310.pyc", "path_type": "hardlink", "sha256": "3679b67a7f50a5ea9a0a4828d0766d7c9435dc397eac65e615390bf5d6f2d6b0", "sha256_in_prefix": "3679b67a7f50a5ea9a0a4828d0766d7c9435dc397eac65e615390bf5d6f2d6b0", "size_in_bytes": 37164}, {"_path": "Lib/site-packages/setuptools/__pycache__/errors.cpython-310.pyc", "path_type": "hardlink", "sha256": "5cc57251224c9bf7d10db481b6df69556d4ddbd45f59b5c2ff9785dd47db59a8", "sha256_in_prefix": "5cc57251224c9bf7d10db481b6df69556d4ddbd45f59b5c2ff9785dd47db59a8", "size_in_bytes": 2830}, {"_path": "Lib/site-packages/setuptools/__pycache__/extension.cpython-310.pyc", "path_type": "hardlink", "sha256": "5ca4d45208b446cf3e6e00bb331ddff41843de7661a30691d363bbe639627b88", "sha256_in_prefix": "5ca4d45208b446cf3e6e00bb331ddff41843de7661a30691d363bbe639627b88", "size_in_bytes": 6268}, {"_path": "Lib/site-packages/setuptools/__pycache__/glob.cpython-310.pyc", "path_type": "hardlink", "sha256": "f3a26b52bf46cb23fad9ef7f92f91bc3eb072400033619988f5fe801c42f2235", "sha256_in_prefix": "f3a26b52bf46cb23fad9ef7f92f91bc3eb072400033619988f5fe801c42f2235", "size_in_bytes": 5092}, {"_path": "Lib/site-packages/setuptools/__pycache__/installer.cpython-310.pyc", "path_type": "hardlink", "sha256": "c0f7165686c0b4ab1f0c331fc25c8a15204e3ee542c2e63914b2f07c789db9cb", "sha256_in_prefix": "c0f7165686c0b4ab1f0c331fc25c8a15204e3ee542c2e63914b2f07c789db9cb", "size_in_bytes": 4128}, {"_path": "Lib/site-packages/setuptools/__pycache__/launch.cpython-310.pyc", "path_type": "hardlink", "sha256": "3c78374db824a4ed272972beec0a5233faeafb5a693dac506f5caaefb5bac624", "sha256_in_prefix": "3c78374db824a4ed272972beec0a5233faeafb5a693dac506f5caaefb5bac624", "size_in_bytes": 880}, {"_path": "Lib/site-packages/setuptools/__pycache__/logging.cpython-310.pyc", "path_type": "hardlink", "sha256": "19f8adee7ab2ebf0785541e812b8e217b5410952ccb9fe6c9887a6934461b760", "sha256_in_prefix": "19f8adee7ab2ebf0785541e812b8e217b5410952ccb9fe6c9887a6934461b760", "size_in_bytes": 1259}, {"_path": "Lib/site-packages/setuptools/__pycache__/modified.cpython-310.pyc", "path_type": "hardlink", "sha256": "7c752a1bab51938fc98c9837bdb65026a167a77c295b831ce2b599646a0b5daa", "sha256_in_prefix": "7c752a1bab51938fc98c9837bdb65026a167a77c295b831ce2b599646a0b5daa", "size_in_bytes": 397}, {"_path": "Lib/site-packages/setuptools/__pycache__/monkey.cpython-310.pyc", "path_type": "hardlink", "sha256": "9bf46837edf7da82eb086c48fa4e2670c937b8f0a59e112030199b0095b13f17", "sha256_in_prefix": "9bf46837edf7da82eb086c48fa4e2670c937b8f0a59e112030199b0095b13f17", "size_in_bytes": 3613}, {"_path": "Lib/site-packages/setuptools/__pycache__/msvc.cpython-310.pyc", "path_type": "hardlink", "sha256": "ffe83518ff1da7ad8b0a9311c369d7626dc49e8408cc3475c3983ab695dcfb50", "sha256_in_prefix": "ffe83518ff1da7ad8b0a9311c369d7626dc49e8408cc3475c3983ab695dcfb50", "size_in_bytes": 36465}, {"_path": "Lib/site-packages/setuptools/__pycache__/namespaces.cpython-310.pyc", "path_type": "hardlink", "sha256": "258f685aeb1062e0d32d221aac273453711682c1ac5699f68a56cfb3c42796b7", "sha256_in_prefix": "258f685aeb1062e0d32d221aac273453711682c1ac5699f68a56cfb3c42796b7", "size_in_bytes": 3713}, {"_path": "Lib/site-packages/setuptools/__pycache__/package_index.cpython-310.pyc", "path_type": "hardlink", "sha256": "1c16e9754a704f856f887e702cbd3b4f690ec795a8bf6e917cae22e5d54ca57d", "sha256_in_prefix": "1c16e9754a704f856f887e702cbd3b4f690ec795a8bf6e917cae22e5d54ca57d", "size_in_bytes": 34783}, {"_path": "Lib/site-packages/setuptools/__pycache__/sandbox.cpython-310.pyc", "path_type": "hardlink", "sha256": "2379a8ee69e1b3828e874e8f0209f0a68e481aae717f5b30f3faa45e7cd2cb9c", "sha256_in_prefix": "2379a8ee69e1b3828e874e8f0209f0a68e481aae717f5b30f3faa45e7cd2cb9c", "size_in_bytes": 16335}, {"_path": "Lib/site-packages/setuptools/__pycache__/unicode_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "bfd47ff81fcf9bd85ef6fcf23300ce36148cefe10e225d736b082f03606b9f06", "sha256_in_prefix": "bfd47ff81fcf9bd85ef6fcf23300ce36148cefe10e225d736b082f03606b9f06", "size_in_bytes": 3136}, {"_path": "Lib/site-packages/setuptools/__pycache__/version.cpython-310.pyc", "path_type": "hardlink", "sha256": "bf5fccf8d520573e2b8dba749581be57816889cd8b39433dfe5f635b0d1b148f", "sha256_in_prefix": "bf5fccf8d520573e2b8dba749581be57816889cd8b39433dfe5f635b0d1b148f", "size_in_bytes": 288}, {"_path": "Lib/site-packages/setuptools/__pycache__/warnings.cpython-310.pyc", "path_type": "hardlink", "sha256": "730aff16c8199352f14bf2876eb1f6358242014ec1362bf975deabcb72ba2354", "sha256_in_prefix": "730aff16c8199352f14bf2876eb1f6358242014ec1362bf975deabcb72ba2354", "size_in_bytes": 3952}, {"_path": "Lib/site-packages/setuptools/__pycache__/wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "e83925945f89307c99fc5c5d27d7b06b0757f1e0558d93b0860d48088dec20b7", "sha256_in_prefix": "e83925945f89307c99fc5c5d27d7b06b0757f1e0558d93b0860d48088dec20b7", "size_in_bytes": 7754}, {"_path": "Lib/site-packages/setuptools/__pycache__/windows_support.cpython-310.pyc", "path_type": "hardlink", "sha256": "5ad0dc5e22a2dc30807d8394851ebc629d87f7facfd91f4735514f1f92320795", "sha256_in_prefix": "5ad0dc5e22a2dc30807d8394851ebc629d87f7facfd91f4735514f1f92320795", "size_in_bytes": 1002}, {"_path": "Lib/site-packages/setuptools/_core_metadata.py", "path_type": "hardlink", "sha256": "4fb4e3a7e592a0df3cd5a75ebf7475c335c23e79031ea6c2d8c83294dd728d2f", "sha256_in_prefix": "4fb4e3a7e592a0df3cd5a75ebf7475c335c23e79031ea6c2d8c83294dd728d2f", "size_in_bytes": 11978}, {"_path": "Lib/site-packages/setuptools/_distutils/__init__.py", "path_type": "hardlink", "sha256": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "sha256_in_prefix": "c4662e856c0b1b4ec9d10e3d0559c48cfcbac320dc77abde24c0c95fb9639723", "size_in_bytes": 359}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "ff1e1ca33e59c17dcf1e9631d1bd662081a1a3e230adaf3cd3b514939e21d069", "sha256_in_prefix": "ff1e1ca33e59c17dcf1e9631d1bd662081a1a3e230adaf3cd3b514939e21d069", "size_in_bytes": 334}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_log.cpython-310.pyc", "path_type": "hardlink", "sha256": "19502c229f9c3da06590a4aeaec928fa26c45638c3b48c66ee85a4e3eeed2df5", "sha256_in_prefix": "19502c229f9c3da06590a4aeaec928fa26c45638c3b48c66ee85a4e3eeed2df5", "size_in_bytes": 185}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_macos_compat.cpython-310.pyc", "path_type": "hardlink", "sha256": "ad5316362a19853767119d113d816da9836b2e01b8a82f6b0393da2888909fcb", "sha256_in_prefix": "ad5316362a19853767119d113d816da9836b2e01b8a82f6b0393da2888909fcb", "size_in_bytes": 408}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_modified.cpython-310.pyc", "path_type": "hardlink", "sha256": "dcf359249c53b3c75e5c63e51ce32ad4cf73bf8da4b74f5ac6d06d4dbf8fa963", "sha256_in_prefix": "dcf359249c53b3c75e5c63e51ce32ad4cf73bf8da4b74f5ac6d06d4dbf8fa963", "size_in_bytes": 3626}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/_msvccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "fc0f23bd57c7d4a13c18d28dc7e8ff29a924652e968d80694da6367e53bd5643", "sha256_in_prefix": "fc0f23bd57c7d4a13c18d28dc7e8ff29a924652e968d80694da6367e53bd5643", "size_in_bytes": 528}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/archive_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "b92181a57698b643e1dff065b51354f51ebf670cfb29c81223eef11eae1c817f", "sha256_in_prefix": "b92181a57698b643e1dff065b51354f51ebf670cfb29c81223eef11eae1c817f", "size_in_bytes": 7160}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/ccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "9edb724d80ad75f44d3dc8450799270303df4298cdb9ee49b01282655b3fcd92", "sha256_in_prefix": "9edb724d80ad75f44d3dc8450799270303df4298cdb9ee49b01282655b3fcd92", "size_in_bytes": 593}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/cmd.cpython-310.pyc", "path_type": "hardlink", "sha256": "48c49d576d3f09cd7bdd099bd423cc86bfb741768c11725480fa3da79bfe0d88", "sha256_in_prefix": "48c49d576d3f09cd7bdd099bd423cc86bfb741768c11725480fa3da79bfe0d88", "size_in_bytes": 17431}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/core.cpython-310.pyc", "path_type": "hardlink", "sha256": "777fb980c67075498198791c4647d1d64683405bd7fb5efa9338a046f6e9a778", "sha256_in_prefix": "777fb980c67075498198791c4647d1d64683405bd7fb5efa9338a046f6e9a778", "size_in_bytes": 7173}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/cygwinccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "90a9765dbc7a5c893c92194eda7c037e0e4983ad791c6f7ad55c259f5e95579d", "sha256_in_prefix": "90a9765dbc7a5c893c92194eda7c037e0e4983ad791c6f7ad55c259f5e95579d", "size_in_bytes": 529}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/debug.cpython-310.pyc", "path_type": "hardlink", "sha256": "3be425ac19b81cfea97f02121b8815592a04d1ebf3c63a85183013100405eb51", "sha256_in_prefix": "3be425ac19b81cfea97f02121b8815592a04d1ebf3c63a85183013100405eb51", "size_in_bytes": 207}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dep_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "0fe33af90cc0db67e0231d3db90aa3c6d6c76d32128d3ab64693e939a280d529", "sha256_in_prefix": "0fe33af90cc0db67e0231d3db90aa3c6d6c76d32128d3ab64693e939a280d529", "size_in_bytes": 543}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dir_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "3db202f36626423715333b749b4df74a66c4587b3d04e57904800a18cd09587c", "sha256_in_prefix": "3db202f36626423715333b749b4df74a66c4587b3d04e57904800a18cd09587c", "size_in_bytes": 7214}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/dist.cpython-310.pyc", "path_type": "hardlink", "sha256": "8ddd99daf0a929e7e4ae9351c59c3803d20ea369899f916e66c722af7d747965", "sha256_in_prefix": "8ddd99daf0a929e7e4ae9351c59c3803d20ea369899f916e66c722af7d747965", "size_in_bytes": 38888}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/errors.cpython-310.pyc", "path_type": "hardlink", "sha256": "f1e82beedad8b298333dd8d532e08fa4f5be1500944cee306fb6cb09bcf979f2", "sha256_in_prefix": "f1e82beedad8b298333dd8d532e08fa4f5be1500944cee306fb6cb09bcf979f2", "size_in_bytes": 3944}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/extension.cpython-310.pyc", "path_type": "hardlink", "sha256": "2e6d2443c24db0fadf9e67e770517defa6cc26f15dfb3d7638ef44d814ba85a3", "sha256_in_prefix": "2e6d2443c24db0fadf9e67e770517defa6cc26f15dfb3d7638ef44d814ba85a3", "size_in_bytes": 7632}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/fancy_getopt.cpython-310.pyc", "path_type": "hardlink", "sha256": "4c071f8aa2c119568ff8adc7bbae378d89a3eaebd8331281be554751fc6430dd", "sha256_in_prefix": "4c071f8aa2c119568ff8adc7bbae378d89a3eaebd8331281be554751fc6430dd", "size_in_bytes": 10849}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/file_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "898c05b6156dc0ddada03f550e5ef6129515c8d349f893c976dc532749a112a2", "sha256_in_prefix": "898c05b6156dc0ddada03f550e5ef6129515c8d349f893c976dc532749a112a2", "size_in_bytes": 5997}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/filelist.cpython-310.pyc", "path_type": "hardlink", "sha256": "ab2c130ec3eede57c09b3f4671b432c250de1fc80f27d0f616586eb417b4503a", "sha256_in_prefix": "ab2c130ec3eede57c09b3f4671b432c250de1fc80f27d0f616586eb417b4503a", "size_in_bytes": 12153}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/log.cpython-310.pyc", "path_type": "hardlink", "sha256": "8dabfe080d3c5ae3aff46c7bd0959d77293771901612965ae052843b3ededed9", "sha256_in_prefix": "8dabfe080d3c5ae3aff46c7bd0959d77293771901612965ae052843b3ededed9", "size_in_bytes": 1654}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/spawn.cpython-310.pyc", "path_type": "hardlink", "sha256": "7107d1b6e1945077e34784e1e04f890b4d44c7a29faac3bd8f46e369367ab8ae", "sha256_in_prefix": "7107d1b6e1945077e34784e1e04f890b4d44c7a29faac3bd8f46e369367ab8ae", "size_in_bytes": 4176}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/sysconfig.cpython-310.pyc", "path_type": "hardlink", "sha256": "39edb793690f0794b7b5e753e0a644ac6e59419dd4045dd3440ce412ba6b14a2", "sha256_in_prefix": "39edb793690f0794b7b5e753e0a644ac6e59419dd4045dd3440ce412ba6b14a2", "size_in_bytes": 15701}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/text_file.cpython-310.pyc", "path_type": "hardlink", "sha256": "8f93244dd6be3d35f5718ceb56b48abcc9839e8f1d3d35fada58c2e83349f2f2", "sha256_in_prefix": "8f93244dd6be3d35f5718ceb56b48abcc9839e8f1d3d35fada58c2e83349f2f2", "size_in_bytes": 8232}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/unixccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "f8b8ead24400ecd8f5ca77287aeee08b77190d154642a3dcd0a5fa34d47c8d3e", "sha256_in_prefix": "f8b8ead24400ecd8f5ca77287aeee08b77190d154642a3dcd0a5fa34d47c8d3e", "size_in_bytes": 296}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/util.cpython-310.pyc", "path_type": "hardlink", "sha256": "109da3d421bd022a899e2bbebf4550e3abf882666e13dbe2685c6561ef5856d5", "sha256_in_prefix": "109da3d421bd022a899e2bbebf4550e3abf882666e13dbe2685c6561ef5856d5", "size_in_bytes": 14281}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/version.cpython-310.pyc", "path_type": "hardlink", "sha256": "0a5aa60b94b1602149088c7ac794c9e0c2dba6ac9b05b66d0a30a050880bb6b6", "sha256_in_prefix": "0a5aa60b94b1602149088c7ac794c9e0c2dba6ac9b05b66d0a30a050880bb6b6", "size_in_bytes": 8083}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/versionpredicate.cpython-310.pyc", "path_type": "hardlink", "sha256": "67a885b633b0294884344465168818dfb53f4d79e7120f851ab75cc5da304b97", "sha256_in_prefix": "67a885b633b0294884344465168818dfb53f4d79e7120f851ab75cc5da304b97", "size_in_bytes": 5256}, {"_path": "Lib/site-packages/setuptools/_distutils/__pycache__/zosccompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "ee0013d527306ae665c71d6761323ea1e4f499ac4a8b48c77776b3f5392deab0", "sha256_in_prefix": "ee0013d527306ae665c71d6761323ea1e4f499ac4a8b48c77776b3f5392deab0", "size_in_bytes": 219}, {"_path": "Lib/site-packages/setuptools/_distutils/_log.py", "path_type": "hardlink", "sha256": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "sha256_in_prefix": "8be94d4d37174bc4e65884c9e833831afb56e73e6d31ab6d250efa87cad9c505", "size_in_bytes": 42}, {"_path": "Lib/site-packages/setuptools/_distutils/_macos_compat.py", "path_type": "hardlink", "sha256": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "sha256_in_prefix": "273506845e04e722084c76d468fa1b6445a318776badc355eb7cfce92e118c17", "size_in_bytes": 239}, {"_path": "Lib/site-packages/setuptools/_distutils/_modified.py", "path_type": "hardlink", "sha256": "445d67d427b1c83615de5bc66de5d2d2cf9708955ba0338851b03cc0442a6136", "sha256_in_prefix": "445d67d427b1c83615de5bc66de5d2d2cf9708955ba0338851b03cc0442a6136", "size_in_bytes": 3211}, {"_path": "Lib/site-packages/setuptools/_distutils/_msvccompiler.py", "path_type": "hardlink", "sha256": "f4f49f487c6f2671e740be92ab3e17733ee2681213eb6a7a061790cc6b12970a", "sha256_in_prefix": "f4f49f487c6f2671e740be92ab3e17733ee2681213eb6a7a061790cc6b12970a", "size_in_bytes": 335}, {"_path": "Lib/site-packages/setuptools/_distutils/archive_util.py", "path_type": "hardlink", "sha256": "430db3f8fb7e355f2535442bce3b375c31960961cc3e7a872f2b7c4e20f65c40", "sha256_in_prefix": "430db3f8fb7e355f2535442bce3b375c31960961cc3e7a872f2b7c4e20f65c40", "size_in_bytes": 8884}, {"_path": "Lib/site-packages/setuptools/_distutils/ccompiler.py", "path_type": "hardlink", "sha256": "14a563ab3189edcf85b68b8d8e12e268c3e6e4b28c6471c0aee5dff0b536d7a7", "sha256_in_prefix": "14a563ab3189edcf85b68b8d8e12e268c3e6e4b28c6471c0aee5dff0b536d7a7", "size_in_bytes": 524}, {"_path": "Lib/site-packages/setuptools/_distutils/cmd.py", "path_type": "hardlink", "sha256": "857b5a45a1fb4019df34e22a12f0ade3b8b06730fd315bc176185d41cb47b313", "sha256_in_prefix": "857b5a45a1fb4019df34e22a12f0ade3b8b06730fd315bc176185d41cb47b313", "size_in_bytes": 22186}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__init__.py", "path_type": "hardlink", "sha256": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "sha256_in_prefix": "19f140cdb06a935ab1487e0175a2a2a0a4b88514670f8e01026c0437ce42e2ef", "size_in_bytes": 386}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "635bf9f734777f812420fc3e2ca5fcd6bf82e43301922b293fdc2e9573a0f76f", "sha256_in_prefix": "635bf9f734777f812420fc3e2ca5fcd6bf82e43301922b293fdc2e9573a0f76f", "size_in_bytes": 453}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/_framework_compat.cpython-310.pyc", "path_type": "hardlink", "sha256": "334a167e03ad33724906b1a4a63ade214589b9013ec6845f046b6c6d9989ed3f", "sha256_in_prefix": "334a167e03ad33724906b1a4a63ade214589b9013ec6845f046b6c6d9989ed3f", "size_in_bytes": 1874}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "8115e2b07d5c5a26a9205a2eb15a494f214c7b0bf71ebfccdb0609ba702da699", "sha256_in_prefix": "8115e2b07d5c5a26a9205a2eb15a494f214c7b0bf71ebfccdb0609ba702da699", "size_in_bytes": 4805}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_dumb.cpython-310.pyc", "path_type": "hardlink", "sha256": "7da97342ec1cae8798770a2011771050c09fac8b380a22145ef016623a7ca7ce", "sha256_in_prefix": "7da97342ec1cae8798770a2011771050c09fac8b380a22145ef016623a7ca7ce", "size_in_bytes": 3645}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/bdist_rpm.cpython-310.pyc", "path_type": "hardlink", "sha256": "89d02dd63da77fce14edc90261b5f4c573408d916e90b6b77295f089d1f6bb0a", "sha256_in_prefix": "89d02dd63da77fce14edc90261b5f4c573408d916e90b6b77295f089d1f6bb0a", "size_in_bytes": 12384}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build.cpython-310.pyc", "path_type": "hardlink", "sha256": "6968d2bf02f9a1fa0d6abe580f8592fc1099d9e8123dae626929e8f9b340ba89", "sha256_in_prefix": "6968d2bf02f9a1fa0d6abe580f8592fc1099d9e8123dae626929e8f9b340ba89", "size_in_bytes": 4105}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_clib.cpython-310.pyc", "path_type": "hardlink", "sha256": "084a8529f81e014b72c6f303de4163d36fb005df484c408fb9832ede7d799100", "sha256_in_prefix": "084a8529f81e014b72c6f303de4163d36fb005df484c408fb9832ede7d799100", "size_in_bytes": 5030}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_ext.cpython-310.pyc", "path_type": "hardlink", "sha256": "335f41e71211dd510b9161e3d0333da3193d710f54e91cc0324be1dc677e3af5", "sha256_in_prefix": "335f41e71211dd510b9161e3d0333da3193d710f54e91cc0324be1dc677e3af5", "size_in_bytes": 17340}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_py.cpython-310.pyc", "path_type": "hardlink", "sha256": "16429de50662bc4e4a28d499e10eb8c921bd81b66e109edbb0cd738592db0e12", "sha256_in_prefix": "16429de50662bc4e4a28d499e10eb8c921bd81b66e109edbb0cd738592db0e12", "size_in_bytes": 9936}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/build_scripts.cpython-310.pyc", "path_type": "hardlink", "sha256": "88039c4b19a007c4eab3f21481e1784abdb313f88ec0c0286eeff6da767e2782", "sha256_in_prefix": "88039c4b19a007c4eab3f21481e1784abdb313f88ec0c0286eeff6da767e2782", "size_in_bytes": 4465}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/check.cpython-310.pyc", "path_type": "hardlink", "sha256": "025b94e2f6487fb1f1745a573d622b5435b0759b3578dcad5c430d0d553911fe", "sha256_in_prefix": "025b94e2f6487fb1f1745a573d622b5435b0759b3578dcad5c430d0d553911fe", "size_in_bytes": 4878}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/clean.cpython-310.pyc", "path_type": "hardlink", "sha256": "4882d052dd32fdadcf63525e2e34fe00c00c0418d50b9050b8a0b5fdf5bd722a", "sha256_in_prefix": "4882d052dd32fdadcf63525e2e34fe00c00c0418d50b9050b8a0b5fdf5bd722a", "size_in_bytes": 2175}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/config.cpython-310.pyc", "path_type": "hardlink", "sha256": "e29f92820ed93729fc696c2cfdcac3c11e1180d9a92e89cc7d421fc9cd56c36b", "sha256_in_prefix": "e29f92820ed93729fc696c2cfdcac3c11e1180d9a92e89cc7d421fc9cd56c36b", "size_in_bytes": 10430}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install.cpython-310.pyc", "path_type": "hardlink", "sha256": "616c4debf639da9f4d36dbd1140042d424ac0a0f891352d44aab0cdadf03b7b6", "sha256_in_prefix": "616c4debf639da9f4d36dbd1140042d424ac0a0f891352d44aab0cdadf03b7b6", "size_in_bytes": 16981}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_data.cpython-310.pyc", "path_type": "hardlink", "sha256": "9c0a3d8eba06d1658fb966802d60534d0b143aeac494caa2445fa2c072adfeae", "sha256_in_prefix": "9c0a3d8eba06d1658fb966802d60534d0b143aeac494caa2445fa2c072adfeae", "size_in_bytes": 2993}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_egg_info.cpython-310.pyc", "path_type": "hardlink", "sha256": "65eb5853203624b2228d70d6717ddf058c4b4199c8dd40d09a82df08f82fb15b", "sha256_in_prefix": "65eb5853203624b2228d70d6717ddf058c4b4199c8dd40d09a82df08f82fb15b", "size_in_bytes": 3421}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_headers.cpython-310.pyc", "path_type": "hardlink", "sha256": "1c916ab2a0e9ed1808772651556ed262e5ae1c0ec201a7a7b4d6c994d79b7abb", "sha256_in_prefix": "1c916ab2a0e9ed1808772651556ed262e5ae1c0ec201a7a7b4d6c994d79b7abb", "size_in_bytes": 1847}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_lib.cpython-310.pyc", "path_type": "hardlink", "sha256": "d38b1245daed31514ccad4b899594194740c902189f639f882355f5c6c29a3e8", "sha256_in_prefix": "d38b1245daed31514ccad4b899594194740c902189f639f882355f5c6c29a3e8", "size_in_bytes": 5506}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/install_scripts.cpython-310.pyc", "path_type": "hardlink", "sha256": "130e219a2398c8a4a4decf71d6997fee25321a706196230924e800e14ad408d0", "sha256_in_prefix": "130e219a2398c8a4a4decf71d6997fee25321a706196230924e800e14ad408d0", "size_in_bytes": 2267}, {"_path": "Lib/site-packages/setuptools/_distutils/command/__pycache__/sdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "0611216783595b1b3a4007edc015c363239b5c2181436603fb5e70ec9b9ae81f", "sha256_in_prefix": "0611216783595b1b3a4007edc015c363239b5c2181436603fb5e70ec9b9ae81f", "size_in_bytes": 14821}, {"_path": "Lib/site-packages/setuptools/_distutils/command/_framework_compat.py", "path_type": "hardlink", "sha256": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "sha256_in_prefix": "d2265d4896331915820afcd10ca13e474fbfc9a018bc531dd729576f67985ee8", "size_in_bytes": 1609}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist.py", "path_type": "hardlink", "sha256": "8d6b64eb547b7d635450dc49574b614d9cd4e67f342f7032d7069288ff6488b0", "sha256_in_prefix": "8d6b64eb547b7d635450dc49574b614d9cd4e67f342f7032d7069288ff6488b0", "size_in_bytes": 5854}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist_dumb.py", "path_type": "hardlink", "sha256": "1f1d6302aa19371608cb83794cbcd4a7a2797e2f0bb35f29cbb5252cd1613b61", "sha256_in_prefix": "1f1d6302aa19371608cb83794cbcd4a7a2797e2f0bb35f29cbb5252cd1613b61", "size_in_bytes": 4631}, {"_path": "Lib/site-packages/setuptools/_distutils/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "9f17175efe5aec1fb59ed5aee036c6982b444b810120dac968141c44d0180892", "sha256_in_prefix": "9f17175efe5aec1fb59ed5aee036c6982b444b810120dac968141c44d0180892", "size_in_bytes": 21785}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build.py", "path_type": "hardlink", "sha256": "4a91e56a07f488d68a572221c437e13c567c5f5f8b0163824000b2fb2b762b4c", "sha256_in_prefix": "4a91e56a07f488d68a572221c437e13c567c5f5f8b0163824000b2fb2b762b4c", "size_in_bytes": 5923}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_clib.py", "path_type": "hardlink", "sha256": "68ca997147c26ce02eff1afe03d896f90f58647ce90c62d14decce80c4099924", "sha256_in_prefix": "68ca997147c26ce02eff1afe03d896f90f58647ce90c62d14decce80c4099924", "size_in_bytes": 7777}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_ext.py", "path_type": "hardlink", "sha256": "cebaecbbd1d79f357a6d761b26e6422b84b05593232a7978a46d68ddb35cc6d7", "sha256_in_prefix": "cebaecbbd1d79f357a6d761b26e6422b84b05593232a7978a46d68ddb35cc6d7", "size_in_bytes": 32710}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_py.py", "path_type": "hardlink", "sha256": "55fabe20d7a6a0c6e0e9fd34dc14f2fd47e9f1b8ce661985221a4a31c7d72e0b", "sha256_in_prefix": "55fabe20d7a6a0c6e0e9fd34dc14f2fd47e9f1b8ce661985221a4a31c7d72e0b", "size_in_bytes": 16696}, {"_path": "Lib/site-packages/setuptools/_distutils/command/build_scripts.py", "path_type": "hardlink", "sha256": "b54a44cf04ec9eb3fcaab368af2de574f076e3440308590ca7ea5d60fb36c139", "sha256_in_prefix": "b54a44cf04ec9eb3fcaab368af2de574f076e3440308590ca7ea5d60fb36c139", "size_in_bytes": 5118}, {"_path": "Lib/site-packages/setuptools/_distutils/command/check.py", "path_type": "hardlink", "sha256": "ca835ed8c3d8e0971333baf0a0841d7d9ef9ab9462d39f08d9ca22f86abd0a33", "sha256_in_prefix": "ca835ed8c3d8e0971333baf0a0841d7d9ef9ab9462d39f08d9ca22f86abd0a33", "size_in_bytes": 4946}, {"_path": "Lib/site-packages/setuptools/_distutils/command/clean.py", "path_type": "hardlink", "sha256": "75001a70e69bc015d4f49a19fb5185bacab778596d0da7972454989dca866ef1", "sha256_in_prefix": "75001a70e69bc015d4f49a19fb5185bacab778596d0da7972454989dca866ef1", "size_in_bytes": 2644}, {"_path": "Lib/site-packages/setuptools/_distutils/command/config.py", "path_type": "hardlink", "sha256": "06e51d3eef75568f70e38c730f54507e2c977d27d570da5e5f769ea0a70600ec", "sha256_in_prefix": "06e51d3eef75568f70e38c730f54507e2c977d27d570da5e5f769ea0a70600ec", "size_in_bytes": 12818}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install.py", "path_type": "hardlink", "sha256": "f897a707e9ae6b885cd9123ff96f05f4f9cffc9f8e6853bb1343c918ac4ba35a", "sha256_in_prefix": "f897a707e9ae6b885cd9123ff96f05f4f9cffc9f8e6853bb1343c918ac4ba35a", "size_in_bytes": 30072}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_data.py", "path_type": "hardlink", "sha256": "1b306551658ab9b4d82653fe2f46ae52b8aaf5c2fee5128e728c874edb4a8f44", "sha256_in_prefix": "1b306551658ab9b4d82653fe2f46ae52b8aaf5c2fee5128e728c874edb4a8f44", "size_in_bytes": 2875}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_egg_info.py", "path_type": "hardlink", "sha256": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "sha256_in_prefix": "7df88ba14d62bd027cab6fd62fb6728196d470eb207452ca2fba2d1082565a42", "size_in_bytes": 2868}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_headers.py", "path_type": "hardlink", "sha256": "e5c88a0a3f1cdd72ac60d29d91d32f9f2a5a50229ca1608379e6628f77c3f99e", "sha256_in_prefix": "e5c88a0a3f1cdd72ac60d29d91d32f9f2a5a50229ca1608379e6628f77c3f99e", "size_in_bytes": 1272}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_lib.py", "path_type": "hardlink", "sha256": "dacf7e9b9f9bd6a2a6e75176f250792f7f59eafbff187325bfd74d052ba9a24d", "sha256_in_prefix": "dacf7e9b9f9bd6a2a6e75176f250792f7f59eafbff187325bfd74d052ba9a24d", "size_in_bytes": 8588}, {"_path": "Lib/site-packages/setuptools/_distutils/command/install_scripts.py", "path_type": "hardlink", "sha256": "334a4f7626aa07b4c69aa4ccba3a4619e88bd08abf0937868cc16dae60e6c333", "sha256_in_prefix": "334a4f7626aa07b4c69aa4ccba3a4619e88bd08abf0937868cc16dae60e6c333", "size_in_bytes": 2002}, {"_path": "Lib/site-packages/setuptools/_distutils/command/sdist.py", "path_type": "hardlink", "sha256": "711205e87b75849e9ac8e38557270c14150dc63a3de1efeb1583f1e078bc0217", "sha256_in_prefix": "711205e87b75849e9ac8e38557270c14150dc63a3de1efeb1583f1e078bc0217", "size_in_bytes": 19151}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__init__.py", "path_type": "hardlink", "sha256": "276d1a5c68c9f3a460e35c452c85a57160a067d79d31d27dbef74d110f3bbcf4", "sha256_in_prefix": "276d1a5c68c9f3a460e35c452c85a57160a067d79d31d27dbef74d110f3bbcf4", "size_in_bytes": 522}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "8f72f5b603603b359f306f0f05a30ae6c779e0542b54e3022c16e80df589e2f5", "sha256_in_prefix": "8f72f5b603603b359f306f0f05a30ae6c779e0542b54e3022c16e80df589e2f5", "size_in_bytes": 970}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/numpy.cpython-310.pyc", "path_type": "hardlink", "sha256": "383cb32b4210d80f69118bc4a9a5d8ca1ad850c0d03de28e9dc29b9a6cbd9158", "sha256_in_prefix": "383cb32b4210d80f69118bc4a9a5d8ca1ad850c0d03de28e9dc29b9a6cbd9158", "size_in_bytes": 232}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/__pycache__/py39.cpython-310.pyc", "path_type": "hardlink", "sha256": "c5a3d5c17dc9d7b3741d91c05b6d86c62d398d4e6071c41136d4c13e7c33a804", "sha256_in_prefix": "c5a3d5c17dc9d7b3741d91c05b6d86c62d398d4e6071c41136d4c13e7c33a804", "size_in_bytes": 1889}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/numpy.py", "path_type": "hardlink", "sha256": "505827799c3dc3dee0e1cfb21a80083b22f150e590f9f3d122185f32ceff3ae7", "sha256_in_prefix": "505827799c3dc3dee0e1cfb21a80083b22f150e590f9f3d122185f32ceff3ae7", "size_in_bytes": 167}, {"_path": "Lib/site-packages/setuptools/_distutils/compat/py39.py", "path_type": "hardlink", "sha256": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "sha256_in_prefix": "84eb03ea5c192ea66832769c349dcfea7500f8b250844a55b584f3547d28f7a3", "size_in_bytes": 1964}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/base.cpython-310.pyc", "path_type": "hardlink", "sha256": "ffae080d58bf3141e25d95a0d851d21ed3b6d088bcb0c538a5d63ceb115988e2", "sha256_in_prefix": "ffae080d58bf3141e25d95a0d851d21ed3b6d088bcb0c538a5d63ceb115988e2", "size_in_bytes": 40618}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/cygwin.cpython-310.pyc", "path_type": "hardlink", "sha256": "3fc41cdcaeda2b56393e21715eed630373ea9a63f6f1cb340d97d269367c770b", "sha256_in_prefix": "3fc41cdcaeda2b56393e21715eed630373ea9a63f6f1cb340d97d269367c770b", "size_in_bytes": 8042}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/errors.cpython-310.pyc", "path_type": "hardlink", "sha256": "343ba12dc53353f5d55fb90e64a3e25a682aba7a88bf7794ef50158c3647f026", "sha256_in_prefix": "343ba12dc53353f5d55fb90e64a3e25a682aba7a88bf7794ef50158c3647f026", "size_in_bytes": 1249}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/msvc.cpython-310.pyc", "path_type": "hardlink", "sha256": "6db076612303d7aeb13c6ea8ea9d00fa68b06c11a00ea5c6d0abc792bda271f6", "sha256_in_prefix": "6db076612303d7aeb13c6ea8ea9d00fa68b06c11a00ea5c6d0abc792bda271f6", "size_in_bytes": 15367}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/unix.cpython-310.pyc", "path_type": "hardlink", "sha256": "d715c0bcd48ac01a05d6bb9033aed4fa5ac6b9e01e33c7e25a7c06c36d0bd276", "sha256_in_prefix": "d715c0bcd48ac01a05d6bb9033aed4fa5ac6b9e01e33c7e25a7c06c36d0bd276", "size_in_bytes": 11044}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/__pycache__/zos.cpython-310.pyc", "path_type": "hardlink", "sha256": "ea551b3e3980a0ff650d4a657688cdddc77571802f66302f4d640f336901382a", "sha256_in_prefix": "ea551b3e3980a0ff650d4a657688cdddc77571802f66302f4d640f336901382a", "size_in_bytes": 4206}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/base.py", "path_type": "hardlink", "sha256": "5d1d6b0424ad0aabaa9bb40e6170f8d7e2dfbec15c3e91b1af0c5e5f32729ffc", "sha256_in_prefix": "5d1d6b0424ad0aabaa9bb40e6170f8d7e2dfbec15c3e91b1af0c5e5f32729ffc", "size_in_bytes": 54876}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/cygwin.py", "path_type": "hardlink", "sha256": "0d49704126f9e5a8fb39d72671d76b98299512311ac48889e611d43b71813cdb", "sha256_in_prefix": "0d49704126f9e5a8fb39d72671d76b98299512311ac48889e611d43b71813cdb", "size_in_bytes": 11844}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/errors.py", "path_type": "hardlink", "sha256": "b0a395cc96a331498d75fcb0a3d50cfd0406b0a15c7250e1b48e5394289730b7", "sha256_in_prefix": "b0a395cc96a331498d75fcb0a3d50cfd0406b0a15c7250e1b48e5394289730b7", "size_in_bytes": 573}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/msvc.py", "path_type": "hardlink", "sha256": "3bcce8e8d2830300aebf917414f65e02ec986fb0055c82ede4db676463e5c8d8", "sha256_in_prefix": "3bcce8e8d2830300aebf917414f65e02ec986fb0055c82ede4db676463e5c8d8", "size_in_bytes": 21802}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_base.cpython-310.pyc", "path_type": "hardlink", "sha256": "4ff8ff4c5187555f0c37404fd900f43ee94cf60dcdfacd2a05383db65970a782", "sha256_in_prefix": "4ff8ff4c5187555f0c37404fd900f43ee94cf60dcdfacd2a05383db65970a782", "size_in_bytes": 2399}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_cygwin.cpython-310.pyc", "path_type": "hardlink", "sha256": "79343a9fc6070855a541634f604686ab39fff614e168656d4388a8e96da73688", "sha256_in_prefix": "79343a9fc6070855a541634f604686ab39fff614e168656d4388a8e96da73688", "size_in_bytes": 2899}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_mingw.cpython-310.pyc", "path_type": "hardlink", "sha256": "0233330f97564924c1a0fe7a4c26e62edc1a5bfdf7a5415237d36d4d63f97ce3", "sha256_in_prefix": "0233330f97564924c1a0fe7a4c26e62edc1a5bfdf7a5415237d36d4d63f97ce3", "size_in_bytes": 2420}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_msvc.cpython-310.pyc", "path_type": "hardlink", "sha256": "f21e1b13224043c91302cd9cdd2bfa3f9ce8b82cdb931fb32f72b349539a6f09", "sha256_in_prefix": "f21e1b13224043c91302cd9cdd2bfa3f9ce8b82cdb931fb32f72b349539a6f09", "size_in_bytes": 5097}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/__pycache__/test_unix.cpython-310.pyc", "path_type": "hardlink", "sha256": "057489ea5affba4adec96d54b8f603cca3c8e42c8324d22d0114efda7eca30cc", "sha256_in_prefix": "057489ea5affba4adec96d54b8f603cca3c8e42c8324d22d0114efda7eca30cc", "size_in_bytes": 9343}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_base.py", "path_type": "hardlink", "sha256": "add847739e9b857b66e4d9cdf41487c2be9cebd52accc22d650ce5c3602c74c7", "sha256_in_prefix": "add847739e9b857b66e4d9cdf41487c2be9cebd52accc22d650ce5c3602c74c7", "size_in_bytes": 2706}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_cygwin.py", "path_type": "hardlink", "sha256": "5205765605178f756e95c6c373450159f132243c78dad812c12e0bcc78b1de66", "sha256_in_prefix": "5205765605178f756e95c6c373450159f132243c78dad812c12e0bcc78b1de66", "size_in_bytes": 2701}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_mingw.py", "path_type": "hardlink", "sha256": "8429b0cb2c084a9468c8ec926c51c12f84e9ad6455d265160ca98e2cef170571", "sha256_in_prefix": "8429b0cb2c084a9468c8ec926c51c12f84e9ad6455d265160ca98e2cef170571", "size_in_bytes": 1900}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_msvc.py", "path_type": "hardlink", "sha256": "0e51a3999d660523172209a5bbcd0129ced5f8424e66e62e730270161e5d9f6f", "sha256_in_prefix": "0e51a3999d660523172209a5bbcd0129ced5f8424e66e62e730270161e5d9f6f", "size_in_bytes": 4151}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/tests/test_unix.py", "path_type": "hardlink", "sha256": "12b6d85a2a3b7a363666a4263e4e00c0ebb51c55b8fbff9a65d52f19ad56d85c", "sha256_in_prefix": "12b6d85a2a3b7a363666a4263e4e00c0ebb51c55b8fbff9a65d52f19ad56d85c", "size_in_bytes": 11834}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/unix.py", "path_type": "hardlink", "sha256": "97b0b1638ac3240102268faf72fea2a344819a63c9f4998de664a665c8a7d955", "sha256_in_prefix": "97b0b1638ac3240102268faf72fea2a344819a63c9f4998de664a665c8a7d955", "size_in_bytes": 16502}, {"_path": "Lib/site-packages/setuptools/_distutils/compilers/C/zos.py", "path_type": "hardlink", "sha256": "be735e58b45991d224759f98c819cbf2275351f7023a7d2d2cc5b938127449c5", "sha256_in_prefix": "be735e58b45991d224759f98c819cbf2275351f7023a7d2d2cc5b938127449c5", "size_in_bytes": 6586}, {"_path": "Lib/site-packages/setuptools/_distutils/core.py", "path_type": "hardlink", "sha256": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "sha256_in_prefix": "1841ca6850b8f13de8fbf4a61f8f3ae06bcacb1d4881b542e884883d5971edae", "size_in_bytes": 9364}, {"_path": "Lib/site-packages/setuptools/_distutils/cygwinccompiler.py", "path_type": "hardlink", "sha256": "986fdc53c4956786a60ff56d179bc7e815cfd3e920846b033db0d25eb43deb77", "sha256_in_prefix": "986fdc53c4956786a60ff56d179bc7e815cfd3e920846b033db0d25eb43deb77", "size_in_bytes": 594}, {"_path": "Lib/site-packages/setuptools/_distutils/debug.py", "path_type": "hardlink", "sha256": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "sha256_in_prefix": "37a32b4c0a8aea5f52564ead5b0791d74f0f33c3a5eea3657f257e9c770b86c6", "size_in_bytes": 139}, {"_path": "Lib/site-packages/setuptools/_distutils/dep_util.py", "path_type": "hardlink", "sha256": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "sha256_in_prefix": "c4def9a7a6691e13221c473eae92f65e29494329c79c336269f1ed79a678b635", "size_in_bytes": 349}, {"_path": "Lib/site-packages/setuptools/_distutils/dir_util.py", "path_type": "hardlink", "sha256": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "sha256_in_prefix": "0d73d495f5551ac83d07e26083802dfe3f53eef33ad0e8303579101ea4e8efe2", "size_in_bytes": 7236}, {"_path": "Lib/site-packages/setuptools/_distutils/dist.py", "path_type": "hardlink", "sha256": "816e7df1413458c9335d0437d4dafef0becc3f0d2820ecf9392491cd8665c2b3", "sha256_in_prefix": "816e7df1413458c9335d0437d4dafef0becc3f0d2820ecf9392491cd8665c2b3", "size_in_bytes": 55794}, {"_path": "Lib/site-packages/setuptools/_distutils/errors.py", "path_type": "hardlink", "sha256": "3cf136a03461e72f50d5b78a2bdae176f0da0b34218b81c25ece0a72a7ea8196", "sha256_in_prefix": "3cf136a03461e72f50d5b78a2bdae176f0da0b34218b81c25ece0a72a7ea8196", "size_in_bytes": 3092}, {"_path": "Lib/site-packages/setuptools/_distutils/extension.py", "path_type": "hardlink", "sha256": "168caee2050b70faa6d7f53dceb6181f1364e0daa0957bf5adbb0e93f42b49db", "sha256_in_prefix": "168caee2050b70faa6d7f53dceb6181f1364e0daa0957bf5adbb0e93f42b49db", "size_in_bytes": 11155}, {"_path": "Lib/site-packages/setuptools/_distutils/fancy_getopt.py", "path_type": "hardlink", "sha256": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "sha256_in_prefix": "3e374ef9b5825b48a657f50df8c184c3d47618fd8e884f291e32138264c06374", "size_in_bytes": 17895}, {"_path": "Lib/site-packages/setuptools/_distutils/file_util.py", "path_type": "hardlink", "sha256": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "sha256_in_prefix": "60540bfe90f784bb8447d208fc7ebe8430bf608184a2891d778c1e74bba24d6d", "size_in_bytes": 7978}, {"_path": "Lib/site-packages/setuptools/_distutils/filelist.py", "path_type": "hardlink", "sha256": "30179244998f70a983bfca28660494e018903d9d0a870bfc97f8e10f9d17c9c2", "sha256_in_prefix": "30179244998f70a983bfca28660494e018903d9d0a870bfc97f8e10f9d17c9c2", "size_in_bytes": 15337}, {"_path": "Lib/site-packages/setuptools/_distutils/log.py", "path_type": "hardlink", "sha256": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "sha256_in_prefix": "57206ce63ef3e3e2ba5d310405385473d1f2329a0f2c6b50a4446a6f3e72970c", "size_in_bytes": 1200}, {"_path": "Lib/site-packages/setuptools/_distutils/spawn.py", "path_type": "hardlink", "sha256": "cec78287db0489fca9d08e5583bd7d24d2004a544e2767a15ea4271e5a6df3d4", "sha256_in_prefix": "cec78287db0489fca9d08e5583bd7d24d2004a544e2767a15ea4271e5a6df3d4", "size_in_bytes": 4086}, {"_path": "Lib/site-packages/setuptools/_distutils/sysconfig.py", "path_type": "hardlink", "sha256": "29e23c3876ccb84cc727c4347017b3f4a667cbc891cba67a634024333d6396c5", "sha256_in_prefix": "29e23c3876ccb84cc727c4347017b3f4a667cbc891cba67a634024333d6396c5", "size_in_bytes": 19728}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__init__.py", "path_type": "hardlink", "sha256": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "sha256_in_prefix": "8fe2283d912d42fdc438fbaa353c1a96be862f2463cc20be38e68dbd9ce61ec2", "size_in_bytes": 1485}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "5ed813f330dffe8693794d7a14fe2af7136924bd7ab3f8b16af302abd750b48e", "sha256_in_prefix": "5ed813f330dffe8693794d7a14fe2af7136924bd7ab3f8b16af302abd750b48e", "size_in_bytes": 1477}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/support.cpython-310.pyc", "path_type": "hardlink", "sha256": "1ed50ee9c6294d1432e6e9920a854d9bdd966e56cdb4e208b28f098a9afc242d", "sha256_in_prefix": "1ed50ee9c6294d1432e6e9920a854d9bdd966e56cdb4e208b28f098a9afc242d", "size_in_bytes": 5078}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_archive_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "937a6dd895aad04fd59dce50971ca77a54fd2b82dd23cca2029e52f51b15a381", "sha256_in_prefix": "937a6dd895aad04fd59dce50971ca77a54fd2b82dd23cca2029e52f51b15a381", "size_in_bytes": 10674}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "34a3bb25059d13cacf292151b195c7632f1a3e43cb5b0283ce0e46931f17aadf", "sha256_in_prefix": "34a3bb25059d13cacf292151b195c7632f1a3e43cb5b0283ce0e46931f17aadf", "size_in_bytes": 1286}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_dumb.cpython-310.pyc", "path_type": "hardlink", "sha256": "30836b66c7e8f03b99792aa9a834ea7bcc2c6f8097f971ea31348816a48e096e", "sha256_in_prefix": "30836b66c7e8f03b99792aa9a834ea7bcc2c6f8097f971ea31348816a48e096e", "size_in_bytes": 2082}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_bdist_rpm.cpython-310.pyc", "path_type": "hardlink", "sha256": "da97b2657e25df43502cc5a16e090ff114b9c451711ccecdc55012f88462cbd7", "sha256_in_prefix": "da97b2657e25df43502cc5a16e090ff114b9c451711ccecdc55012f88462cbd7", "size_in_bytes": 2992}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build.cpython-310.pyc", "path_type": "hardlink", "sha256": "627622915916ee41eef2083a1c202ef2ce9b6da0e599dcfa86e7aeb00d43f98f", "sha256_in_prefix": "627622915916ee41eef2083a1c202ef2ce9b6da0e599dcfa86e7aeb00d43f98f", "size_in_bytes": 1438}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_clib.cpython-310.pyc", "path_type": "hardlink", "sha256": "edea53e3f28c11c4d835b4614ce825c588c6880f02f94e2a67923952af835ee8", "sha256_in_prefix": "edea53e3f28c11c4d835b4614ce825c588c6880f02f94e2a67923952af835ee8", "size_in_bytes": 3733}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_ext.cpython-310.pyc", "path_type": "hardlink", "sha256": "7551d94f7e734b052f2895a777c2f56597f4f28f3619478e35d8e0afcd202e74", "sha256_in_prefix": "7551d94f7e734b052f2895a777c2f56597f4f28f3619478e35d8e0afcd202e74", "size_in_bytes": 15482}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_py.cpython-310.pyc", "path_type": "hardlink", "sha256": "a73b57e82e1563af2bc14119f27fdd5e096848880057ffad750e543bbf73f55f", "sha256_in_prefix": "a73b57e82e1563af2bc14119f27fdd5e096848880057ffad750e543bbf73f55f", "size_in_bytes": 5559}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_build_scripts.cpython-310.pyc", "path_type": "hardlink", "sha256": "01aa4d8bd40391c5953b7e6bf1fa46fa34712b49119862c691638c72be46f228", "sha256_in_prefix": "01aa4d8bd40391c5953b7e6bf1fa46fa34712b49119862c691638c72be46f228", "size_in_bytes": 3095}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_check.cpython-310.pyc", "path_type": "hardlink", "sha256": "30a712dd0dadfb43328dd1d56af5c22362d63b583ee4c848b420ea1e65501d27", "sha256_in_prefix": "30a712dd0dadfb43328dd1d56af5c22362d63b583ee4c848b420ea1e65501d27", "size_in_bytes": 4403}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_clean.cpython-310.pyc", "path_type": "hardlink", "sha256": "1566c58aeee5f6d131cf318eaa2604ca1848ad77da215726137c61b6bcb08193", "sha256_in_prefix": "1566c58aeee5f6d131cf318eaa2604ca1848ad77da215726137c61b6bcb08193", "size_in_bytes": 1299}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_cmd.cpython-310.pyc", "path_type": "hardlink", "sha256": "009d079bd5a5d84d87a02594a96034366a338f138ade15dbf31a1536f308930b", "sha256_in_prefix": "009d079bd5a5d84d87a02594a96034366a338f138ade15dbf31a1536f308930b", "size_in_bytes": 4055}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_config_cmd.cpython-310.pyc", "path_type": "hardlink", "sha256": "7c8b8b6a768496f37301c643fb5fa54fa83b8fd1ae60b5582ca2adccc6479d31", "sha256_in_prefix": "7c8b8b6a768496f37301c643fb5fa54fa83b8fd1ae60b5582ca2adccc6479d31", "size_in_bytes": 3013}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_core.cpython-310.pyc", "path_type": "hardlink", "sha256": "ce91a6a301b985f713b81c45754dd1fbefba0690b58e96ce90685457b9f41e9c", "sha256_in_prefix": "ce91a6a301b985f713b81c45754dd1fbefba0690b58e96ce90685457b9f41e9c", "size_in_bytes": 3952}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dir_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "e524265942d1edb36f266fe3645965e4d65ab9c95e10a0b31f40b50244625db6", "sha256_in_prefix": "e524265942d1edb36f266fe3645965e4d65ab9c95e10a0b31f40b50244625db6", "size_in_bytes": 4933}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_dist.cpython-310.pyc", "path_type": "hardlink", "sha256": "6e507d43f492a326c7a7856ab472dd2ceb490cfe5145d97c0ce9e289fffb6318", "sha256_in_prefix": "6e507d43f492a326c7a7856ab472dd2ceb490cfe5145d97c0ce9e289fffb6318", "size_in_bytes": 16746}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_extension.cpython-310.pyc", "path_type": "hardlink", "sha256": "4e45fb59468678eeba8a92e76e059d7df9322cdd0b0b10db59d76cd572b7ecad", "sha256_in_prefix": "4e45fb59468678eeba8a92e76e059d7df9322cdd0b0b10db59d76cd572b7ecad", "size_in_bytes": 2584}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_file_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "2704ca0789f8877ac08ff51d2f8a305d5a9435e09f4148ee0e3be10f9edd29b5", "sha256_in_prefix": "2704ca0789f8877ac08ff51d2f8a305d5a9435e09f4148ee0e3be10f9edd29b5", "size_in_bytes": 3475}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_filelist.cpython-310.pyc", "path_type": "hardlink", "sha256": "a8c5f71d146a9544e050b5975353756c454d48aa05afe2a5bd60a8b3290f424d", "sha256_in_prefix": "a8c5f71d146a9544e050b5975353756c454d48aa05afe2a5bd60a8b3290f424d", "size_in_bytes": 8286}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install.cpython-310.pyc", "path_type": "hardlink", "sha256": "31ce2ab489d26a0389f193896cad52f45ec8d5ea01e16ba31d0bafc9bea0a477", "sha256_in_prefix": "31ce2ab489d26a0389f193896cad52f45ec8d5ea01e16ba31d0bafc9bea0a477", "size_in_bytes": 7383}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_data.cpython-310.pyc", "path_type": "hardlink", "sha256": "bb08dfae38f28212f966f4f89ee9daf329c86c5940090be3a71a0bb6d4cb7389", "sha256_in_prefix": "bb08dfae38f28212f966f4f89ee9daf329c86c5940090be3a71a0bb6d4cb7389", "size_in_bytes": 1838}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_headers.cpython-310.pyc", "path_type": "hardlink", "sha256": "944219b0a71920b5ddf1976ea388ddc2ad619ac163c2134db924b69f3224d163", "sha256_in_prefix": "944219b0a71920b5ddf1976ea388ddc2ad619ac163c2134db924b69f3224d163", "size_in_bytes": 1138}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_lib.cpython-310.pyc", "path_type": "hardlink", "sha256": "838e47ce23bb6a72c52e480ba1c507ccf5d2c0bf1403d98e7fddb4780cb142cd", "sha256_in_prefix": "838e47ce23bb6a72c52e480ba1c507ccf5d2c0bf1403d98e7fddb4780cb142cd", "size_in_bytes": 3101}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_install_scripts.cpython-310.pyc", "path_type": "hardlink", "sha256": "c7e581faf6e7f5de1c3e527119a140ae3ec73d0bc5e5f28a70e5d52ad61492b7", "sha256_in_prefix": "c7e581faf6e7f5de1c3e527119a140ae3ec73d0bc5e5f28a70e5d52ad61492b7", "size_in_bytes": 1655}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_log.cpython-310.pyc", "path_type": "hardlink", "sha256": "f35247adbce2582df1a809fb5474222a1a2e3e0baa3d41d424604ef161ba4d21", "sha256_in_prefix": "f35247adbce2582df1a809fb5474222a1a2e3e0baa3d41d424604ef161ba4d21", "size_in_bytes": 689}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_modified.cpython-310.pyc", "path_type": "hardlink", "sha256": "293886ba40f3cb40fad4d2dbdce75120793ff47762f4d0bba0c1effe26bb4d9e", "sha256_in_prefix": "293886ba40f3cb40fad4d2dbdce75120793ff47762f4d0bba0c1effe26bb4d9e", "size_in_bytes": 4174}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "46938f610697bb739065965806969f6394d5e6944033e6ab6905641fbaac76ad", "sha256_in_prefix": "46938f610697bb739065965806969f6394d5e6944033e6ab6905641fbaac76ad", "size_in_bytes": 11183}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_spawn.cpython-310.pyc", "path_type": "hardlink", "sha256": "49391ee02e20a8dd3fbbdd98927e20afefed26752ad4aff32fc3766884b1a07c", "sha256_in_prefix": "49391ee02e20a8dd3fbbdd98927e20afefed26752ad4aff32fc3766884b1a07c", "size_in_bytes": 3584}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_sysconfig.cpython-310.pyc", "path_type": "hardlink", "sha256": "b93dab722af3e4429bc90b30871838b89a448f3c48cbbc86f37416aeca88932f", "sha256_in_prefix": "b93dab722af3e4429bc90b30871838b89a448f3c48cbbc86f37416aeca88932f", "size_in_bytes": 10851}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_text_file.cpython-310.pyc", "path_type": "hardlink", "sha256": "b5b5a0a79a390d4ca4710bd53b473e6ab0970004e57bcd3b49c9723f6fbdbe39", "sha256_in_prefix": "b5b5a0a79a390d4ca4710bd53b473e6ab0970004e57bcd3b49c9723f6fbdbe39", "size_in_bytes": 2145}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "e7fcc8be72024a7902776bd79515f50a0899fc8ec9c51ccf83f60413d8a5a4e5", "sha256_in_prefix": "e7fcc8be72024a7902776bd79515f50a0899fc8ec9c51ccf83f60413d8a5a4e5", "size_in_bytes": 7873}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_version.cpython-310.pyc", "path_type": "hardlink", "sha256": "d661262adf842a23d1ef729f946f4be09cdf116d28e885720bd0f3d7f6cec188", "sha256_in_prefix": "d661262adf842a23d1ef729f946f4be09cdf116d28e885720bd0f3d7f6cec188", "size_in_bytes": 2448}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/test_versionpredicate.cpython-310.pyc", "path_type": "hardlink", "sha256": "7c5db8a93e4805269221f214e0c24c85729446eced52317f0e61c32d5ac2f109", "sha256_in_prefix": "7c5db8a93e4805269221f214e0c24c85729446eced52317f0e61c32d5ac2f109", "size_in_bytes": 163}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/__pycache__/unix_compat.cpython-310.pyc", "path_type": "hardlink", "sha256": "3ce40ff074dc8dc9b79e38fda668230ddbd129a5b020f9b4ad0ccc4f8ee7cb68", "sha256_in_prefix": "3ce40ff074dc8dc9b79e38fda668230ddbd129a5b020f9b4ad0ccc4f8ee7cb68", "size_in_bytes": 499}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "f21ee1abefae13f43c5de262e9fe2289682aa3096d1fa343409e46cebbcfb92f", "sha256_in_prefix": "f21ee1abefae13f43c5de262e9fe2289682aa3096d1fa343409e46cebbcfb92f", "size_in_bytes": 157}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/__pycache__/py39.cpython-310.pyc", "path_type": "hardlink", "sha256": "18c3164dddae11a07500c32432d2566f9d0786c3e3c605e5e046b5f1eb2fe760", "sha256_in_prefix": "18c3164dddae11a07500c32432d2566f9d0786c3e3c605e5e046b5f1eb2fe760", "size_in_bytes": 572}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/compat/py39.py", "path_type": "hardlink", "sha256": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "sha256_in_prefix": "b741814ccfb7d235fef7309f93094d045b73cda6de9b1eb4eb9989d1df7f551c", "size_in_bytes": 1026}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/support.py", "path_type": "hardlink", "sha256": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "sha256_in_prefix": "b63b18b32c6fa532b836b902b1e876ba3bc320657431ffdbe522397cfd93d323", "size_in_bytes": 4099}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "sha256_in_prefix": "8e8ce2992c0f045f89a097cdfef0da895199a7ae8135c5991a1df81655b9ec34", "size_in_bytes": 11787}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist.py", "path_type": "hardlink", "sha256": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "sha256_in_prefix": "c4d1f152c2e51ec6504709332dbfe2483db8b3ef4c93e357d9f7c15b03b23f27", "size_in_bytes": 1396}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py", "path_type": "hardlink", "sha256": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "sha256_in_prefix": "405d393073613ce759ca1f3c5e9c3c2ac3bae2cee9445925f0a2fe4685785cad", "size_in_bytes": 2247}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py", "path_type": "hardlink", "sha256": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "sha256_in_prefix": "1dd9bea705a0c9aa067466c470665af1c461194e39cbc8072bcba639a9d38e29", "size_in_bytes": 3932}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build.py", "path_type": "hardlink", "sha256": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "sha256_in_prefix": "2496395e9399728db9658d29b2dc65fa223c987b163f4ba37f9a3c68eb6e6586", "size_in_bytes": 1742}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "sha256_in_prefix": "328d5915be02d555c160e1af9da965c0ded80a74edaf6e1a90b0cef198b80ac6", "size_in_bytes": 4331}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "4053bda98561596749bb5ec75dce31f513272d99619349401e2f47569a5bb97e", "sha256_in_prefix": "4053bda98561596749bb5ec75dce31f513272d99619349401e2f47569a5bb97e", "size_in_bytes": 22545}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_py.py", "path_type": "hardlink", "sha256": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "sha256_in_prefix": "36c7e646ba2338705734ca9647f9a9e60e0f2d3823843ee264551f7c664521dc", "size_in_bytes": 6882}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_build_scripts.py", "path_type": "hardlink", "sha256": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "sha256_in_prefix": "703f85472fa85f9e6c5d15f9133e7140269e1eb59a8f229ce17bb0bf67dee3cc", "size_in_bytes": 2880}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_check.py", "path_type": "hardlink", "sha256": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "sha256_in_prefix": "847495d3ba9fed8a12c46b136dbb1443db6cb19cf945135d6eb635b364b06852", "size_in_bytes": 6226}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_clean.py", "path_type": "hardlink", "sha256": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "sha256_in_prefix": "84f1fa8df22918552bbd66c5d6dc6488d55235a031b76c2ae578d5e3df733b81", "size_in_bytes": 1240}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_cmd.py", "path_type": "hardlink", "sha256": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "sha256_in_prefix": "6e0441efd9a2b6838a4753a2c991e70a882f1b1b77a56931793a880b4e254164", "size_in_bytes": 3254}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_config_cmd.py", "path_type": "hardlink", "sha256": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "sha256_in_prefix": "66ce965f421fc43be6b82d7d5f3b953676029d3afd63e865ef74c09834813786", "size_in_bytes": 2664}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_core.py", "path_type": "hardlink", "sha256": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "sha256_in_prefix": "2fb5ca540c5af8c1a8019780368a67b8af5f44a9de621912429830f1742f705f", "size_in_bytes": 3829}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_dir_util.py", "path_type": "hardlink", "sha256": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "sha256_in_prefix": "13ce250be938ae2554c1447259a43426ac76ba2dbe8a8fb446e25adcceea909b", "size_in_bytes": 4500}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_dist.py", "path_type": "hardlink", "sha256": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "sha256_in_prefix": "6bac257397d025de6a43a1ce9ddcdcba93618d3c6f8fafbf24bb69b98bda3f53", "size_in_bytes": 18793}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_extension.py", "path_type": "hardlink", "sha256": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "sha256_in_prefix": "f987a32e0642bb2705ace05deb8a551f426fc0c73d3708731ef431bef8d71ea9", "size_in_bytes": 3670}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_file_util.py", "path_type": "hardlink", "sha256": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "sha256_in_prefix": "962be39e5dc592295096b076ac574542af67be3115647ca73726b46dfceffdbe", "size_in_bytes": 3522}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_filelist.py", "path_type": "hardlink", "sha256": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "sha256_in_prefix": "ac9c24a8251f9060e05a50f6d93a33b13f3271bba930707c0d7a93873c13d53e", "size_in_bytes": 10766}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install.py", "path_type": "hardlink", "sha256": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "sha256_in_prefix": "4df081d32921231c9d202d90e12b93019cd21efb5e30782b04bf708684a02bd4", "size_in_bytes": 8618}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_data.py", "path_type": "hardlink", "sha256": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "sha256_in_prefix": "bcaab72bdee4d210409ce837f279b011d7fb7040d5afdad357209e2689606f80", "size_in_bytes": 2464}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_headers.py", "path_type": "hardlink", "sha256": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "sha256_in_prefix": "3d5018a68fed625f7cd107fae033ce9a64afc9e7c81dd534e9fed5b09799ca41", "size_in_bytes": 936}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_lib.py", "path_type": "hardlink", "sha256": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "sha256_in_prefix": "aab8ba465fa668d4d0acd0d5f036de5cd974863b1f4482a2238adf64bae65812", "size_in_bytes": 3612}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "sha256_in_prefix": "284defd1c0e4156fbdd083880fe3a665918cda6872f99904bae5bb5174b6487c", "size_in_bytes": 1600}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_log.py", "path_type": "hardlink", "sha256": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "sha256_in_prefix": "8ac16d3ae7e5a02c84759690395edc554af8e86c2d755323e37986041e571fb9", "size_in_bytes": 323}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_modified.py", "path_type": "hardlink", "sha256": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "sha256_in_prefix": "875fbe6ce5a6b49a356e9555eae4617674bd6ebef508188d0ccd4c0f0486a6e8", "size_in_bytes": 4221}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_sdist.py", "path_type": "hardlink", "sha256": "71fcd4865080e35f3ed6f1fdb5adc806cdba73f8d405b909a0538ae469c0c8d9", "sha256_in_prefix": "71fcd4865080e35f3ed6f1fdb5adc806cdba73f8d405b909a0538ae469c0c8d9", "size_in_bytes": 15062}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_spawn.py", "path_type": "hardlink", "sha256": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "sha256_in_prefix": "792f30f43edb4f1c852d2c916a12567ae87c29cd45f11596898fdd486e41e417", "size_in_bytes": 4803}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_sysconfig.py", "path_type": "hardlink", "sha256": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "sha256_in_prefix": "97133c2ec522d53a268c35781e860af2ee6752806478d2fad14abc3d8d437305", "size_in_bytes": 11986}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_text_file.py", "path_type": "hardlink", "sha256": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "sha256_in_prefix": "59059207901f7410d968c03c045822a493e7b096ffd9228c7cbf747d291156dc", "size_in_bytes": 3460}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_util.py", "path_type": "hardlink", "sha256": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "sha256_in_prefix": "1fdce5678cf8561e137e33580c1b313fbc20b902e9c427c963239c9b5c995377", "size_in_bytes": 7988}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_version.py", "path_type": "hardlink", "sha256": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "sha256_in_prefix": "6a17e0fe63fcc11cb5b20c18fbf3f1e61ae381febfba94c8a670a0a51e325919", "size_in_bytes": 2750}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/test_versionpredicate.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_distutils/tests/unix_compat.py", "path_type": "hardlink", "sha256": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "sha256_in_prefix": "cfea29e82da255d5f56aae4120147b72a3b18a3284f7b6a537026a1cd74ba682", "size_in_bytes": 386}, {"_path": "Lib/site-packages/setuptools/_distutils/text_file.py", "path_type": "hardlink", "sha256": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "sha256_in_prefix": "cf876438906bf41a362c6d1336a9bcb03eb72c411a29248fd09d1b581ac51b77", "size_in_bytes": 12101}, {"_path": "Lib/site-packages/setuptools/_distutils/unixccompiler.py", "path_type": "hardlink", "sha256": "d5b5c9587e1f8aefc0d967eb887cdff3cc639654135e79e352465d44ab3d7165", "sha256_in_prefix": "d5b5c9587e1f8aefc0d967eb887cdff3cc639654135e79e352465d44ab3d7165", "size_in_bytes": 212}, {"_path": "Lib/site-packages/setuptools/_distutils/util.py", "path_type": "hardlink", "sha256": "3637e7aa4eb4ccc7648808d19c6713597dede3dfa86c76a93a56cdbf2225d362", "sha256_in_prefix": "3637e7aa4eb4ccc7648808d19c6713597dede3dfa86c76a93a56cdbf2225d362", "size_in_bytes": 18094}, {"_path": "Lib/site-packages/setuptools/_distutils/version.py", "path_type": "hardlink", "sha256": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "sha256_in_prefix": "bc8993e7e1025e4436d6828bd17605893a8ae8dc8cd3d729cc136803fdf80905", "size_in_bytes": 12619}, {"_path": "Lib/site-packages/setuptools/_distutils/versionpredicate.py", "path_type": "hardlink", "sha256": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "sha256_in_prefix": "a81590eb04e3d76383cada13988c9d79f218da36f8b98d6c75b81bb8b9fe2093", "size_in_bytes": 5205}, {"_path": "Lib/site-packages/setuptools/_distutils/zosccompiler.py", "path_type": "hardlink", "sha256": "b2f7625d9da475cc0aac929f8548626f4df2f20cfb68664aba45c7dc8ed89017", "sha256_in_prefix": "b2f7625d9da475cc0aac929f8548626f4df2f20cfb68664aba45c7dc8ed89017", "size_in_bytes": 58}, {"_path": "Lib/site-packages/setuptools/_entry_points.py", "path_type": "hardlink", "sha256": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "sha256_in_prefix": "63741413d24a156fd8caab839e97df3564ace9fde3284b757be767c7efbdf8ac", "size_in_bytes": 2310}, {"_path": "Lib/site-packages/setuptools/_imp.py", "path_type": "hardlink", "sha256": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "sha256_in_prefix": "618d448d910dfb4cd8722d5cc4ed7407f69d0043abee2f1e2bc26809cf487ab7", "size_in_bytes": 2435}, {"_path": "Lib/site-packages/setuptools/_importlib.py", "path_type": "hardlink", "sha256": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "sha256_in_prefix": "68a22370ad07297373d83f974ebc5a8b11a7fe3b9390e3709aeddd72178c385d", "size_in_bytes": 223}, {"_path": "Lib/site-packages/setuptools/_itertools.py", "path_type": "hardlink", "sha256": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "sha256_in_prefix": "8d645fb08ae90bb9b2a28cf78435118fd1adbe9b3065e2978361da926121363a", "size_in_bytes": 657}, {"_path": "Lib/site-packages/setuptools/_normalization.py", "path_type": "hardlink", "sha256": "9009867ebc23179763c9d11f2cbc8a82391709b2ffd3f67150f3be0e52e59886", "sha256_in_prefix": "9009867ebc23179763c9d11f2cbc8a82391709b2ffd3f67150f3be0e52e59886", "size_in_bytes": 5824}, {"_path": "Lib/site-packages/setuptools/_path.py", "path_type": "hardlink", "sha256": "70fbf8d6fd371c3eee118a82228f84fdc1917e93d5af8972c010a22be1d2ac28", "sha256_in_prefix": "70fbf8d6fd371c3eee118a82228f84fdc1917e93d5af8972c010a22be1d2ac28", "size_in_bytes": 2685}, {"_path": "Lib/site-packages/setuptools/_reqs.py", "path_type": "hardlink", "sha256": "408dc2f6e38148d45c72edb4f2a3e78b11f1e759f10abcbbfe73d32096926313", "sha256_in_prefix": "408dc2f6e38148d45c72edb4f2a3e78b11f1e759f10abcbbfe73d32096926313", "size_in_bytes": 1438}, {"_path": "Lib/site-packages/setuptools/_shutil.py", "path_type": "hardlink", "sha256": "7003a595ca323135ece492e8c9b422dbdc88e6000193cda17a9272381bf66ccc", "sha256_in_prefix": "7003a595ca323135ece492e8c9b422dbdc88e6000193cda17a9272381bf66ccc", "size_in_bytes": 1496}, {"_path": "Lib/site-packages/setuptools/_static.py", "path_type": "hardlink", "sha256": "19347bf60112175fc968ae2dacb9290eb12e09e12d3e5c105b4311bfb54d417e", "sha256_in_prefix": "19347bf60112175fc968ae2dacb9290eb12e09e12d3e5c105b4311bfb54d417e", "size_in_bytes": 4855}, {"_path": "Lib/site-packages/setuptools/_vendor/__pycache__/typing_extensions.cpython-310.pyc", "path_type": "hardlink", "sha256": "b1b7b9eead15b6235fbf66a5f05b9c4a0d8178ba7b94bc8161543f323e459028", "sha256_in_prefix": "b1b7b9eead15b6235fbf66a5f05b9c4a0d8178ba7b94bc8161543f323e459028", "size_in_bytes": 100301}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "sha256_in_prefix": "ade78d04982d69972d444a8e14a94f87a2334dd3855cc80348ea8e240aa0df2d", "size_in_bytes": 7634}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "sha256_in_prefix": "3800d9b91dceea2065a6ed6279383362e97ac38b8e56b9343f404ee531860099", "size_in_bytes": 15006}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "sha256_in_prefix": "822bba66b41526fa547186b80221f85da50d652bee5493dbfe5d14085112f0c3", "size_in_bytes": 1308}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "sha256_in_prefix": "db07a93359e4e034b8785a58ad6d534ea3dca0635f1e184efe2e66e1c3a299ba", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand-2.2.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "sha256_in_prefix": "0337e180a292f04740c16513485f2681e5506d7398f64a241c1ea44aac30aaed", "size_in_bytes": 12}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__init__.py", "path_type": "hardlink", "sha256": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "sha256_in_prefix": "ce4a39467be896f6fe5178c2c7fd80acf4c6056c142b9418e0b21020a611ec0b", "size_in_bytes": 1037}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "6297d94e2bf165d4aadd7a5e287a9993a1ea0ac6617a620314573e0359fa6e10", "sha256_in_prefix": "6297d94e2bf165d4aadd7a5e287a9993a1ea0ac6617a620314573e0359fa6e10", "size_in_bytes": 348}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoasync.cpython-310.pyc", "path_type": "hardlink", "sha256": "953d70482fa7f2114aa30699e27df97f71e73205bb304084de6a39f9b4331366", "sha256_in_prefix": "953d70482fa7f2114aa30699e27df97f71e73205bb304084de6a39f9b4331366", "size_in_bytes": 4141}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autocommand.cpython-310.pyc", "path_type": "hardlink", "sha256": "577615645dda7551f26bfc2f39d29c4ad1b5e7506c8464db12f4766672c808ee", "sha256_in_prefix": "577615645dda7551f26bfc2f39d29c4ad1b5e7506c8464db12f4766672c808ee", "size_in_bytes": 993}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/automain.cpython-310.pyc", "path_type": "hardlink", "sha256": "af96e16107c8db023e7fbb5e14b85e97cf87b507f977027ad45d199db82021a2", "sha256_in_prefix": "af96e16107c8db023e7fbb5e14b85e97cf87b507f977027ad45d199db82021a2", "size_in_bytes": 1631}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/autoparse.cpython-310.pyc", "path_type": "hardlink", "sha256": "f9dcd2ef26e6d96b4f497c1c6dff8a46122d7f00ebfd2379a0fe7395528877ad", "sha256_in_prefix": "f9dcd2ef26e6d96b4f497c1c6dff8a46122d7f00ebfd2379a0fe7395528877ad", "size_in_bytes": 8348}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/__pycache__/errors.cpython-310.pyc", "path_type": "hardlink", "sha256": "eeb738191874fdc1d18a50f1c78fcc3d09a84e9de48208ede9bcce1b0834c9a4", "sha256_in_prefix": "eeb738191874fdc1d18a50f1c78fcc3d09a84e9de48208ede9bcce1b0834c9a4", "size_in_bytes": 378}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autoasync.py", "path_type": "hardlink", "sha256": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "sha256_in_prefix": "00c772af1352e29a9625f3ffc6ea0e70898e1d60fea93ef1d3ac2628dd55a7e5", "size_in_bytes": 5680}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autocommand.py", "path_type": "hardlink", "sha256": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "sha256_in_prefix": "866904990ef61ed2f9e609d44558c33a7b1f62519de652d76ef4f8286e3de90c", "size_in_bytes": 2505}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/automain.py", "path_type": "hardlink", "sha256": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "sha256_in_prefix": "0366fc8bbe7833173f0e353d585afabea6035a5873d1c9fc9a2bbc77c12cc55f", "size_in_bytes": 2076}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/autoparse.py", "path_type": "hardlink", "sha256": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "sha256_in_prefix": "5955a66493dc6f350a5cfe34ada430ff41c3f2a3c9d95f551b57851669a7171c", "size_in_bytes": 11642}, {"_path": "Lib/site-packages/setuptools/_vendor/autocommand/errors.py", "path_type": "hardlink", "sha256": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "sha256_in_prefix": "eda6b7ae887d1deaddea720aa501cd584b25584f28abb1a21d8554b91a8e4670", "size_in_bytes": 886}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "sha256_in_prefix": "8215c54ead77d9dc5a108a25c6bdc72b5999aa6f62c9499a440359412afa5a51", "size_in_bytes": 2020}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "sha256_in_prefix": "258a1f1c849e1175069a55a5d6ce357afdd04e34cd5de27093e4acec7a9d2ce1", "size_in_bytes": 1360}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/backports.tarfile-1.2.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "sha256_in_prefix": "7068da2cc3a8051d452b4029a23b73595995893b49ec91882bf1f05e212cbed5", "size_in_bytes": 10}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/__init__.py", "path_type": "hardlink", "sha256": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "sha256_in_prefix": "88e10cc2794e4567b374ef3edafc4120f491dfb0fb2468e5b99f1fe79bf3c65b", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "13cbdafb99a84e7f0df7b59762570c3ccc6b94f07a3edfe9658a582e8a3fd0e7", "sha256_in_prefix": "13cbdafb99a84e7f0df7b59762570c3ccc6b94f07a3edfe9658a582e8a3fd0e7", "size_in_bytes": 218}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__init__.py", "path_type": "hardlink", "sha256": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "sha256_in_prefix": "3f07f6a9421f0744a89493c229cc77bf3dd412efda89db38838b007f1cbde2a8", "size_in_bytes": 108491}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__main__.py", "path_type": "hardlink", "sha256": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "sha256_in_prefix": "630da8193d5a7ebcf6781b24cdd3d82fc45e07fde5880a6684590dd846c399ce", "size_in_bytes": 59}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "4278227bd6878beac2787f0eea357c803193c0f507e103845be376127f071ab5", "sha256_in_prefix": "4278227bd6878beac2787f0eea357c803193c0f507e103845be376127f071ab5", "size_in_bytes": 71890}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/__pycache__/__main__.cpython-310.pyc", "path_type": "hardlink", "sha256": "e7a2423dc1742fd5100c44bb7de48bdaa4ebd6fa0c75517738f1a1a6d646951c", "sha256_in_prefix": "e7a2423dc1742fd5100c44bb7de48bdaa4ebd6fa0c75517738f1a1a6d646951c", "size_in_bytes": 232}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "1971e26ca1ebc9fc7ca746eea8aec5540b28ed8286a4cf58f2c7fa5e9346970b", "sha256_in_prefix": "1971e26ca1ebc9fc7ca746eea8aec5540b28ed8286a4cf58f2c7fa5e9346970b", "size_in_bytes": 166}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/__pycache__/py38.cpython-310.pyc", "path_type": "hardlink", "sha256": "9094820ee8eebac96f9717f41ab0d7b45cf2e887777ea66b01a1ff470d3fbe87", "sha256_in_prefix": "9094820ee8eebac96f9717f41ab0d7b45cf2e887777ea66b01a1ff470d3fbe87", "size_in_bytes": 748}, {"_path": "Lib/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py", "path_type": "hardlink", "sha256": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "sha256_in_prefix": "898932b7f82f5a32f31944c90fd4ee4df30c8ce93e7abb17666465bd060ddaa1", "size_in_bytes": 568}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "sha256_in_prefix": "6a7b90effee1e09d5b484cdf7232016a43e2d9cc9543bcbb8e494b1ec05e1f59", "size_in_bytes": 4648}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "sha256_in_prefix": "0d8d3c6eeb9ebbe86cac7d60861552433c329da9ea51248b61d02be2e5e64030", "size_in_bytes": 2518}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "sha256_in_prefix": "9a0b8c95618c5fe5479cca4a3a38d089d228d6cb1194216ee1ae26069cf5b363", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "sha256_in_prefix": "08eddf0fdcb29403625e4acca38a872d5fe6a972f6b02e4914a82dd725804fe0", "size_in_bytes": 19}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__init__.py", "path_type": "hardlink", "sha256": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "sha256_in_prefix": "b59341fb6de1f018b18bdb82ad0aa3f587f469e0bef89a2c772dc8651210781d", "size_in_bytes": 33798}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "900bda6e9f7ff8b791ef8bb39572743e4b8c5e5d19b3889c3340d19995ea1d2b", "sha256_in_prefix": "900bda6e9f7ff8b791ef8bb39572743e4b8c5e5d19b3889c3340d19995ea1d2b", "size_in_bytes": 40228}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_adapters.cpython-310.pyc", "path_type": "hardlink", "sha256": "a2b49579ea447e06d37af1111610643a923f22a51b06978b115d2df834b1cd25", "sha256_in_prefix": "a2b49579ea447e06d37af1111610643a923f22a51b06978b115d2df834b1cd25", "size_in_bytes": 2846}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_collections.cpython-310.pyc", "path_type": "hardlink", "sha256": "c7d2732c840495044b8bb5870bae9c9bead5358166ed93da9ea711b2912e148d", "sha256_in_prefix": "c7d2732c840495044b8bb5870bae9c9bead5358166ed93da9ea711b2912e148d", "size_in_bytes": 1536}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_compat.cpython-310.pyc", "path_type": "hardlink", "sha256": "5f1018a59139647b803b16a8a383518cf3a917a8395ad4a4dbe4a1d45d891a6b", "sha256_in_prefix": "5f1018a59139647b803b16a8a383518cf3a917a8395ad4a4dbe4a1d45d891a6b", "size_in_bytes": 1868}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_functools.cpython-310.pyc", "path_type": "hardlink", "sha256": "ce77138c3d7fb407a5ea1a46908004b44cf319e2a5d7320e33ca35781d71d9a9", "sha256_in_prefix": "ce77138c3d7fb407a5ea1a46908004b44cf319e2a5d7320e33ca35781d71d9a9", "size_in_bytes": 3126}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_itertools.cpython-310.pyc", "path_type": "hardlink", "sha256": "a6f74c3f72d53f10405bc5b6fbb120d4a782b9a3c830c196eb64ffffd48a8dfe", "sha256_in_prefix": "a6f74c3f72d53f10405bc5b6fbb120d4a782b9a3c830c196eb64ffffd48a8dfe", "size_in_bytes": 2007}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_meta.cpython-310.pyc", "path_type": "hardlink", "sha256": "24e50be021a9ff694f710d61a5a58c5448abc7f545ca47801c65bd7a2aca0482", "sha256_in_prefix": "24e50be021a9ff694f710d61a5a58c5448abc7f545ca47801c65bd7a2aca0482", "size_in_bytes": 3333}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/_text.cpython-310.pyc", "path_type": "hardlink", "sha256": "d285e88f42f79c56155d478558191e305447be0613b4948ed42e6afc9bc74bcb", "sha256_in_prefix": "d285e88f42f79c56155d478558191e305447be0613b4948ed42e6afc9bc74bcb", "size_in_bytes": 3059}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/__pycache__/diagnose.cpython-310.pyc", "path_type": "hardlink", "sha256": "de583baedef2b4cb6842e19feec7c9af01ac0713feadae83b1b458ad57c7ba9a", "sha256_in_prefix": "de583baedef2b4cb6842e19feec7c9af01ac0713feadae83b1b458ad57c7ba9a", "size_in_bytes": 828}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py", "path_type": "hardlink", "sha256": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "sha256_in_prefix": "ac88564f006f600d5b57b8bee457d55f7f2a1170d35c5792e5c6f9c49b4fde4b", "size_in_bytes": 2317}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_collections.py", "path_type": "hardlink", "sha256": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "sha256_in_prefix": "089d0e4c21c88d6034648552e2fa0e440b27d91e11d9c40112d3ec6442690126", "size_in_bytes": 743}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_compat.py", "path_type": "hardlink", "sha256": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "sha256_in_prefix": "ef740aacdf4a368699ce16d7e723c20996be15a00bc177bc4cf94347bd898015", "size_in_bytes": 1314}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_functools.py", "path_type": "hardlink", "sha256": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "sha256_in_prefix": "3ec636fb8aeb297e1155e442d681a9d65075a660bd78a37cf3f7fe6c3f6e3a80", "size_in_bytes": 2895}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py", "path_type": "hardlink", "sha256": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "sha256_in_prefix": "72faffdaff0145bc5c225e71e6575fa9d1e3848f188bcb3cca4e741bf9e6ea34", "size_in_bytes": 2068}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_meta.py", "path_type": "hardlink", "sha256": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "sha256_in_prefix": "9f167b0bc19595c04500a5b254e9ff767ee8b7fb7005c6e6d4d9af8c87ad0472", "size_in_bytes": 1801}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/_text.py", "path_type": "hardlink", "sha256": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "sha256_in_prefix": "1c2b0592c66924b7933f734493f9e0ac079755146d4ebb7287d78e001a113f80", "size_in_bytes": 2166}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "b2ad906ce008e82dd51c3d65f924382a5499fe49f4e1a115fe0f83dc48542a77", "sha256_in_prefix": "b2ad906ce008e82dd51c3d65f924382a5499fe49f4e1a115fe0f83dc48542a77", "size_in_bytes": 167}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py311.cpython-310.pyc", "path_type": "hardlink", "sha256": "207a1613445502285feb0d5f2ea7e9563a08db0615161a5e2a79cc11685906f6", "sha256_in_prefix": "207a1613445502285feb0d5f2ea7e9563a08db0615161a5e2a79cc11685906f6", "size_in_bytes": 1009}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/__pycache__/py39.cpython-310.pyc", "path_type": "hardlink", "sha256": "841738b0ae3235f6d0e1047d9fa758a23a167874e7e25ed330e1e463fec799a7", "sha256_in_prefix": "841738b0ae3235f6d0e1047d9fa758a23a167874e7e25ed330e1e463fec799a7", "size_in_bytes": 1162}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py", "path_type": "hardlink", "sha256": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "sha256_in_prefix": "baa9be2beba88728f5d38d931f86bd12bfc8e68efaebb0efba5703fa00bf7d20", "size_in_bytes": 608}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py", "path_type": "hardlink", "sha256": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "sha256_in_prefix": "70f90cbfafb48a52bed09c3f4e49f4c586ce28965ce1624a407a30d1cd93e38c", "size_in_bytes": 1102}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py", "path_type": "hardlink", "sha256": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "sha256_in_prefix": "9e4491322a309669212d884a86f0a0f60966b7fd750a8c7e1262f311ba984daf", "size_in_bytes": 379}, {"_path": "Lib/site-packages/setuptools/_vendor/importlib_metadata/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "sha256_in_prefix": "66030d634580651b3e53cc19895d9231f8d22aa06b327817c8332cfc20303308", "size_in_bytes": 21079}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "sha256_in_prefix": "5d7834ac1ba2612c6801050fde57a7b98b0f36acf88c3c2d4f376fd8911b3091", "size_in_bytes": 943}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "sha256_in_prefix": "cb8997f92397e1f6089289ec0060393743b2fbcfe0238157c391cd235c6abd68", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect-7.3.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "sha256_in_prefix": "9b9dae8dda75d02a93ea38755d0c594fa9049ed727bfeed397b52218d4f35990", "size_in_bytes": 8}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/__init__.py", "path_type": "hardlink", "sha256": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "sha256_in_prefix": "271cb51c95d9899f3990778b021926bf3e84313745a817be76ebeddf847a20e7", "size_in_bytes": 103796}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "66f0d9feb88fa3954f042fc1177ba891ae5711942fdfca6bc17a157402fda849", "sha256_in_prefix": "66f0d9feb88fa3954f042fc1177ba891ae5711942fdfca6bc17a157402fda849", "size_in_bytes": 74038}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "2a3c653d36558d58c489d6194670e0965c9110539dc1bb2c0452f0c277dd3c8a", "sha256_in_prefix": "2a3c653d36558d58c489d6194670e0965c9110539dc1bb2c0452f0c277dd3c8a", "size_in_bytes": 156}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/__pycache__/py38.cpython-310.pyc", "path_type": "hardlink", "sha256": "dd5b7c583ae5d4800290b1b9e282284f5adca032e4ac7aab4042f782d19e2cc9", "sha256_in_prefix": "dd5b7c583ae5d4800290b1b9e282284f5adca032e4ac7aab4042f782d19e2cc9", "size_in_bytes": 282}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/compat/py38.py", "path_type": "hardlink", "sha256": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "sha256_in_prefix": "a0e6d57d59d65fdfcea673ae1099f59856c9c55870c91e5ea5b8933570c36aca", "size_in_bytes": 160}, {"_path": "Lib/site-packages/setuptools/_vendor/inflect/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "sha256_in_prefix": "20c51a96236c0395f53b1f4c5d458e6a0721e51e16c1bff733b7aba76f5d06d8", "size_in_bytes": 3933}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "sha256_in_prefix": "1e9b62bd70e4a5fa26e9594cbb80860ffeca3debfee8773daefa774cd259ca06", "size_in_bytes": 873}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "sha256_in_prefix": "31d8bd3c3370119a6d3a34e551c02d87b5c90c5b4aac761a40c3ee9597810a24", "size_in_bytes": 91}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.collections-5.1.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "sha256_in_prefix": "c43b60b897a3d2d37d8845c252fc44261d9aef171e21154111a9012d2afffed6", "size_in_bytes": 4020}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "sha256_in_prefix": "55197b88a78443297bb2d827a75baae740b33896251d872835d4b4c75ec2f57e", "size_in_bytes": 641}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.context-5.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "sha256_in_prefix": "8b86946900d7fa38dd1102b9c1ebe17a0cb1f09c8b7e29f61f2bda4a4dc51eca", "size_in_bytes": 2891}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "sha256_in_prefix": "632aa7c04f7c4bcc01c027af5b9bc76fe8958f4a181035b957a3bd3014ba248b", "size_in_bytes": 843}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.functools-4.0.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "sha256_in_prefix": "03359d9ba56231f0ce3e840c7cb5a7db380141218949ccaa78ddbd4dcb965d52", "size_in_bytes": 3658}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "sha256_in_prefix": "816d945741dca246099388ca3eed74fc0667acbaa36f70b559b2494c3979b1f6", "size_in_bytes": 1500}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco.text-3.12.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "sha256_in_prefix": "d099cddcb7d71f82c845f5cbf9014e18227341664edc42f1e11d5dfe5a2ea103", "size_in_bytes": 7}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/__pycache__/context.cpython-310.pyc", "path_type": "hardlink", "sha256": "53b7f0479c3d914291a7a2a6e366cf51171deb3595ecab3d855483a5fa61ca66", "sha256_in_prefix": "53b7f0479c3d914291a7a2a6e366cf51171deb3595ecab3d855483a5fa61ca66", "size_in_bytes": 11057}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/__init__.py", "path_type": "hardlink", "sha256": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "sha256_in_prefix": "3dcd7e4aa8d69bcd5a7753f4f86b6da64c0efcb5a59da63a814abc81c2a1dafd", "size_in_bytes": 26640}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "a9227972e3affb63a5e994880752de1800be89ca459dac13c95ffbfdf320e7cf", "sha256_in_prefix": "a9227972e3affb63a5e994880752de1800be89ca459dac13c95ffbfdf320e7cf", "size_in_bytes": 32198}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/collections/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/context.py", "path_type": "hardlink", "sha256": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "sha256_in_prefix": "444a0b2310e43b931f118a30b7f5a8ba9342468c414b9bfb617d8f6d6f2521cb", "size_in_bytes": 9552}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.py", "path_type": "hardlink", "sha256": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "sha256_in_prefix": "844009692dae49946e17f258e02c421c8621efd669c5a3e9f4e887cabf44275c", "size_in_bytes": 16642}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__init__.pyi", "path_type": "hardlink", "sha256": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "sha256_in_prefix": "824dddb201f3a3917f53be07cc0be9362bc500f0a43c9d5bdbec8277ad9d7e7c", "size_in_bytes": 3878}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "1ac5837485a1cc8ae7be5605bd4c63aa0b367ea579f89a4cffb67e49ba9ecdeb", "sha256_in_prefix": "1ac5837485a1cc8ae7be5605bd4c63aa0b367ea579f89a4cffb67e49ba9ecdeb", "size_in_bytes": 19214}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/functools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/Lorem ipsum.txt", "path_type": "hardlink", "sha256": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "sha256_in_prefix": "37fedcffbf73c4eb9f058f47677cb33203a436ff9390e4d38a8e01c9dad28e0b", "size_in_bytes": 1335}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__init__.py", "path_type": "hardlink", "sha256": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "sha256_in_prefix": "636614a9747fa2b5280da6384a43d17ba65985be4750707f021f5108db15ca1a", "size_in_bytes": 16250}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "6591ffc91b8fbef7a0bb9be0c1a42c1f8688d86fc195aaef79023719e39a9a97", "sha256_in_prefix": "6591ffc91b8fbef7a0bb9be0c1a42c1f8688d86fc195aaef79023719e39a9a97", "size_in_bytes": 20433}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/layouts.cpython-310.pyc", "path_type": "hardlink", "sha256": "63e97e6730f1a3bcf247cd5a038143f14d1ce666d9e66543408586034ecd20f8", "sha256_in_prefix": "63e97e6730f1a3bcf247cd5a038143f14d1ce666d9e66543408586034ecd20f8", "size_in_bytes": 869}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/show-newlines.cpython-310.pyc", "path_type": "hardlink", "sha256": "e5bf4ed9a3bde41850fa52b35ff266d8f21c1ba3569f2e44c2bec03921ac43fb", "sha256_in_prefix": "e5bf4ed9a3bde41850fa52b35ff266d8f21c1ba3569f2e44c2bec03921ac43fb", "size_in_bytes": 1091}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/strip-prefix.cpython-310.pyc", "path_type": "hardlink", "sha256": "c3c716f153398e09a0bdfd0e673900c80e727a653ac0483f0f7fbb9b119df111", "sha256_in_prefix": "c3c716f153398e09a0bdfd0e673900c80e727a653ac0483f0f7fbb9b119df111", "size_in_bytes": 639}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-dvorak.cpython-310.pyc", "path_type": "hardlink", "sha256": "946ef5d8dbd1416f6af83c812e7a64c597a1b4e2c700506175b3e74484027065", "sha256_in_prefix": "946ef5d8dbd1416f6af83c812e7a64c597a1b4e2c700506175b3e74484027065", "size_in_bytes": 295}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/__pycache__/to-qwerty.cpython-310.pyc", "path_type": "hardlink", "sha256": "6e7be1f422cccd74a3d40c5ff34fcccec4cbed23e3341ff519605dc9bb9d36bf", "sha256_in_prefix": "6e7be1f422cccd74a3d40c5ff34fcccec4cbed23e3341ff519605dc9bb9d36bf", "size_in_bytes": 295}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/layouts.py", "path_type": "hardlink", "sha256": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "sha256_in_prefix": "1d30bc6924cb67bb978a9c8e5daa51302d79f23b9e7232ba455c22b5f999f7fc", "size_in_bytes": 643}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py", "path_type": "hardlink", "sha256": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "sha256_in_prefix": "58641aeb97bc97285bf762d438ba959fa29a0ada1e560570b3a5ad49198b93ac", "size_in_bytes": 904}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py", "path_type": "hardlink", "sha256": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "sha256_in_prefix": "35f55757c255368ea7a9cb980127cc57bff2e04a3cccc42a942386bc09d1215c", "size_in_bytes": 412}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py", "path_type": "hardlink", "sha256": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "sha256_in_prefix": "d5235c6d2b2f212a575e0f8b9f26c81c763e45414e42bdfacdc1e4635a5616ac", "size_in_bytes": 119}, {"_path": "Lib/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py", "path_type": "hardlink", "sha256": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "sha256_in_prefix": "b3850c4149cfc059ff741e6e642dbb06eac7390718a277417f322954be69133c", "size_in_bytes": 119}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "sha256_in_prefix": "09f1c8c9e941af3e584d59641ea9b87d83c0cb0fd007eb5ef391a7e2643c1a46", "size_in_bytes": 1053}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "sha256_in_prefix": "0453bdd0ef9f2cd89540ca63ee8212e73b73809514419dd3037d8fe471f737e0", "size_in_bytes": 36293}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "sha256_in_prefix": "77c8e73e018dc0fd7e9ed6c80b05a4404545f641fb085220ce42b368b59aa3d3", "size_in_bytes": 1259}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools-10.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "sha256_in_prefix": "ad282afc9a4717d7c7475971e77ab083fd7ed8bca9644fea99cb976d552af78f", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.py", "path_type": "hardlink", "sha256": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "sha256_in_prefix": "76d01b1a34c39a7fe08625396177e1c83cb4a610255d576de649590397d46be4", "size_in_bytes": 149}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__init__.pyi", "path_type": "hardlink", "sha256": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "sha256_in_prefix": "e41dde4f338dd4106e38ba1bd6f09f97211bda549deaeb17410f82bfe85791e0", "size_in_bytes": 43}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "70a942a4b3de826dbc7c3f5545307a4c23dd1ee347ae709f22783998c4898f53", "sha256_in_prefix": "70a942a4b3de826dbc7c3f5545307a4c23dd1ee347ae709f22783998c4898f53", "size_in_bytes": 298}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/more.cpython-310.pyc", "path_type": "hardlink", "sha256": "90aeb8a8723a5c5930fbc93b8ca4921c289939ae7842fb0d8fc1eaf898e0612a", "sha256_in_prefix": "90aeb8a8723a5c5930fbc93b8ca4921c289939ae7842fb0d8fc1eaf898e0612a", "size_in_bytes": 138258}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/__pycache__/recipes.cpython-310.pyc", "path_type": "hardlink", "sha256": "dd88ea137398375a6a1cea6666b45ca86f0b2ade59881623e7677b8cccb8b24a", "sha256_in_prefix": "dd88ea137398375a6a1cea6666b45ca86f0b2ade59881623e7677b8cccb8b24a", "size_in_bytes": 29191}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/more.py", "path_type": "hardlink", "sha256": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "sha256_in_prefix": "d44e64cc59dc44a4c3c34718bf5c915cc80376e9ecb4b41dd504ad7272fa53dd", "size_in_bytes": 148370}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/more.pyi", "path_type": "hardlink", "sha256": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "sha256_in_prefix": "8975deaade3c3717bc5469885a99155ee2a947615836ebb60d4f2740b5820aed", "size_in_bytes": 21484}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.py", "path_type": "hardlink", "sha256": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "sha256_in_prefix": "59e76185f846560aface28bc7c86c62559914f0d1929188b2a76010c626fe276", "size_in_bytes": 28591}, {"_path": "Lib/site-packages/setuptools/_vendor/more_itertools/recipes.pyi", "path_type": "hardlink", "sha256": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "sha256_in_prefix": "4ff99d1a970575facfdc94966f0cd83fd272355f86a3eed13dfa717dfb405a50", "size_in_bytes": 4617}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "sha256_in_prefix": "a211fceacea4e6621f4316364d2d0b7127c00de3856b8062082f9bc5957ea4db", "size_in_bytes": 3204}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "sha256_in_prefix": "6380eb5ccd0a63402b7f385b0046b52d814fc16dd612011e2f8882a977be03e3", "size_in_bytes": 2009}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging-24.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__init__.py", "path_type": "hardlink", "sha256": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "sha256_in_prefix": "764e136bfbe67552716070dc7f286f40dc3c5773e0481a2628d5ea83e0f62436", "size_in_bytes": 494}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "04f7a019de9ddf521e4fa85487c82a85ae50e6122b6733dc57fac063e2d682e4", "sha256_in_prefix": "04f7a019de9ddf521e4fa85487c82a85ae50e6122b6733dc57fac063e2d682e4", "size_in_bytes": 481}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_elffile.cpython-310.pyc", "path_type": "hardlink", "sha256": "ff7658ecc6b6f2e876b31280b381df7dcf2744311ff52412b1d22ae3e52c63d6", "sha256_in_prefix": "ff7658ecc6b6f2e876b31280b381df7dcf2744311ff52412b1d22ae3e52c63d6", "size_in_bytes": 3356}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_manylinux.cpython-310.pyc", "path_type": "hardlink", "sha256": "e7f82ff813ee73078dce2d4303584c190ac55c89cc3d8160fcb27bc2343f795d", "sha256_in_prefix": "e7f82ff813ee73078dce2d4303584c190ac55c89cc3d8160fcb27bc2343f795d", "size_in_bytes": 6546}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_musllinux.cpython-310.pyc", "path_type": "hardlink", "sha256": "690dc823888ddfdf45f5de52fcb25b1a394e1c990730c472fb664aac221e29df", "sha256_in_prefix": "690dc823888ddfdf45f5de52fcb25b1a394e1c990730c472fb664aac221e29df", "size_in_bytes": 3405}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_parser.cpython-310.pyc", "path_type": "hardlink", "sha256": "bf83db9aa00e5ed84a51960445cbd1535176d84334e096da2762e9855bace1d1", "sha256_in_prefix": "bf83db9aa00e5ed84a51960445cbd1535176d84334e096da2762e9855bace1d1", "size_in_bytes": 9221}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_structures.cpython-310.pyc", "path_type": "hardlink", "sha256": "bd5d868b1004e1d2c97b4293cd284c12ae442c1c6aea5033f37bbafc05b902c5", "sha256_in_prefix": "bd5d868b1004e1d2c97b4293cd284c12ae442c1c6aea5033f37bbafc05b902c5", "size_in_bytes": 2663}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/_tokenizer.cpython-310.pyc", "path_type": "hardlink", "sha256": "0e21a6b92556e0f61090495b859418205096f0e604aabe6d7d4d06e27227a2c1", "sha256_in_prefix": "0e21a6b92556e0f61090495b859418205096f0e604aabe6d7d4d06e27227a2c1", "size_in_bytes": 5875}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/markers.cpython-310.pyc", "path_type": "hardlink", "sha256": "07d773e3eae167a8686d7979763cc85f384177c235e1b1b9e38b18e178a29203", "sha256_in_prefix": "07d773e3eae167a8686d7979763cc85f384177c235e1b1b9e38b18e178a29203", "size_in_bytes": 7842}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/metadata.cpython-310.pyc", "path_type": "hardlink", "sha256": "d0a0d20f0ab681d65056fccfbf4147bfdc0024452992d4e35b7a639448efb333", "sha256_in_prefix": "d0a0d20f0ab681d65056fccfbf4147bfdc0024452992d4e35b7a639448efb333", "size_in_bytes": 18706}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/requirements.cpython-310.pyc", "path_type": "hardlink", "sha256": "7cbc65ff77e39130acf32763297de1bda9899cc6da75739199f0ecb8d0886b5d", "sha256_in_prefix": "7cbc65ff77e39130acf32763297de1bda9899cc6da75739199f0ecb8d0886b5d", "size_in_bytes": 2875}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/specifiers.cpython-310.pyc", "path_type": "hardlink", "sha256": "2db8f658f86461f048ad340257e021334d29b9d3541e070530e20fad402a7403", "sha256_in_prefix": "2db8f658f86461f048ad340257e021334d29b9d3541e070530e20fad402a7403", "size_in_bytes": 31349}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/tags.cpython-310.pyc", "path_type": "hardlink", "sha256": "28393e57ae634b71e4544a1ac4b26271d5c7ea16499a772cc17a23a53ced82be", "sha256_in_prefix": "28393e57ae634b71e4544a1ac4b26271d5c7ea16499a772cc17a23a53ced82be", "size_in_bytes": 15175}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "85b6ad31b8617c1c360fba2071a827bea9d1d854bfa0489b10438ee0f8f3c2cb", "sha256_in_prefix": "85b6ad31b8617c1c360fba2071a827bea9d1d854bfa0489b10438ee0f8f3c2cb", "size_in_bytes": 4609}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/__pycache__/version.cpython-310.pyc", "path_type": "hardlink", "sha256": "55aa28e0b43e9e1652a1213a91b92111023a531d61c0001ad3d3b7a00c1a47e2", "sha256_in_prefix": "55aa28e0b43e9e1652a1213a91b92111023a531d61c0001ad3d3b7a00c1a47e2", "size_in_bytes": 14998}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_elffile.py", "path_type": "hardlink", "sha256": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "sha256_in_prefix": "71f940400904db9b738589aafda0a2ef641f6d3fed9fcf75b4fcdfa5b7873b01", "size_in_bytes": 3306}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "sha256_in_prefix": "be5e4e0a8cf8931f341f9af05ca7975a397d58d2121a6af86604e94cff6553d7", "size_in_bytes": 9612}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "sha256_in_prefix": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "size_in_bytes": 2694}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_parser.py", "path_type": "hardlink", "sha256": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "sha256_in_prefix": "b3f4ef4ef0cd2b436b336401dd529385d58533835cd0fe899e439b925dcc8e93", "size_in_bytes": 10236}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "sha256_in_prefix": "27abf91fb273bdbfa0f35c69ff640008ac0eecbc47400ea292bc8c53bcd7c0df", "size_in_bytes": 5273}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__init__.py", "path_type": "hardlink", "sha256": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "sha256_in_prefix": "d71e4cd671188dc83011b2edd1d5f0cf6ba48ebd7c0e20b30b4b2b690a89f96c", "size_in_bytes": 5715}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "0f3ceb0a0ed3895cc89bc1c0c22f6e026e2175b31c5a96a35143d2e9a1f85e92", "sha256_in_prefix": "0f3ceb0a0ed3895cc89bc1c0c22f6e026e2175b31c5a96a35143d2e9a1f85e92", "size_in_bytes": 2568}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/__pycache__/_spdx.cpython-310.pyc", "path_type": "hardlink", "sha256": "2e2e2865ab10be2fc6c1970fe097097c9882e678728d83b03346c1b3a37f5d61", "sha256_in_prefix": "2e2e2865ab10be2fc6c1970fe097097c9882e678728d83b03346c1b3a37f5d61", "size_in_bytes": 40940}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py", "path_type": "hardlink", "sha256": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "sha256_in_prefix": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "size_in_bytes": 48398}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/markers.py", "path_type": "hardlink", "sha256": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "sha256_in_prefix": "73cf5337307b65d198864a2f9ba3d89aa1b21f15e561568b5b9f753c750d283f", "size_in_bytes": 10561}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/metadata.py", "path_type": "hardlink", "sha256": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "sha256_in_prefix": "60989b33b1987b8adef3ed1adce9579864be5c38131283b8b6506ddaadb90678", "size_in_bytes": 34762}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/requirements.py", "path_type": "hardlink", "sha256": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "sha256_in_prefix": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "size_in_bytes": 2947}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/specifiers.py", "path_type": "hardlink", "sha256": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "sha256_in_prefix": "186d703cd31c2f47cc24eebcbc5e77c0a31dc277de84371a23eafd3694df8a50", "size_in_bytes": 40074}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/tags.py", "path_type": "hardlink", "sha256": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "sha256_in_prefix": "085aab2730337365cd19ec5eac7fff4fe639230abb59bb185ec88b1112d6c58d", "size_in_bytes": 21014}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/utils.py", "path_type": "hardlink", "sha256": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "sha256_in_prefix": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "size_in_bytes": 5050}, {"_path": "Lib/site-packages/setuptools/_vendor/packaging/version.py", "path_type": "hardlink", "sha256": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "sha256_in_prefix": "a257f2ba4fc33db7e5364278c0159eb57435edcef8c770c1e74d5d7a052fec36", "size_in_bytes": 16676}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "sha256_in_prefix": "ce6b227b4d46d4cb57474c2022fe57a557933bb89daf4596bdf9b12ac296b869", "size_in_bytes": 11429}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "sha256_in_prefix": "4c211d76d42ed40efc3acfcc866d8912a718afbca2b7e51849442366d6e99fe8", "size_in_bytes": 1642}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "sha256_in_prefix": "cc431c46bf4aaf4df1d68cc6c20e6ff4d4012a7de49dda7a2d2a1295583e8e15", "size_in_bytes": 87}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs-4.2.2.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "sha256_in_prefix": "29e0fd62e929850e86eb28c3fdccf0cefdf4fa94879011cffb3d0d4bed6d4db6", "size_in_bytes": 1089}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__init__.py", "path_type": "hardlink", "sha256": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "sha256_in_prefix": "10c184f2a787451f42cc316bf242f7b40f217596678988d705565dd1419358ad", "size_in_bytes": 22225}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__main__.py", "path_type": "hardlink", "sha256": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "sha256_in_prefix": "1e7b14407a6205a893c70726c15c3e9c568f755359b5021d8b57960ed23e3332", "size_in_bytes": 1493}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "efca589f51d47605f6ddd0c05240587aa7e599ecc3f68f5e39ca6c7aff0bd01c", "sha256_in_prefix": "efca589f51d47605f6ddd0c05240587aa7e599ecc3f68f5e39ca6c7aff0bd01c", "size_in_bytes": 15752}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/__main__.cpython-310.pyc", "path_type": "hardlink", "sha256": "53b5df0e47c833d33cf12ef22ec70696976ba14e8af1ea53c9f1bf77e4014ad8", "sha256_in_prefix": "53b5df0e47c833d33cf12ef22ec70696976ba14e8af1ea53c9f1bf77e4014ad8", "size_in_bytes": 1349}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/android.cpython-310.pyc", "path_type": "hardlink", "sha256": "c0f3bafa53b34593989590a1992587f4639bdeb060b666ae7f59d165c1a2f195", "sha256_in_prefix": "c0f3bafa53b34593989590a1992587f4639bdeb060b666ae7f59d165c1a2f195", "size_in_bytes": 7369}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/api.cpython-310.pyc", "path_type": "hardlink", "sha256": "7d2e79d8488966945856b9c8fb1718c6305b38334f357ef10e521d9af17307d2", "sha256_in_prefix": "7d2e79d8488966945856b9c8fb1718c6305b38334f357ef10e521d9af17307d2", "size_in_bytes": 9888}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/macos.cpython-310.pyc", "path_type": "hardlink", "sha256": "fee1b2f56d3ed4909b37f2db35b512bd3a2f1650222fb685e65fee0f230c1ead", "sha256_in_prefix": "fee1b2f56d3ed4909b37f2db35b512bd3a2f1650222fb685e65fee0f230c1ead", "size_in_bytes": 5854}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/unix.cpython-310.pyc", "path_type": "hardlink", "sha256": "5da368f9d3d96f38ba2ba825d2a5b3c445c924285434fabd5193aaa9a8d86ea3", "sha256_in_prefix": "5da368f9d3d96f38ba2ba825d2a5b3c445c924285434fabd5193aaa9a8d86ea3", "size_in_bytes": 10710}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/version.cpython-310.pyc", "path_type": "hardlink", "sha256": "1bc262504036a2842cc361280487c99e66996dd6eb8bec3ba899f9e066568fc8", "sha256_in_prefix": "1bc262504036a2842cc361280487c99e66996dd6eb8bec3ba899f9e066568fc8", "size_in_bytes": 476}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/__pycache__/windows.cpython-310.pyc", "path_type": "hardlink", "sha256": "5a83c13e1c9a32305b50b877f0f27da5d12843f9159140b1243d8646cf4c16e0", "sha256_in_prefix": "5a83c13e1c9a32305b50b877f0f27da5d12843f9159140b1243d8646cf4c16e0", "size_in_bytes": 9049}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/android.py", "path_type": "hardlink", "sha256": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "sha256_in_prefix": "c595d8f49778e963acc53d94ebee47b0db4367e210ab170452b04b977858938a", "size_in_bytes": 9016}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/api.py", "path_type": "hardlink", "sha256": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "sha256_in_prefix": "40161d51a736782e76d5e93fcb9dee0f50dcabe9495fc22049155de089c2eae7", "size_in_bytes": 8996}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/macos.py", "path_type": "hardlink", "sha256": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "sha256_in_prefix": "c1fb6c6ecbeaea767458e4574a20ab64d9111f3fd62ae92d9746ba982ecc1642", "size_in_bytes": 5580}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/unix.py", "path_type": "hardlink", "sha256": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "sha256_in_prefix": "09c8bd5aab77e5d00cb20e874fd9d11874815b9a1b6f4a51dc01352499ec0978", "size_in_bytes": 10643}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/version.py", "path_type": "hardlink", "sha256": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "sha256_in_prefix": "afb17bead6518e040aceba71fc8d3f64c40e314f8f4bb7869c70fbcc42b7281d", "size_in_bytes": 411}, {"_path": "Lib/site-packages/setuptools/_vendor/platformdirs/windows.py", "path_type": "hardlink", "sha256": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "sha256_in_prefix": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "size_in_bytes": 10125}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "sha256_in_prefix": "b80816b0d530b8accb4c2211783790984a6e3b61922c2b5ee92f3372ab2742fe", "size_in_bytes": 1072}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "sha256_in_prefix": "ccf0dc78a98fc0918b5ad67292b1e2c4bed65575a6246cd9d63c914f9942a0f2", "size_in_bytes": 8875}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "sha256_in_prefix": "0cb9f9a451a1e365ac54b4c88662e1da0cb54a72d16a5258fb0abff9d3e1c022", "size_in_bytes": 999}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli-2.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "sha256_in_prefix": "8cf311fc3ce47385f889c42d9b3f35967358fe402c7e883baf2eeaa11bd82d7c", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__init__.py", "path_type": "hardlink", "sha256": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "sha256_in_prefix": "26153057ae830758381efb7551009531d7c2bbe220015f055e6bc353da27c5de", "size_in_bytes": 396}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "ba0105c61697b66b5e695e19df812794d1b7537dcbd098e35978f8056a769674", "sha256_in_prefix": "ba0105c61697b66b5e695e19df812794d1b7537dcbd098e35978f8056a769674", "size_in_bytes": 313}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_parser.cpython-310.pyc", "path_type": "hardlink", "sha256": "d596458469f3184507198db39294fe35c271c0a499b65cb0985191d3dab2fc47", "sha256_in_prefix": "d596458469f3184507198db39294fe35c271c0a499b65cb0985191d3dab2fc47", "size_in_bytes": 17027}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_re.cpython-310.pyc", "path_type": "hardlink", "sha256": "b3db72ddda991afd6a4a23e08f251482688dd0e4ea586b1965b4eaa12a0f14b9", "sha256_in_prefix": "b3db72ddda991afd6a4a23e08f251482688dd0e4ea586b1965b4eaa12a0f14b9", "size_in_bytes": 2859}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/__pycache__/_types.cpython-310.pyc", "path_type": "hardlink", "sha256": "e74fe38958049a88ac2bcb492fb54059bfec6088fb50b62cf4f3c6968bd0f48a", "sha256_in_prefix": "e74fe38958049a88ac2bcb492fb54059bfec6088fb50b62cf4f3c6968bd0f48a", "size_in_bytes": 283}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_parser.py", "path_type": "hardlink", "sha256": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "sha256_in_prefix": "83df8435a00b4be07c768918a42bb35056a55a5a20ed3f922183232d9496aed3", "size_in_bytes": 22633}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_re.py", "path_type": "hardlink", "sha256": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "sha256_in_prefix": "75b8e0e428594f6dca6bdcfd0c73977ddb52a4fc147dd80c5e78fc34ea25cbec", "size_in_bytes": 2943}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/_types.py", "path_type": "hardlink", "sha256": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "sha256_in_prefix": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "size_in_bytes": 254}, {"_path": "Lib/site-packages/setuptools/_vendor/tomli/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "sha256_in_prefix": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "sha256_in_prefix": "6163f7987dfb38d6bc320ce2b70b2f02b862bc41126516d552ef1cd43247e758", "size_in_bytes": 1130}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "sha256_in_prefix": "cf675c1c0a744f08580855390de87cc77d676b312582e8d4cfdb5bb8fd298d21", "size_in_bytes": 3717}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "sha256_in_prefix": "48a51959582478352275428ceecd78ef77d79ac9dae796e39a2eaf2540282552", "size_in_bytes": 2402}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "sha256_in_prefix": "aa9ecd43568bb624a0310aa8ea05a57c6a72d08217ce830999e4132e9cea1594", "size_in_bytes": 48}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard-4.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "sha256_in_prefix": "e33dbc021b83a1dc114bf73527f97c1f9d6de50bb07d3b1eb24633971a7a82bb", "size_in_bytes": 10}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__init__.py", "path_type": "hardlink", "sha256": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "sha256_in_prefix": "3a7878c37f1e94f0a3b65714dc963d93787bd0d8ecc5722401f966427f99d056", "size_in_bytes": 2071}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "3229395d30540544b8e2179d6c9780f0b819fffde0cfeffc321f74c8824ca947", "sha256_in_prefix": "3229395d30540544b8e2179d6c9780f0b819fffde0cfeffc321f74c8824ca947", "size_in_bytes": 1737}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_checkers.cpython-310.pyc", "path_type": "hardlink", "sha256": "226298c753544eee0935129cb8d97d4bc77feaf03917057171bba0be042677f2", "sha256_in_prefix": "226298c753544eee0935129cb8d97d4bc77feaf03917057171bba0be042677f2", "size_in_bytes": 19783}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_config.cpython-310.pyc", "path_type": "hardlink", "sha256": "85c2090420d995dd475132f794d6f8c0ee924bdce926662e7c59e4bb26a85f15", "sha256_in_prefix": "85c2090420d995dd475132f794d6f8c0ee924bdce926662e7c59e4bb26a85f15", "size_in_bytes": 3407}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_decorators.cpython-310.pyc", "path_type": "hardlink", "sha256": "10f6227ba7e08752068798e1ff288b2da6178acfd66093293b28f33ba96b2d69", "sha256_in_prefix": "10f6227ba7e08752068798e1ff288b2da6178acfd66093293b28f33ba96b2d69", "size_in_bytes": 7112}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_exceptions.cpython-310.pyc", "path_type": "hardlink", "sha256": "7d05d240a5c7aa03af9b40b8eb1585b7e72856604e83f05a11090ad6d7207f94", "sha256_in_prefix": "7d05d240a5c7aa03af9b40b8eb1585b7e72856604e83f05a11090ad6d7207f94", "size_in_bytes": 2130}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_functions.cpython-310.pyc", "path_type": "hardlink", "sha256": "cccf329cfe32f12d1e6f986544bb381699548b53012409cf07ced83dc033d5d3", "sha256_in_prefix": "cccf329cfe32f12d1e6f986544bb381699548b53012409cf07ced83dc033d5d3", "size_in_bytes": 7673}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_importhook.cpython-310.pyc", "path_type": "hardlink", "sha256": "ff0b359fb22c158a7b8cd0f88f666314658f15c3ae7bd5902186b01251ee2b11", "sha256_in_prefix": "ff0b359fb22c158a7b8cd0f88f666314658f15c3ae7bd5902186b01251ee2b11", "size_in_bytes": 6975}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_memo.cpython-310.pyc", "path_type": "hardlink", "sha256": "112f12a634aeede309c5fbcf6084a56879f6aeccbbd4cefa11078c914fce03ac", "sha256_in_prefix": "112f12a634aeede309c5fbcf6084a56879f6aeccbbd4cefa11078c914fce03ac", "size_in_bytes": 1599}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_pytest_plugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "2ec73f8d15e8104d23cd2e8f7e328928032428fbb120d3bfd0276e27816953e4", "sha256_in_prefix": "2ec73f8d15e8104d23cd2e8f7e328928032428fbb120d3bfd0276e27816953e4", "size_in_bytes": 4045}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_suppression.cpython-310.pyc", "path_type": "hardlink", "sha256": "2d0d28257a865ccdceeb3861e8768d073c50fb4102cd9bec1c7cf3ca25b2df97", "sha256_in_prefix": "2d0d28257a865ccdceeb3861e8768d073c50fb4102cd9bec1c7cf3ca25b2df97", "size_in_bytes": 2668}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_transformer.cpython-310.pyc", "path_type": "hardlink", "sha256": "0c79afd666fd0482487446ebf4d5e0957cd712d0d4db1eefcf76af4d7af1015f", "sha256_in_prefix": "0c79afd666fd0482487446ebf4d5e0957cd712d0d4db1eefcf76af4d7af1015f", "size_in_bytes": 28110}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_union_transformer.cpython-310.pyc", "path_type": "hardlink", "sha256": "02d53ae281c089389e69286db25be1779683466cc8fbcdc549f14aa0bf0ad7d2", "sha256_in_prefix": "02d53ae281c089389e69286db25be1779683466cc8fbcdc549f14aa0bf0ad7d2", "size_in_bytes": 1875}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/__pycache__/_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "3bc84ded182f2179e650c45c511bf7537beb6e7fc5e9de5eb4907eb8e9c4f684", "sha256_in_prefix": "3bc84ded182f2179e650c45c511bf7537beb6e7fc5e9de5eb4907eb8e9c4f684", "size_in_bytes": 5156}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_checkers.py", "path_type": "hardlink", "sha256": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "sha256_in_prefix": "251ae02a271d3847c8068344b5e81808422586969df9ad6ed449bb1828f45822", "size_in_bytes": 31360}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_config.py", "path_type": "hardlink", "sha256": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "sha256_in_prefix": "9c8cfc4300dafa814edcbf4ef3feacaf396677df6949bcb6c0e33859bec5fc1d", "size_in_bytes": 2846}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_decorators.py", "path_type": "hardlink", "sha256": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "sha256_in_prefix": "bfa76c21e5af3e113118b3ffc1717e4660d4ca365ffc0936f20ee0049fefd3ed", "size_in_bytes": 9033}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_exceptions.py", "path_type": "hardlink", "sha256": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "sha256_in_prefix": "6483de895f8505de449b0d8469677616f96caf08b8a1cc13d9f54604802d1dc4", "size_in_bytes": 1121}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_functions.py", "path_type": "hardlink", "sha256": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "sha256_in_prefix": "89b81200a6b9a6d226d5e47d0111b4052a3300524f14d01266a84f57241eaa28", "size_in_bytes": 10393}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_importhook.py", "path_type": "hardlink", "sha256": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "sha256_in_prefix": "ba08c20ef15c756314ed4ba0aa5246f7522954da44231b51afef7db3487593b3", "size_in_bytes": 6389}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_memo.py", "path_type": "hardlink", "sha256": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "sha256_in_prefix": "d63b9057fbf19c3d8960a6d2ade6e242e8f8a0a1f3ea7ebbbfda5803e0822128", "size_in_bytes": 1303}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py", "path_type": "hardlink", "sha256": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "sha256_in_prefix": "f9f712aa4bf9e2b21f205f290dabd8e5840f923e0e5fc18cb7f94bec24120f82", "size_in_bytes": 4416}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_suppression.py", "path_type": "hardlink", "sha256": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "sha256_in_prefix": "5507f3c5cc086eede27f47fb54190a33b86460e03bb4d170f5aee3301b26320e", "size_in_bytes": 2266}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_transformer.py", "path_type": "hardlink", "sha256": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "sha256_in_prefix": "f476bbfd085dc285278bfea1bdd63e8596ee11eae0a301eb34bdafcc721a9056", "size_in_bytes": 44937}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_union_transformer.py", "path_type": "hardlink", "sha256": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "sha256_in_prefix": "bffe36afbfba1ee457d92a05c27c89f84e4f9715e955e5093c9475f8753da92a", "size_in_bytes": 1354}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/_utils.py", "path_type": "hardlink", "sha256": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "sha256_in_prefix": "e4784ed6b3e7e5fd4ceb29a454012fed79a5cf5717fa3d0e9d3325c87aaaad1f", "size_in_bytes": 5270}, {"_path": "Lib/site-packages/setuptools/_vendor/typeguard/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "sha256_in_prefix": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "size_in_bytes": 13936}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "sha256_in_prefix": "05e51021af1c9d86eb8d6c7e37c4cece733d5065b91a6d8389c5690ed440f16d", "size_in_bytes": 3018}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "sha256_in_prefix": "7710002d81971e632aa6a2fc33dc5d74aaf5d7caae22040a65d3e31503b05ee9", "size_in_bytes": 571}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions-4.12.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/setuptools/_vendor/typing_extensions.py", "path_type": "hardlink", "sha256": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "sha256_in_prefix": "8307a4a721bd0d51b797158a5f89e2f2eee793759ee6c946f7c980f45dc3250c", "size_in_bytes": 134451}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "sha256_in_prefix": "98acfce07ee6ee3b31272cde21c4d53918936f434f315dfd2af3886211a09a30", "size_in_bytes": 2313}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "d639f1ac7c993c3715bd42f27c616189b6b86792fdfd1b17afd77170d6e16984", "sha256_in_prefix": "d639f1ac7c993c3715bd42f27c616189b6b86792fdfd1b17afd77170d6e16984", "size_in_bytes": 4900}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "sha256_in_prefix": "0a950253178741b44de54191407611268acee407fe432fdf1cc72d710f034862", "size_in_bytes": 82}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel-0.45.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__init__.py", "path_type": "hardlink", "sha256": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "sha256_in_prefix": "9abc4c9ef757002babfcb59e81b51f879839cac599addeb75099fcf74c2f18d9", "size_in_bytes": 59}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "e9a05f5d0a187ce42b461955f7d7baeffc56388187825e69b91d96e226c7a17e", "sha256_in_prefix": "e9a05f5d0a187ce42b461955f7d7baeffc56388187825e69b91d96e226c7a17e", "size_in_bytes": 220}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/__main__.cpython-310.pyc", "path_type": "hardlink", "sha256": "396e3c59ae6e2290d0d5907f5c2132a1aadb79a755364d2725f0a45fda2c5d80", "sha256_in_prefix": "396e3c59ae6e2290d0d5907f5c2132a1aadb79a755364d2725f0a45fda2c5d80", "size_in_bytes": 618}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_bdist_wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "d8b4c56af818f0e21e0a5f3560930978fc82283f6afa1dd41b152b3326a42ec4", "sha256_in_prefix": "d8b4c56af818f0e21e0a5f3560930978fc82283f6afa1dd41b152b3326a42ec4", "size_in_bytes": 15200}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/_setuptools_logging.cpython-310.pyc", "path_type": "hardlink", "sha256": "b3e1bd38010c7389a4c3c678bc19830c2ecb3b507895b21f169c4e498d84f0d9", "sha256_in_prefix": "b3e1bd38010c7389a4c3c678bc19830c2ecb3b507895b21f169c4e498d84f0d9", "size_in_bytes": 1006}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/bdist_wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "34f5da230fab6336c8620bfcaa4d7a482bc64492c8b5fd95356bb13f39e54d7e", "sha256_in_prefix": "34f5da230fab6336c8620bfcaa4d7a482bc64492c8b5fd95356bb13f39e54d7e", "size_in_bytes": 680}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/macosx_libfile.cpython-310.pyc", "path_type": "hardlink", "sha256": "cab3527da0dd494c5c469e1240277e92e8a5bb69467515e229c267612e0df739", "sha256_in_prefix": "cab3527da0dd494c5c469e1240277e92e8a5bb69467515e229c267612e0df739", "size_in_bytes": 10436}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/metadata.cpython-310.pyc", "path_type": "hardlink", "sha256": "5a1c5d7d9a2343e35d05dd6fbd1bdb96952b250cd84f418d448b7d9b36ba4e32", "sha256_in_prefix": "5a1c5d7d9a2343e35d05dd6fbd1bdb96952b250cd84f418d448b7d9b36ba4e32", "size_in_bytes": 6177}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/util.cpython-310.pyc", "path_type": "hardlink", "sha256": "f28871cdc2252f9f06e3ef88a96fd202c1d67786bf603143cfa2e8f09155cacd", "sha256_in_prefix": "f28871cdc2252f9f06e3ef88a96fd202c1d67786bf603143cfa2e8f09155cacd", "size_in_bytes": 701}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/__pycache__/wheelfile.cpython-310.pyc", "path_type": "hardlink", "sha256": "4e3714d7ef681aa181978032483c281abba6a69a5158ebd45b94807e17163d1c", "sha256_in_prefix": "4e3714d7ef681aa181978032483c281abba6a69a5158ebd45b94807e17163d1c", "size_in_bytes": 6492}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "sha256_in_prefix": "520842423487fe955f71987aa118f34b0fd342171fdda9d2c753a488b48bf363", "size_in_bytes": 21694}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "sha256_in_prefix": "b697fd5ae7e248ed51b84320e683e121f486f0333388267fe26b82285ebd0aaa", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "be8a448805a20736fa1978dde4879882aee6805b5d9859298f3bbbf4d3657af6", "sha256_in_prefix": "be8a448805a20736fa1978dde4879882aee6805b5d9859298f3bbbf4d3657af6", "size_in_bytes": 4568}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/convert.cpython-310.pyc", "path_type": "hardlink", "sha256": "1e4413e2f932cd64300904acf9e815a675f72267e17b4af31a01fd16d093ed3f", "sha256_in_prefix": "1e4413e2f932cd64300904acf9e815a675f72267e17b4af31a01fd16d093ed3f", "size_in_bytes": 9618}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/pack.cpython-310.pyc", "path_type": "hardlink", "sha256": "d0c8ea68b393a5d5994fe51a2d95ec46507cc74ccd35be827a232ca913169dfe", "sha256_in_prefix": "d0c8ea68b393a5d5994fe51a2d95ec46507cc74ccd35be827a232ca913169dfe", "size_in_bytes": 3088}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/tags.cpython-310.pyc", "path_type": "hardlink", "sha256": "c5928d8a658af50c5629624597e7667fbc052f7173562c1d1c5da718b556c24b", "sha256_in_prefix": "c5928d8a658af50c5629624597e7667fbc052f7173562c1d1c5da718b556c24b", "size_in_bytes": 3837}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/__pycache__/unpack.cpython-310.pyc", "path_type": "hardlink", "sha256": "1fe44c587d1c1df944bd464ee2a7877424bfa0d9d58e035b9b5daf0f6a2e5cbe", "sha256_in_prefix": "1fe44c587d1c1df944bd464ee2a7877424bfa0d9d58e035b9b5daf0f6a2e5cbe", "size_in_bytes": 1090}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "sha256_in_prefix": "062d27b445dbf674e5942c56793450e23fa73ecdeccd64842a2a46fc68273244", "size_in_bytes": 12634}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/util.py", "path_type": "hardlink", "sha256": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "sha256_in_prefix": "68beda89b1f061481f73c5a5a252f9b577645780dab5b2716476f59301c52405", "size_in_bytes": 423}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "6e853c345ff448bc7dd13e14913076b4388dfea0588059217e319d491f1676bc", "sha256_in_prefix": "6e853c345ff448bc7dd13e14913076b4388dfea0588059217e319d491f1676bc", "size_in_bytes": 156}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE", "path_type": "hardlink", "sha256": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "sha256_in_prefix": "cad1ef5bd340d73e074ba614d26f7deaca5c7940c3d8c34852e65c4909686c48", "size_in_bytes": 197}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.APACHE", "path_type": "hardlink", "sha256": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "sha256_in_prefix": "0d542e0c8804e39aa7f37eb00da5a762149dc682d7829451287e11b938e94594", "size_in_bytes": 10174}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/LICENSE.BSD", "path_type": "hardlink", "sha256": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "sha256_in_prefix": "b70e7e9b742f1cc6f948b34c16aa39ffece94196364bc88ff0d2180f0028fac5", "size_in_bytes": 1344}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "e9a33c31f9d544ca3035f41700255993712bd4cdd2fee5d9fa2fce8988a22813", "sha256_in_prefix": "e9a33c31f9d544ca3035f41700255993712bd4cdd2fee5d9fa2fce8988a22813", "size_in_bytes": 166}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_elffile.cpython-310.pyc", "path_type": "hardlink", "sha256": "360c42169cfb51088c4b34caa0ada90a3ef6cb5d61f0fc0a3357c4a840853fc2", "sha256_in_prefix": "360c42169cfb51088c4b34caa0ada90a3ef6cb5d61f0fc0a3357c4a840853fc2", "size_in_bytes": 3288}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_manylinux.cpython-310.pyc", "path_type": "hardlink", "sha256": "afefca7d96665def8c8f5cc7b4797e46a6652dcda56a84806cc6c7eabc2bcfa4", "sha256_in_prefix": "afefca7d96665def8c8f5cc7b4797e46a6652dcda56a84806cc6c7eabc2bcfa4", "size_in_bytes": 6397}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_musllinux.cpython-310.pyc", "path_type": "hardlink", "sha256": "5aae6c2de8412a8dee8ee60e06d38d191602e86a1104e03289a5cf62684448d3", "sha256_in_prefix": "5aae6c2de8412a8dee8ee60e06d38d191602e86a1104e03289a5cf62684448d3", "size_in_bytes": 3316}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_parser.cpython-310.pyc", "path_type": "hardlink", "sha256": "bf21ef5a8b68b6493bb35edc6596e3636fd804d7427196fd608474abee48c47b", "sha256_in_prefix": "bf21ef5a8b68b6493bb35edc6596e3636fd804d7427196fd608474abee48c47b", "size_in_bytes": 8940}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_structures.cpython-310.pyc", "path_type": "hardlink", "sha256": "1423d16fa7e301e7df40b1e330a07fb6c3060910cafe6047e8a2e1c343f0d9ce", "sha256_in_prefix": "1423d16fa7e301e7df40b1e330a07fb6c3060910cafe6047e8a2e1c343f0d9ce", "size_in_bytes": 2678}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-310.pyc", "path_type": "hardlink", "sha256": "8f2666c77576b1ba09f86c9d809062c1541ed3025b96e9ef7394c2bb54b0e8a5", "sha256_in_prefix": "8f2666c77576b1ba09f86c9d809062c1541ed3025b96e9ef7394c2bb54b0e8a5", "size_in_bytes": 5798}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/markers.cpython-310.pyc", "path_type": "hardlink", "sha256": "53997489405eabfd3d865e5b64d6f580256be3829f1cda8637828427191e9dd1", "sha256_in_prefix": "53997489405eabfd3d865e5b64d6f580256be3829f1cda8637828427191e9dd1", "size_in_bytes": 6884}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/requirements.cpython-310.pyc", "path_type": "hardlink", "sha256": "782c035be3659b8d07f70027133ce20dfa2282dd0f90d14a517a720ba0e7ad62", "sha256_in_prefix": "782c035be3659b8d07f70027133ce20dfa2282dd0f90d14a517a720ba0e7ad62", "size_in_bytes": 2811}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/specifiers.cpython-310.pyc", "path_type": "hardlink", "sha256": "901dd9ff03abcf47a51089192fa18cb262a26bf04df282c050b4922e386d4948", "sha256_in_prefix": "901dd9ff03abcf47a51089192fa18cb262a26bf04df282c050b4922e386d4948", "size_in_bytes": 30970}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/tags.cpython-310.pyc", "path_type": "hardlink", "sha256": "3f8ca1080ec76512748436a8a26bdc34a7d39752f40fcffa96f2c1cc6479bc67", "sha256_in_prefix": "3f8ca1080ec76512748436a8a26bdc34a7d39752f40fcffa96f2c1cc6479bc67", "size_in_bytes": 13778}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "e83c3ed73509461642cf2bfeaee34328033d8e61ba08e5aefde74bd139e7cee2", "sha256_in_prefix": "e83c3ed73509461642cf2bfeaee34328033d8e61ba08e5aefde74bd139e7cee2", "size_in_bytes": 4496}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/__pycache__/version.cpython-310.pyc", "path_type": "hardlink", "sha256": "813650046e2db64212b6c0641e0db04db3ec63054e6ad36bfb1827b8de5ab965", "sha256_in_prefix": "813650046e2db64212b6c0641e0db04db3ec63054e6ad36bfb1827b8de5ab965", "size_in_bytes": 14141}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "Lib/site-packages/setuptools/_vendor/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/LICENSE", "path_type": "hardlink", "sha256": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "sha256_in_prefix": "86da0f01aeae46348a3c3d465195dc1ceccde79f79e87769a64b8da04b2a4741", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "sha256_in_prefix": "508ae4fe43081c64b0b0a2828588b3a8cc3430c6693d1676662569400b0dfdb1", "size_in_bytes": 3575}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "sha256_in_prefix": "f316f2e03fd9ade7ebbc0b154706848e2bb8fd568b90935109f0d8e3ce2b9bfe", "size_in_bytes": 1039}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "sha256_in_prefix": "189eedfe4581172c1b6a02b97a8f48a14c0b5baa3239e4ca990fbd8871553714", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp-3.19.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "sha256_in_prefix": "8806dda121df686a817d56f65ee47d26a4901c2a0eb0eb46eb2f42fcb4a9a85c", "size_in_bytes": 5}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__init__.py", "path_type": "hardlink", "sha256": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "sha256_in_prefix": "42e235834d06e1f440706b7e1ea6d5d285889264a079d086198b071d8ccd6bc0", "size_in_bytes": 13412}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "d156d77cddbe0f4799cfa93858bc312cbb51e6bb7efc1d22511419f4b09fa60d", "sha256_in_prefix": "d156d77cddbe0f4799cfa93858bc312cbb51e6bb7efc1d22511419f4b09fa60d", "size_in_bytes": 16164}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/__pycache__/glob.cpython-310.pyc", "path_type": "hardlink", "sha256": "5439782b629939a8a4c7459c6908c36a541267dcafc0d9e99fecebdfd4f5a8d0", "sha256_in_prefix": "5439782b629939a8a4c7459c6908c36a541267dcafc0d9e99fecebdfd4f5a8d0", "size_in_bytes": 3926}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "0a8543248c578099d6bcc29b389af3ea766a03b19d844af59de4917068add35c", "sha256_in_prefix": "0a8543248c578099d6bcc29b389af3ea766a03b19d844af59de4917068add35c", "size_in_bytes": 153}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/__pycache__/py310.cpython-310.pyc", "path_type": "hardlink", "sha256": "8a6ee0b72ea3c7b9345636a1b69e6ac48466323ed27d8e8ddb68a6e275aeed14", "sha256_in_prefix": "8a6ee0b72ea3c7b9345636a1b69e6ac48466323ed27d8e8ddb68a6e275aeed14", "size_in_bytes": 394}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/compat/py310.py", "path_type": "hardlink", "sha256": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "sha256_in_prefix": "799a645b4cd1b6e9e484487c8e35f780219edb67a6a0a081270ef666de119210", "size_in_bytes": 219}, {"_path": "Lib/site-packages/setuptools/_vendor/zipp/glob.py", "path_type": "hardlink", "sha256": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "sha256_in_prefix": "7ad5a99df1284727d4beb52c8bab13886984aef3f07ba1f363aa53f2383f9542", "size_in_bytes": 3082}, {"_path": "Lib/site-packages/setuptools/archive_util.py", "path_type": "hardlink", "sha256": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "sha256_in_prefix": "4e5ffae21493b5ce32f31ef16bdf2b15551b1b6e2802ba63ccb0181983f6fec2", "size_in_bytes": 7356}, {"_path": "Lib/site-packages/setuptools/build_meta.py", "path_type": "hardlink", "sha256": "aebcbe2e8c2abd616cc46e909b94167ad1c919e113cd1762439f9bb386ce923a", "sha256_in_prefix": "aebcbe2e8c2abd616cc46e909b94167ad1c919e113cd1762439f9bb386ce923a", "size_in_bytes": 20446}, {"_path": "Lib/site-packages/setuptools/cli-32.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/cli-64.exe", "path_type": "hardlink", "sha256": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "sha256_in_prefix": "bbb3de5707629e6a60a0c238cd477b28f07f0066982fda953fa6fcec39073a4a", "size_in_bytes": 14336}, {"_path": "Lib/site-packages/setuptools/cli-arm64.exe", "path_type": "hardlink", "sha256": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "sha256_in_prefix": "b9a7d08da880dfac8bcf548eba4b06fb59b6f09b17d33148a0f6618328926c61", "size_in_bytes": 13824}, {"_path": "Lib/site-packages/setuptools/cli.exe", "path_type": "hardlink", "sha256": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "sha256_in_prefix": "32acc1bc543116cbe2cff10cb867772df2f254ff2634c870aef0b46c4b696fdb", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/command/__init__.py", "path_type": "hardlink", "sha256": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "sha256_in_prefix": "c1d4ab94d4743fa9c2cfdfe816d08088091e14932c65ad633dca574f9ddfd123", "size_in_bytes": 803}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "ea4fc9e761a21fb9b7736af90927223bcaf9fd4a5aceaf85d3a4fda8e86f6853", "sha256_in_prefix": "ea4fc9e761a21fb9b7736af90927223bcaf9fd4a5aceaf85d3a4fda8e86f6853", "size_in_bytes": 399}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/_requirestxt.cpython-310.pyc", "path_type": "hardlink", "sha256": "a91ae56244a1dec15ff8f5ed830bd32ad96cb49437e71b3747299e3c0d3bd7ef", "sha256_in_prefix": "a91ae56244a1dec15ff8f5ed830bd32ad96cb49437e71b3747299e3c0d3bd7ef", "size_in_bytes": 4653}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/alias.cpython-310.pyc", "path_type": "hardlink", "sha256": "892ff4ad447b44f2b4e758e6176eac90181423e7f35656cf82a483e08b5a0ead", "sha256_in_prefix": "892ff4ad447b44f2b4e758e6176eac90181423e7f35656cf82a483e08b5a0ead", "size_in_bytes": 2352}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_egg.cpython-310.pyc", "path_type": "hardlink", "sha256": "ead26a6dc340c05fdf8565543efc9acca1a7f53c54ebb077685865f36e51e600", "sha256_in_prefix": "ead26a6dc340c05fdf8565543efc9acca1a7f53c54ebb077685865f36e51e600", "size_in_bytes": 13699}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_rpm.cpython-310.pyc", "path_type": "hardlink", "sha256": "f15ac283c866578541574f75e995c3fd0ec2a8c79c42fbfdf53676e26a8f8453", "sha256_in_prefix": "f15ac283c866578541574f75e995c3fd0ec2a8c79c42fbfdf53676e26a8f8453", "size_in_bytes": 1772}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/bdist_wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "61fa2d4b8805abb5e95b0072cd0279a376780bed4aa19affe8ac29cd30583782", "sha256_in_prefix": "61fa2d4b8805abb5e95b0072cd0279a376780bed4aa19affe8ac29cd30583782", "size_in_bytes": 15237}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build.cpython-310.pyc", "path_type": "hardlink", "sha256": "81cbc9768a01ba2f6ee80963856aea9563ea27f54bd8fcfa9df7f10b50859b0c", "sha256_in_prefix": "81cbc9768a01ba2f6ee80963856aea9563ea27f54bd8fcfa9df7f10b50859b0c", "size_in_bytes": 5272}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_clib.cpython-310.pyc", "path_type": "hardlink", "sha256": "44e3624ed57f459970d101fb42e4b9e7dfb100969e4341097ab70e76f2ee7bd3", "sha256_in_prefix": "44e3624ed57f459970d101fb42e4b9e7dfb100969e4341097ab70e76f2ee7bd3", "size_in_bytes": 2488}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_ext.cpython-310.pyc", "path_type": "hardlink", "sha256": "111e5eafd41cec26d3142ac9bedabfe6f869bf14ec3a95613022936696140305", "sha256_in_prefix": "111e5eafd41cec26d3142ac9bedabfe6f869bf14ec3a95613022936696140305", "size_in_bytes": 14038}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/build_py.cpython-310.pyc", "path_type": "hardlink", "sha256": "aa471583634ec59015e0f1e0bc4f4a198fdfc3ec1e86adb8d4f1766f507ec317", "sha256_in_prefix": "aa471583634ec59015e0f1e0bc4f4a198fdfc3ec1e86adb8d4f1766f507ec317", "size_in_bytes": 14959}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/develop.cpython-310.pyc", "path_type": "hardlink", "sha256": "54a315ced55dfc2341d27fda012a5341189892171d2d1ed7b827721ac873a7b5", "sha256_in_prefix": "54a315ced55dfc2341d27fda012a5341189892171d2d1ed7b827721ac873a7b5", "size_in_bytes": 6084}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/dist_info.cpython-310.pyc", "path_type": "hardlink", "sha256": "9814a3c98c973fb2e12855822954b1f9922f1187a289b79af54f292ee2ab9989", "sha256_in_prefix": "9814a3c98c973fb2e12855822954b1f9922f1187a289b79af54f292ee2ab9989", "size_in_bytes": 3244}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/easy_install.cpython-310.pyc", "path_type": "hardlink", "sha256": "8c1dca0ab427e3bbb9de99363058efa8f21b06cb7763da38ccb0f4e849a3cbdc", "sha256_in_prefix": "8c1dca0ab427e3bbb9de99363058efa8f21b06cb7763da38ccb0f4e849a3cbdc", "size_in_bytes": 65181}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/editable_wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "1ff1967692cd4a3f4d307999aa257e2671c0192f4159f34e95825d683a30c154", "sha256_in_prefix": "1ff1967692cd4a3f4d307999aa257e2671c0192f4159f34e95825d683a30c154", "size_in_bytes": 35763}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/egg_info.cpython-310.pyc", "path_type": "hardlink", "sha256": "30b96dd36de83fed2c7b65fb5f210fc58b3963c76eb7cc17edb4893938193dba", "sha256_in_prefix": "30b96dd36de83fed2c7b65fb5f210fc58b3963c76eb7cc17edb4893938193dba", "size_in_bytes": 22261}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install.cpython-310.pyc", "path_type": "hardlink", "sha256": "ed758d8e725ae24bdd8039ddde9bdc292717206f2ab11161e621ef69df37c382", "sha256_in_prefix": "ed758d8e725ae24bdd8039ddde9bdc292717206f2ab11161e621ef69df37c382", "size_in_bytes": 5413}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_egg_info.cpython-310.pyc", "path_type": "hardlink", "sha256": "88546cc8190f2f95c9a97591625249f4bf2dc4faed4f5975a480110fd3b9024d", "sha256_in_prefix": "88546cc8190f2f95c9a97591625249f4bf2dc4faed4f5975a480110fd3b9024d", "size_in_bytes": 2362}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_lib.cpython-310.pyc", "path_type": "hardlink", "sha256": "2898d284e462245573c09cf9b8d4edfd6a725e4e48a6c3f79967f5683e0bc597", "sha256_in_prefix": "2898d284e462245573c09cf9b8d4edfd6a725e4e48a6c3f79967f5683e0bc597", "size_in_bytes": 4504}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/install_scripts.cpython-310.pyc", "path_type": "hardlink", "sha256": "d939174bfbc982b194d354a9e77f198bc75aeb1e19039e271cb165fd6219a6b6", "sha256_in_prefix": "d939174bfbc982b194d354a9e77f198bc75aeb1e19039e271cb165fd6219a6b6", "size_in_bytes": 2571}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/rotate.cpython-310.pyc", "path_type": "hardlink", "sha256": "ff47c7b7f6125c8bbf47b7413d8d960a72ddf06bea929b43e11a63f933e8839f", "sha256_in_prefix": "ff47c7b7f6125c8bbf47b7413d8d960a72ddf06bea929b43e11a63f933e8839f", "size_in_bytes": 2618}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/saveopts.cpython-310.pyc", "path_type": "hardlink", "sha256": "61e4ce55e9413b3c17ec2a1efd4f8d6107bee52d9ea627c52fe47fd5f29a39b0", "sha256_in_prefix": "61e4ce55e9413b3c17ec2a1efd4f8d6107bee52d9ea627c52fe47fd5f29a39b0", "size_in_bytes": 900}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/sdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "64c7356b5db2cb02e283f9e10137aeeb03b20fed828bc4dfa6445fd2a0dfd601", "sha256_in_prefix": "64c7356b5db2cb02e283f9e10137aeeb03b20fed828bc4dfa6445fd2a0dfd601", "size_in_bytes": 7971}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/setopt.cpython-310.pyc", "path_type": "hardlink", "sha256": "756fe9d7aed87f88680db72c95a00dc4ccb25394443e313df0a254265181ba73", "sha256_in_prefix": "756fe9d7aed87f88680db72c95a00dc4ccb25394443e313df0a254265181ba73", "size_in_bytes": 4742}, {"_path": "Lib/site-packages/setuptools/command/__pycache__/test.cpython-310.pyc", "path_type": "hardlink", "sha256": "45bbd49ed8c7e5f8a287ca7ed73ce8677c41b28339007f711ba0509a53247062", "sha256_in_prefix": "45bbd49ed8c7e5f8a287ca7ed73ce8677c41b28339007f711ba0509a53247062", "size_in_bytes": 1702}, {"_path": "Lib/site-packages/setuptools/command/_requirestxt.py", "path_type": "hardlink", "sha256": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "sha256_in_prefix": "22d60c4c91a1fe2e53950b2d5ff9c5a29a848640b83c915a7412f665ddd5ebbd", "size_in_bytes": 4228}, {"_path": "Lib/site-packages/setuptools/command/alias.py", "path_type": "hardlink", "sha256": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "sha256_in_prefix": "ac376b32ddf60d2eaa7f72bbb63659c870ff74c2ab9bbec05dc02dc7e9c14342", "size_in_bytes": 2380}, {"_path": "Lib/site-packages/setuptools/command/bdist_egg.py", "path_type": "hardlink", "sha256": "dde0ee710e1f75e60cb0b3bd3e105f63526470c2e1657827008ffd15d14db041", "sha256_in_prefix": "dde0ee710e1f75e60cb0b3bd3e105f63526470c2e1657827008ffd15d14db041", "size_in_bytes": 16972}, {"_path": "Lib/site-packages/setuptools/command/bdist_rpm.py", "path_type": "hardlink", "sha256": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "sha256_in_prefix": "2f2a88e3dc38f122a4d059ae1ec13d30bcd7d52b978cbed830d6d930566a1482", "size_in_bytes": 1435}, {"_path": "Lib/site-packages/setuptools/command/bdist_wheel.py", "path_type": "hardlink", "sha256": "fcb7c61c1ec257fbb29dcaa53934844c48b6823542a0f2ae017732445a2aec2b", "sha256_in_prefix": "fcb7c61c1ec257fbb29dcaa53934844c48b6823542a0f2ae017732445a2aec2b", "size_in_bytes": 22246}, {"_path": "Lib/site-packages/setuptools/command/build.py", "path_type": "hardlink", "sha256": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "sha256_in_prefix": "788ed24cc111186644a73935b6f24df29f483a30005cc7062f3963bf69b02373", "size_in_bytes": 6052}, {"_path": "Lib/site-packages/setuptools/command/build_clib.py", "path_type": "hardlink", "sha256": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "sha256_in_prefix": "01b8293c817fdea2fc7d9af724879b23e5874cc4c188c7eb164550cfc2b8d06e", "size_in_bytes": 4528}, {"_path": "Lib/site-packages/setuptools/command/build_ext.py", "path_type": "hardlink", "sha256": "6d41f8334362cda249aefd74c0af990f7b98d13c42499958403862c30cc7b253", "sha256_in_prefix": "6d41f8334362cda249aefd74c0af990f7b98d13c42499958403862c30cc7b253", "size_in_bytes": 18377}, {"_path": "Lib/site-packages/setuptools/command/build_py.py", "path_type": "hardlink", "sha256": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "sha256_in_prefix": "0c26e3bc1d7c9242fec542b9aef9739b40bab704de3b1361caf243c716bb7c82", "size_in_bytes": 15539}, {"_path": "Lib/site-packages/setuptools/command/develop.py", "path_type": "hardlink", "sha256": "cd7db6d75f6c2351b581f27580d084e21920db36cb2b1d2e530bcd982e5b22ef", "sha256_in_prefix": "cd7db6d75f6c2351b581f27580d084e21920db36cb2b1d2e530bcd982e5b22ef", "size_in_bytes": 6886}, {"_path": "Lib/site-packages/setuptools/command/dist_info.py", "path_type": "hardlink", "sha256": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "sha256_in_prefix": "1d4ef9da22cb9a660c1dbb03060cf6b9b4639202686ee80ea7c1fbd4bcf30f2b", "size_in_bytes": 3450}, {"_path": "Lib/site-packages/setuptools/command/easy_install.py", "path_type": "hardlink", "sha256": "d19e2416513bf007b601f1d7613c591546b6b77aa536a5c2b50bb8275371f220", "sha256_in_prefix": "d19e2416513bf007b601f1d7613c591546b6b77aa536a5c2b50bb8275371f220", "size_in_bytes": 87870}, {"_path": "Lib/site-packages/setuptools/command/editable_wheel.py", "path_type": "hardlink", "sha256": "ddb062a51640dc4e29a10f0b11684c6048c78c2cea53fa4874ef3a0b7b7ba0d6", "sha256_in_prefix": "ddb062a51640dc4e29a10f0b11684c6048c78c2cea53fa4874ef3a0b7b7ba0d6", "size_in_bytes": 35624}, {"_path": "Lib/site-packages/setuptools/command/egg_info.py", "path_type": "hardlink", "sha256": "596528cd1dc3642ad6b134211d73b280c88451cae32d6a61113d3e66ca1cb26e", "sha256_in_prefix": "596528cd1dc3642ad6b134211d73b280c88451cae32d6a61113d3e66ca1cb26e", "size_in_bytes": 25982}, {"_path": "Lib/site-packages/setuptools/command/install.py", "path_type": "hardlink", "sha256": "3264c66fc9b547c7c9d1c73915358217abaafacd59266be9626f8dfc2b6a11a2", "sha256_in_prefix": "3264c66fc9b547c7c9d1c73915358217abaafacd59266be9626f8dfc2b6a11a2", "size_in_bytes": 7046}, {"_path": "Lib/site-packages/setuptools/command/install_egg_info.py", "path_type": "hardlink", "sha256": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "sha256_in_prefix": "dc8f483c21fb0f9f5287ee9a558cfe87ac30cb1abec24c6b2b02a0de70dd26ab", "size_in_bytes": 2075}, {"_path": "Lib/site-packages/setuptools/command/install_lib.py", "path_type": "hardlink", "sha256": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "sha256_in_prefix": "f67d7f53cdde1dc1112ff6bfaeffcb8470a485794b76ac99e12741a30fbda9c1", "size_in_bytes": 4319}, {"_path": "Lib/site-packages/setuptools/command/install_scripts.py", "path_type": "hardlink", "sha256": "b553828f77bc39322b9282ff6c66d3e693a4b1dc597d06e51ff6dd2380ed555e", "sha256_in_prefix": "b553828f77bc39322b9282ff6c66d3e693a4b1dc597d06e51ff6dd2380ed555e", "size_in_bytes": 2637}, {"_path": "Lib/site-packages/setuptools/command/launcher manifest.xml", "path_type": "hardlink", "sha256": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "sha256_in_prefix": "c652db8d6ac1d35b4a0b4fa195590e2a48923dbccc9a5d9e38fb49fee7029db1", "size_in_bytes": 628}, {"_path": "Lib/site-packages/setuptools/command/rotate.py", "path_type": "hardlink", "sha256": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "sha256_in_prefix": "5cd77f04410e5802475b515c2d3314596978401eb302e93b6fc556420dc51e8b", "size_in_bytes": 2187}, {"_path": "Lib/site-packages/setuptools/command/saveopts.py", "path_type": "hardlink", "sha256": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "sha256_in_prefix": "369d0f55bed20fba136eef59f6ca2c4bb0fe0a4908914ef1e2096ee44b35b630", "size_in_bytes": 692}, {"_path": "Lib/site-packages/setuptools/command/sdist.py", "path_type": "hardlink", "sha256": "25a426dbe79b5c8da4bf2ac18c928ff3234b3dae5e31b31e8acf3c09704c6259", "sha256_in_prefix": "25a426dbe79b5c8da4bf2ac18c928ff3234b3dae5e31b31e8acf3c09704c6259", "size_in_bytes": 7374}, {"_path": "Lib/site-packages/setuptools/command/setopt.py", "path_type": "hardlink", "sha256": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "sha256_in_prefix": "c59176442738001bc4f5e1c7033179d3e7e4420ddabbc7dc45455519de7c9375", "size_in_bytes": 5100}, {"_path": "Lib/site-packages/setuptools/command/test.py", "path_type": "hardlink", "sha256": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "sha256_in_prefix": "93bc5cabb0fb6c47a18316ab6f0f9d5b702d98664e46acfc1e3291e85189de39", "size_in_bytes": 1343}, {"_path": "Lib/site-packages/setuptools/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "1d6531d6720a51476b35a25cd602fe983204854bd84debc4d83e51c2e2746a4b", "sha256_in_prefix": "1d6531d6720a51476b35a25cd602fe983204854bd84debc4d83e51c2e2746a4b", "size_in_bytes": 140}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py310.cpython-310.pyc", "path_type": "hardlink", "sha256": "0532a497ec28d3c67cb9cd4da43153e9e6e11b8862e10615ffc8373eb9152afb", "sha256_in_prefix": "0532a497ec28d3c67cb9cd4da43153e9e6e11b8862e10615ffc8373eb9152afb", "size_in_bytes": 252}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py311.cpython-310.pyc", "path_type": "hardlink", "sha256": "2de5c82904f8cc5ee73d051be28cfbd041dd7d31d234ac2187628fbe913cf707", "sha256_in_prefix": "2de5c82904f8cc5ee73d051be28cfbd041dd7d31d234ac2187628fbe913cf707", "size_in_bytes": 1164}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py312.cpython-310.pyc", "path_type": "hardlink", "sha256": "ea49a1596bcc5672ec96f09e734b6ac623b4d2458e40855d5437a0b858803843", "sha256_in_prefix": "ea49a1596bcc5672ec96f09e734b6ac623b4d2458e40855d5437a0b858803843", "size_in_bytes": 371}, {"_path": "Lib/site-packages/setuptools/compat/__pycache__/py39.cpython-310.pyc", "path_type": "hardlink", "sha256": "844a38107e7117d01624a6dc3a7644ae3ffbad01f8ffcfd48a0848e8f3f8b738", "sha256_in_prefix": "844a38107e7117d01624a6dc3a7644ae3ffbad01f8ffcfd48a0848e8f3f8b738", "size_in_bytes": 226}, {"_path": "Lib/site-packages/setuptools/compat/py310.py", "path_type": "hardlink", "sha256": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "sha256_in_prefix": "f2cab059ccc872b9337806e16a29b8a4a55de2d5d975caa679b81dbf38e2d2b7", "size_in_bytes": 141}, {"_path": "Lib/site-packages/setuptools/compat/py311.py", "path_type": "hardlink", "sha256": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "sha256_in_prefix": "7bab49005c1910ff36866301975d0761e4b2a5e968fd38b6c138ca65528bc0e1", "size_in_bytes": 790}, {"_path": "Lib/site-packages/setuptools/compat/py312.py", "path_type": "hardlink", "sha256": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "sha256_in_prefix": "bd8295b5dadd393b0efd1f747499045ec1707cc245b881497e5848807ae327e6", "size_in_bytes": 366}, {"_path": "Lib/site-packages/setuptools/compat/py39.py", "path_type": "hardlink", "sha256": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "sha256_in_prefix": "04932d9e47dcab24df71caa3610c5fa11b54da74e759a104481564b214e25ea6", "size_in_bytes": 493}, {"_path": "Lib/site-packages/setuptools/config/NOTICE", "path_type": "hardlink", "sha256": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "sha256_in_prefix": "2dddf08818297a3b89d43d95ff659d8da85741108c9136dfa3a4d856c0623bd8", "size_in_bytes": 493}, {"_path": "Lib/site-packages/setuptools/config/__init__.py", "path_type": "hardlink", "sha256": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "sha256_in_prefix": "6a23e72fd0499f53ba31f9ae357ca7f16d8ba7cbbdaa2cd156ac0f88e74f2236", "size_in_bytes": 1499}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "bfcdfd9b30e330c8bd24b5f95b3f188800283bebee683c3c78b412660a189b60", "sha256_in_prefix": "bfcdfd9b30e330c8bd24b5f95b3f188800283bebee683c3c78b412660a189b60", "size_in_bytes": 1599}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/_apply_pyprojecttoml.cpython-310.pyc", "path_type": "hardlink", "sha256": "f7ab6390ddab479cc5b8fdaf7ccb711d569a9acb9350312830be494c6d5103bc", "sha256_in_prefix": "f7ab6390ddab479cc5b8fdaf7ccb711d569a9acb9350312830be494c6d5103bc", "size_in_bytes": 17964}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/expand.cpython-310.pyc", "path_type": "hardlink", "sha256": "7398106031d0d7cadd96bf61ada31ab42190403fe7a55a8089c546d774209305", "sha256_in_prefix": "7398106031d0d7cadd96bf61ada31ab42190403fe7a55a8089c546d774209305", "size_in_bytes": 18203}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/pyprojecttoml.cpython-310.pyc", "path_type": "hardlink", "sha256": "098bae1ad335241b2363e99c45886f034e3a39c68898acff6bb9ccf3eae88270", "sha256_in_prefix": "098bae1ad335241b2363e99c45886f034e3a39c68898acff6bb9ccf3eae88270", "size_in_bytes": 15988}, {"_path": "Lib/site-packages/setuptools/config/__pycache__/setupcfg.cpython-310.pyc", "path_type": "hardlink", "sha256": "8c095a5eb37ffcb6a69083062c90c1bec9a89f286634202b06841aec58cedd29", "sha256_in_prefix": "8c095a5eb37ffcb6a69083062c90c1bec9a89f286634202b06841aec58cedd29", "size_in_bytes": 24163}, {"_path": "Lib/site-packages/setuptools/config/_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "494c93c3b0366ed675941b9628de68e36f838b2bfde5e193898277ad93a71927", "sha256_in_prefix": "494c93c3b0366ed675941b9628de68e36f838b2bfde5e193898277ad93a71927", "size_in_bytes": 19120}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/NOTICE", "path_type": "hardlink", "sha256": "5d300dbfa643138b013b75ac9caeee591f951b8b0ee24288c34ccd926c4780c8", "sha256_in_prefix": "5d300dbfa643138b013b75ac9caeee591f951b8b0ee24288c34ccd926c4780c8", "size_in_bytes": 18737}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__init__.py", "path_type": "hardlink", "sha256": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "sha256_in_prefix": "767a7a4fb78f3f5479cf83ae0bb15dd9d905948aed21f8b351fbe91893fa9f3d", "size_in_bytes": 1042}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "8c58482126614f18a770e7b9dd65bfc340ffbdca6034335417f5d4d42dfa3e66", "sha256_in_prefix": "8c58482126614f18a770e7b9dd65bfc340ffbdca6034335417f5d4d42dfa3e66", "size_in_bytes": 1479}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/error_reporting.cpython-310.pyc", "path_type": "hardlink", "sha256": "b129d6d2ba2c358c58ff57b4510bddf1359ac1892947ac59ce83db3359bc7213", "sha256_in_prefix": "b129d6d2ba2c358c58ff57b4510bddf1359ac1892947ac59ce83db3359bc7213", "size_in_bytes": 11999}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/extra_validations.cpython-310.pyc", "path_type": "hardlink", "sha256": "e1ec39662d91e3e894fa9df7bb9c091c540383173a68506c37946ae9c0c684d6", "sha256_in_prefix": "e1ec39662d91e3e894fa9df7bb9c091c540383173a68506c37946ae9c0c684d6", "size_in_bytes": 2334}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_exceptions.cpython-310.pyc", "path_type": "hardlink", "sha256": "d479aefa1ee231f3ae36a660ef6d6a4e138bb9292b4de4234b2c85c2c0625b66", "sha256_in_prefix": "d479aefa1ee231f3ae36a660ef6d6a4e138bb9292b4de4234b2c85c2c0625b66", "size_in_bytes": 2405}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/fastjsonschema_validations.cpython-310.pyc", "path_type": "hardlink", "sha256": "2600a4b8130a56a2f69afc8732b9605e4b599706bdc67a31d1b057f51ef205c0", "sha256_in_prefix": "2600a4b8130a56a2f69afc8732b9605e4b599706bdc67a31d1b057f51ef205c0", "size_in_bytes": 90738}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/__pycache__/formats.cpython-310.pyc", "path_type": "hardlink", "sha256": "2bee4f19dc6ff0a356c9c93c05105bbcfc4d87cbf5b0c57689f1ba217fecca0a", "sha256_in_prefix": "2bee4f19dc6ff0a356c9c93c05105bbcfc4d87cbf5b0c57689f1ba217fecca0a", "size_in_bytes": 13145}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/error_reporting.py", "path_type": "hardlink", "sha256": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "sha256_in_prefix": "99e95d0fb9c141da25421bc6fb8debd547be814d67ece440251f3abe1dd1aef9", "size_in_bytes": 11813}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/extra_validations.py", "path_type": "hardlink", "sha256": "f86506e52fbe8a363c59f5db7573e81e5eb2c06b32105f5db43a5e9d2a093c78", "sha256_in_prefix": "f86506e52fbe8a363c59f5db7573e81e5eb2c06b32105f5db43a5e9d2a093c78", "size_in_bytes": 2858}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py", "path_type": "hardlink", "sha256": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "sha256_in_prefix": "c3be3d260a8a8bc72504570e6dd71b655aac985e2827f401ca16754866d414dc", "size_in_bytes": 1612}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py", "path_type": "hardlink", "sha256": "162843e5970cea9efb04f674e021aa877044c153683cc289649032b89a64014d", "sha256_in_prefix": "162843e5970cea9efb04f674e021aa877044c153683cc289649032b89a64014d", "size_in_bytes": 354682}, {"_path": "Lib/site-packages/setuptools/config/_validate_pyproject/formats.py", "path_type": "hardlink", "sha256": "4c44e890904af618e5f9c560d6896ca23909c0bc5f3fbfdc860250366cc007df", "sha256_in_prefix": "4c44e890904af618e5f9c560d6896ca23909c0bc5f3fbfdc860250366cc007df", "size_in_bytes": 13564}, {"_path": "Lib/site-packages/setuptools/config/distutils.schema.json", "path_type": "hardlink", "sha256": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "sha256_in_prefix": "4dca77da44678703911b0ffda7a1848b4f258f6875e6d411cce6016f31a67015", "size_in_bytes": 972}, {"_path": "Lib/site-packages/setuptools/config/expand.py", "path_type": "hardlink", "sha256": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "sha256_in_prefix": "24d024b510accb2441fab42875b3e70ae7262d6a9c62fcc20c2f046e7d99ef13", "size_in_bytes": 16041}, {"_path": "Lib/site-packages/setuptools/config/pyprojecttoml.py", "path_type": "hardlink", "sha256": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "sha256_in_prefix": "60cbb93dd6c9248e5ace9ea447f6e833599f95fe67a8e03e227178b3a2e72e0c", "size_in_bytes": 18320}, {"_path": "Lib/site-packages/setuptools/config/setupcfg.py", "path_type": "hardlink", "sha256": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "sha256_in_prefix": "5590e4c04ec362fe3949b69d243f02c0aac3b625ef8e09652fc3d84afa110b28", "size_in_bytes": 26575}, {"_path": "Lib/site-packages/setuptools/config/setuptools.schema.json", "path_type": "hardlink", "sha256": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "sha256_in_prefix": "759051b921276646ada1596dd645701bca1c4de45d3bb043d31bce58a1f9e0f6", "size_in_bytes": 16071}, {"_path": "Lib/site-packages/setuptools/depends.py", "path_type": "hardlink", "sha256": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "sha256_in_prefix": "8ca61f8e6b7fd9941856085bf0bf5b53b2c9e9eac7279cdef8afdf295d413179", "size_in_bytes": 5965}, {"_path": "Lib/site-packages/setuptools/discovery.py", "path_type": "hardlink", "sha256": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "sha256_in_prefix": "fb8d9cdd7870ce47e874328a3f9d02d98073af5d5f9dc020994cc174145bd3e4", "size_in_bytes": 21258}, {"_path": "Lib/site-packages/setuptools/dist.py", "path_type": "hardlink", "sha256": "459cfb6a3f51c6a498ae2aa016864ebbeeca215f3672695a305c7da3066b0294", "sha256_in_prefix": "459cfb6a3f51c6a498ae2aa016864ebbeeca215f3672695a305c7da3066b0294", "size_in_bytes": 44897}, {"_path": "Lib/site-packages/setuptools/errors.py", "path_type": "hardlink", "sha256": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "sha256_in_prefix": "818db1d8f21a220cb4d724403510becdc0b0c430aa09272026808e6457b4ca2a", "size_in_bytes": 3024}, {"_path": "Lib/site-packages/setuptools/extension.py", "path_type": "hardlink", "sha256": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "sha256_in_prefix": "2829eff69ded826d1956ab60138e757f220bb26e210b2bce894b4ebbbf3b0fee", "size_in_bytes": 6683}, {"_path": "Lib/site-packages/setuptools/glob.py", "path_type": "hardlink", "sha256": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "sha256_in_prefix": "002fc1df70d8f20f821c42f10ec26bb7016ba62b9c48066c6a43c5752390ce17", "size_in_bytes": 6062}, {"_path": "Lib/site-packages/setuptools/gui-32.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/gui-64.exe", "path_type": "hardlink", "sha256": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "sha256_in_prefix": "3471b6140eadc6412277dbbefe3fef8c345a0f1a59776086b80a3618c3a83e3b", "size_in_bytes": 14336}, {"_path": "Lib/site-packages/setuptools/gui-arm64.exe", "path_type": "hardlink", "sha256": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "sha256_in_prefix": "e694f4743405c8b5926ff457db6fe7f1a12dec7c16a9c3864784d3f4e07ae097", "size_in_bytes": 13824}, {"_path": "Lib/site-packages/setuptools/gui.exe", "path_type": "hardlink", "sha256": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "sha256_in_prefix": "85dae1e95d77845f2cb59bcac3d4afe74bbe4c91a9bcc5bf4a71cd43104dbe7c", "size_in_bytes": 11776}, {"_path": "Lib/site-packages/setuptools/installer.py", "path_type": "hardlink", "sha256": "ff859e831e2bdcbd39b0ca37f8896a169f8ebb19d6c81aa3c8c67b2d64179a1f", "sha256_in_prefix": "ff859e831e2bdcbd39b0ca37f8896a169f8ebb19d6c81aa3c8c67b2d64179a1f", "size_in_bytes": 5110}, {"_path": "Lib/site-packages/setuptools/launch.py", "path_type": "hardlink", "sha256": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "sha256_in_prefix": "2016f9944bfaf42cae67d7b022b98a957875e7891d2e63f6f4b29f4cc9318a61", "size_in_bytes": 820}, {"_path": "Lib/site-packages/setuptools/logging.py", "path_type": "hardlink", "sha256": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "sha256_in_prefix": "5b5ea21c9d477025d8434471cab11f27cdc54f8d7be6d0ada1883e13ab92a552", "size_in_bytes": 1261}, {"_path": "Lib/site-packages/setuptools/modified.py", "path_type": "hardlink", "sha256": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "sha256_in_prefix": "6706df05f0853fcf25b6f6effdd243cfeb752ec4239ccf895298199e74198e33", "size_in_bytes": 568}, {"_path": "Lib/site-packages/setuptools/monkey.py", "path_type": "hardlink", "sha256": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "sha256_in_prefix": "1703169769f5bf66c76dea81cbea3d83cc9435a0246056eccc26d77bd77965af", "size_in_bytes": 3717}, {"_path": "Lib/site-packages/setuptools/msvc.py", "path_type": "hardlink", "sha256": "be6334a8be2b233aed0fda626bd644c2da99e0b6dbae02f4754d0400d558466f", "sha256_in_prefix": "be6334a8be2b233aed0fda626bd644c2da99e0b6dbae02f4754d0400d558466f", "size_in_bytes": 41631}, {"_path": "Lib/site-packages/setuptools/namespaces.py", "path_type": "hardlink", "sha256": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "sha256_in_prefix": "d861aa618d4134312132d05cd6b1d3bfb92582635545d92c25e5be2f57fefb2b", "size_in_bytes": 3171}, {"_path": "Lib/site-packages/setuptools/package_index.py", "path_type": "hardlink", "sha256": "229e1037982820092350ae941e0d34e6ea70c55f1ad948ed1045a3b0ff3174e9", "sha256_in_prefix": "229e1037982820092350ae941e0d34e6ea70c55f1ad948ed1045a3b0ff3174e9", "size_in_bytes": 40519}, {"_path": "Lib/site-packages/setuptools/sandbox.py", "path_type": "hardlink", "sha256": "7ccaad70eba2a473ba44a3e1d58079a3b77df3974b2a8efa5a1a77beb21e8b61", "sha256_in_prefix": "7ccaad70eba2a473ba44a3e1d58079a3b77df3974b2a8efa5a1a77beb21e8b61", "size_in_bytes": 14906}, {"_path": "Lib/site-packages/setuptools/script (dev).tmpl", "path_type": "hardlink", "sha256": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "sha256_in_prefix": "454cd0cc2414697b7074bb581d661b21098e6844b906baaad45bd403fb6efb92", "size_in_bytes": 218}, {"_path": "Lib/site-packages/setuptools/script.tmpl", "path_type": "hardlink", "sha256": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "sha256_in_prefix": "5864ede6989eccedbb73e0dbc7a9794384f715fdb4039cfbf3bda1bf76808586", "size_in_bytes": 138}, {"_path": "Lib/site-packages/setuptools/tests/__init__.py", "path_type": "hardlink", "sha256": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "sha256_in_prefix": "02705f96cda225b4c343398c29e2d1b7ef65c6168e1d454e644817bfcf54c2fb", "size_in_bytes": 335}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "2e6da0bab9159305a9f4cd7276e6afd5840f3f7568f63f5b68e3a49278defd8a", "sha256_in_prefix": "2e6da0bab9159305a9f4cd7276e6afd5840f3f7568f63f5b68e3a49278defd8a", "size_in_bytes": 446}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/contexts.cpython-310.pyc", "path_type": "hardlink", "sha256": "c4092e22024c6f2e6d346255ce662d3ebb00531377e8198074ee0c6ef691ee4c", "sha256_in_prefix": "c4092e22024c6f2e6d346255ce662d3ebb00531377e8198074ee0c6ef691ee4c", "size_in_bytes": 3918}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/environment.cpython-310.pyc", "path_type": "hardlink", "sha256": "f1394dfdbdd6170636511a793cbf238c27df63be72aba370a43c49d34cb1922f", "sha256_in_prefix": "f1394dfdbdd6170636511a793cbf238c27df63be72aba370a43c49d34cb1922f", "size_in_bytes": 2083}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/fixtures.cpython-310.pyc", "path_type": "hardlink", "sha256": "84a6e31c067e9625eb8f21d2d7f34796c203242552a9646dfc8d02063362bc06", "sha256_in_prefix": "84a6e31c067e9625eb8f21d2d7f34796c203242552a9646dfc8d02063362bc06", "size_in_bytes": 4101}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/mod_with_constant.cpython-310.pyc", "path_type": "hardlink", "sha256": "ca7264cf50460c29e91bd9e3a8590b736a3bf544061736d1e2841e3e10c31b28", "sha256_in_prefix": "ca7264cf50460c29e91bd9e3a8590b736a3bf544061736d1e2841e3e10c31b28", "size_in_bytes": 169}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/namespaces.cpython-310.pyc", "path_type": "hardlink", "sha256": "f0797ede2de3d6618bfadb63535a49c3c6adaa68b6ffdc11e5426834c5abddc2", "sha256_in_prefix": "f0797ede2de3d6618bfadb63535a49c3c6adaa68b6ffdc11e5426834c5abddc2", "size_in_bytes": 2719}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/script-with-bom.cpython-310.pyc", "path_type": "hardlink", "sha256": "421c6816f1f4e2bd1263d2e0baad239dacf97af4400a5fffcc093073b99e8113", "sha256_in_prefix": "421c6816f1f4e2bd1263d2e0baad239dacf97af4400a5fffcc093073b99e8113", "size_in_bytes": 163}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/server.cpython-310.pyc", "path_type": "hardlink", "sha256": "dd5cfa4a982c1e6177923c60ae21e71de03af1e47a9eb263350a272333fab652", "sha256_in_prefix": "dd5cfa4a982c1e6177923c60ae21e71de03af1e47a9eb263350a272333fab652", "size_in_bytes": 3369}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_archive_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "f5f6e926a9d64c62a2358ef39e81a22f6a266470912f0feec9e9094a76cadc91", "sha256_in_prefix": "f5f6e926a9d64c62a2358ef39e81a22f6a266470912f0feec9e9094a76cadc91", "size_in_bytes": 1137}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_deprecations.cpython-310.pyc", "path_type": "hardlink", "sha256": "7c6e462b499b69ef49a466a2efa9729c70119c742ae02ece9df04a06868a1235", "sha256_in_prefix": "7c6e462b499b69ef49a466a2efa9729c70119c742ae02ece9df04a06868a1235", "size_in_bytes": 1014}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_egg.cpython-310.pyc", "path_type": "hardlink", "sha256": "29e74107f68c03543fa2e39c40a06b9535a79fb7955f75bc66d5f1f73c9846b5", "sha256_in_prefix": "29e74107f68c03543fa2e39c40a06b9535a79fb7955f75bc66d5f1f73c9846b5", "size_in_bytes": 2348}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_bdist_wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "dc5fd8e70c5b98c2de957f2886f41c062886631c6ab383044207e20edbb15db6", "sha256_in_prefix": "dc5fd8e70c5b98c2de957f2886f41c062886631c6ab383044207e20edbb15db6", "size_in_bytes": 23058}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build.cpython-310.pyc", "path_type": "hardlink", "sha256": "f2093da4702bae1dbd5f5cbd2af24847afc14de070d4be5927e65c8946f5e37c", "sha256_in_prefix": "f2093da4702bae1dbd5f5cbd2af24847afc14de070d4be5927e65c8946f5e37c", "size_in_bytes": 1371}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_clib.cpython-310.pyc", "path_type": "hardlink", "sha256": "45471ac4335c62d16059b159cfbd3fa3d93806c84190677f8e6fbedd29023208", "sha256_in_prefix": "45471ac4335c62d16059b159cfbd3fa3d93806c84190677f8e6fbedd29023208", "size_in_bytes": 2244}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_ext.cpython-310.pyc", "path_type": "hardlink", "sha256": "58963f744b7e970cd17f12206bbcec2567c433628acfb3cd464bb7f5ebeb20cb", "sha256_in_prefix": "58963f744b7e970cd17f12206bbcec2567c433628acfb3cd464bb7f5ebeb20cb", "size_in_bytes": 9356}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_meta.cpython-310.pyc", "path_type": "hardlink", "sha256": "79797105348c6c99a972ac6b2b5ec2945aa14b72cbdecbab8c5a5d5fccda3f1a", "sha256_in_prefix": "79797105348c6c99a972ac6b2b5ec2945aa14b72cbdecbab8c5a5d5fccda3f1a", "size_in_bytes": 29163}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_build_py.cpython-310.pyc", "path_type": "hardlink", "sha256": "a09c0f1479e0d4690f740d0573c15b504651f8ec2e81929d692ab7f9a4d66c8d", "sha256_in_prefix": "a09c0f1479e0d4690f740d0573c15b504651f8ec2e81929d692ab7f9a4d66c8d", "size_in_bytes": 10830}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_config_discovery.cpython-310.pyc", "path_type": "hardlink", "sha256": "65545f84e143ebd576bbe588bac403da57215ad936836fb7f8e3c0264a2b2b62", "sha256_in_prefix": "65545f84e143ebd576bbe588bac403da57215ad936836fb7f8e3c0264a2b2b62", "size_in_bytes": 20514}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_core_metadata.cpython-310.pyc", "path_type": "hardlink", "sha256": "12271d8616df77794c3e7f6723ede4cdf34458e64134322e914979c9874bc2dc", "sha256_in_prefix": "12271d8616df77794c3e7f6723ede4cdf34458e64134322e914979c9874bc2dc", "size_in_bytes": 15833}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_depends.cpython-310.pyc", "path_type": "hardlink", "sha256": "52e9fc959890adc8d42cc3fa7c32fd289069c14596a1a4bfbb7d94ee7240813d", "sha256_in_prefix": "52e9fc959890adc8d42cc3fa7c32fd289069c14596a1a4bfbb7d94ee7240813d", "size_in_bytes": 748}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_develop.cpython-310.pyc", "path_type": "hardlink", "sha256": "d41a4cc42135daa177f7c6bd0f5247cc5b4e69a1b9ec4ce236f1ddb26905c7a5", "sha256_in_prefix": "d41a4cc42135daa177f7c6bd0f5247cc5b4e69a1b9ec4ce236f1ddb26905c7a5", "size_in_bytes": 5679}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_dist.cpython-310.pyc", "path_type": "hardlink", "sha256": "51e9cea65069ebd9a3a2a0d017260d106dc0729c7c3bed110ad9806da4f14b2b", "sha256_in_prefix": "51e9cea65069ebd9a3a2a0d017260d106dc0729c7c3bed110ad9806da4f14b2b", "size_in_bytes": 6919}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_dist_info.cpython-310.pyc", "path_type": "hardlink", "sha256": "4e7069ea44d2e394f265b4c1c10cd00d39998bedb4bcbe67e553f2e6115c2177", "sha256_in_prefix": "4e7069ea44d2e394f265b4c1c10cd00d39998bedb4bcbe67e553f2e6115c2177", "size_in_bytes": 7008}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_distutils_adoption.cpython-310.pyc", "path_type": "hardlink", "sha256": "157e1f5f669dc521811feb5994e94fcd4fa18fe729d3ceeb1ac27f9f5c3e6149", "sha256_in_prefix": "157e1f5f669dc521811feb5994e94fcd4fa18fe729d3ceeb1ac27f9f5c3e6149", "size_in_bytes": 5605}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_easy_install.cpython-310.pyc", "path_type": "hardlink", "sha256": "62fb9f4af4ac14bb6065e3f85bbd3d5cd9ec638198be659d35fe3ca4e0744a9b", "sha256_in_prefix": "62fb9f4af4ac14bb6065e3f85bbd3d5cd9ec638198be659d35fe3ca4e0744a9b", "size_in_bytes": 44547}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_editable_install.cpython-310.pyc", "path_type": "hardlink", "sha256": "2e9c961d699bd2d27c75bd6f4408d466e2498a7771cd82d4f91860f7d064bcb6", "sha256_in_prefix": "2e9c961d699bd2d27c75bd6f4408d466e2498a7771cd82d4f91860f7d064bcb6", "size_in_bytes": 36915}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_egg_info.cpython-310.pyc", "path_type": "hardlink", "sha256": "d49b15b92e47646783c0b3df704a2dfde107b0695314fb08b7032e70d91b1900", "sha256_in_prefix": "d49b15b92e47646783c0b3df704a2dfde107b0695314fb08b7032e70d91b1900", "size_in_bytes": 31068}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_extern.cpython-310.pyc", "path_type": "hardlink", "sha256": "5c19d3e16bef3d76c9c793b18d6e0df3c7a493977171b56930f26b2847c791f3", "sha256_in_prefix": "5c19d3e16bef3d76c9c793b18d6e0df3c7a493977171b56930f26b2847c791f3", "size_in_bytes": 594}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_find_packages.cpython-310.pyc", "path_type": "hardlink", "sha256": "9d5e7f87e92cedc62ee239bb90f917f35aed31a842af297a4b5c5d997b0d64cb", "sha256_in_prefix": "9d5e7f87e92cedc62ee239bb90f917f35aed31a842af297a4b5c5d997b0d64cb", "size_in_bytes": 8126}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_find_py_modules.cpython-310.pyc", "path_type": "hardlink", "sha256": "c9a35627da8a23b91e3950cc0880d228b00a6ce2f20f7a91b48951f082183e79", "sha256_in_prefix": "c9a35627da8a23b91e3950cc0880d228b00a6ce2f20f7a91b48951f082183e79", "size_in_bytes": 2663}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_glob.cpython-310.pyc", "path_type": "hardlink", "sha256": "b8fc9a0b43b7b8f725acdb673f525fbee3537fef08d8d0de1672086b5d242d64", "sha256_in_prefix": "b8fc9a0b43b7b8f725acdb673f525fbee3537fef08d8d0de1672086b5d242d64", "size_in_bytes": 953}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_install_scripts.cpython-310.pyc", "path_type": "hardlink", "sha256": "2a07d4dec10c350768d4392b14d849485b81d0d8093f0c24891d16fb5de9acec", "sha256_in_prefix": "2a07d4dec10c350768d4392b14d849485b81d0d8093f0c24891d16fb5de9acec", "size_in_bytes": 3647}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_logging.cpython-310.pyc", "path_type": "hardlink", "sha256": "1724ab6a446b3dec495b7881ae9702072208eb45987c89e199fa2f654edd86d4", "sha256_in_prefix": "1724ab6a446b3dec495b7881ae9702072208eb45987c89e199fa2f654edd86d4", "size_in_bytes": 2025}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_manifest.cpython-310.pyc", "path_type": "hardlink", "sha256": "2590fde4bd12417e24b450fd3c52e1c0d67c0ee61a0b4fdb65e9d3e66eeaa145", "sha256_in_prefix": "2590fde4bd12417e24b450fd3c52e1c0d67c0ee61a0b4fdb65e9d3e66eeaa145", "size_in_bytes": 15914}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_namespaces.cpython-310.pyc", "path_type": "hardlink", "sha256": "8b12a059821e4062d3b0b27b911615fe21a33d41538fc423ef8ab25657e42297", "sha256_in_prefix": "8b12a059821e4062d3b0b27b911615fe21a33d41538fc423ef8ab25657e42297", "size_in_bytes": 3467}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_packageindex.cpython-310.pyc", "path_type": "hardlink", "sha256": "3f649a94efd932e7ef25b22aa4e91dbfd9bc7e31286a9c53bfef4b80b4efb66d", "sha256_in_prefix": "3f649a94efd932e7ef25b22aa4e91dbfd9bc7e31286a9c53bfef4b80b4efb66d", "size_in_bytes": 13922}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_sandbox.cpython-310.pyc", "path_type": "hardlink", "sha256": "9d2ccc0068406e79c29e0eb02deedffe9da5b14acb2fb67943b63e8b7aa16e8b", "sha256_in_prefix": "9d2ccc0068406e79c29e0eb02deedffe9da5b14acb2fb67943b63e8b7aa16e8b", "size_in_bytes": 6238}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_sdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "ea4731fb3a63c736291957688cabcf7c45f56f78a58236c8d502509234bcc8df", "sha256_in_prefix": "ea4731fb3a63c736291957688cabcf7c45f56f78a58236c8d502509234bcc8df", "size_in_bytes": 28599}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_setopt.cpython-310.pyc", "path_type": "hardlink", "sha256": "52217b9123c6229e768670a9202ce03364abcdefed18c5006dc29513ac9abbe1", "sha256_in_prefix": "52217b9123c6229e768670a9202ce03364abcdefed18c5006dc29513ac9abbe1", "size_in_bytes": 1887}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_setuptools.cpython-310.pyc", "path_type": "hardlink", "sha256": "ed140bf10996b02564097f43371ddc8372749273806204d5bdf8479efe519568", "sha256_in_prefix": "ed140bf10996b02564097f43371ddc8372749273806204d5bdf8479efe519568", "size_in_bytes": 9914}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_shutil_wrapper.cpython-310.pyc", "path_type": "hardlink", "sha256": "b8c3c174a5eaec9fffb07925bcc4e58d29961f1727d680cb507f817a6ea50eac", "sha256_in_prefix": "b8c3c174a5eaec9fffb07925bcc4e58d29961f1727d680cb507f817a6ea50eac", "size_in_bytes": 819}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_unicode_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "e645fc11e67fc4075b9890008816b6765121231b4862fd59166dac043ab1d385", "sha256_in_prefix": "e645fc11e67fc4075b9890008816b6765121231b4862fd59166dac043ab1d385", "size_in_bytes": 675}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_virtualenv.cpython-310.pyc", "path_type": "hardlink", "sha256": "8477bb6071866aab7f31330af0ec2717bf6587d67487e3f8c2d7e89252688392", "sha256_in_prefix": "8477bb6071866aab7f31330af0ec2717bf6587d67487e3f8c2d7e89252688392", "size_in_bytes": 2765}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_warnings.cpython-310.pyc", "path_type": "hardlink", "sha256": "3ac1ebe78ad31ad09f02ac4af54cc4d7f30df4dd93a4b6243bc49a1b51c3089b", "sha256_in_prefix": "3ac1ebe78ad31ad09f02ac4af54cc4d7f30df4dd93a4b6243bc49a1b51c3089b", "size_in_bytes": 3190}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_wheel.cpython-310.pyc", "path_type": "hardlink", "sha256": "3e9df0ae2b543892508ee1c2bb9181bcdb9ae62ba8500efa19f56b5476c0d136", "sha256_in_prefix": "3e9df0ae2b543892508ee1c2bb9181bcdb9ae62ba8500efa19f56b5476c0d136", "size_in_bytes": 13199}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/test_windows_wrappers.cpython-310.pyc", "path_type": "hardlink", "sha256": "23ae118e4f0c28cb914fe82a36c4dd9412bbc73049269bc9955ee31913d68db0", "sha256_in_prefix": "23ae118e4f0c28cb914fe82a36c4dd9412bbc73049269bc9955ee31913d68db0", "size_in_bytes": 7403}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/text.cpython-310.pyc", "path_type": "hardlink", "sha256": "fa13afec4f560282f9c4783d7ce588850fd40a857d53c3b051d372bfa71ed3bd", "sha256_in_prefix": "fa13afec4f560282f9c4783d7ce588850fd40a857d53c3b051d372bfa71ed3bd", "size_in_bytes": 383}, {"_path": "Lib/site-packages/setuptools/tests/__pycache__/textwrap.cpython-310.pyc", "path_type": "hardlink", "sha256": "e78ea1590193efe6f8b917370fc64ba215114801053390537f3b7f5f6e037137", "sha256_in_prefix": "e78ea1590193efe6f8b917370fc64ba215114801053390537f3b7f5f6e037137", "size_in_bytes": 308}, {"_path": "Lib/site-packages/setuptools/tests/compat/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/compat/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "d41ced0294eb840df33986459ef02cc411ef2d3151e64aee2a2ea9643e78f16d", "sha256_in_prefix": "d41ced0294eb840df33986459ef02cc411ef2d3151e64aee2a2ea9643e78f16d", "size_in_bytes": 146}, {"_path": "Lib/site-packages/setuptools/tests/compat/__pycache__/py39.cpython-310.pyc", "path_type": "hardlink", "sha256": "5ae8471db286090b6ac13914f6289df8b962a16e4ed4cd77ebe185c6b4d68f42", "sha256_in_prefix": "5ae8471db286090b6ac13914f6289df8b962a16e4ed4cd77ebe185c6b4d68f42", "size_in_bytes": 271}, {"_path": "Lib/site-packages/setuptools/tests/compat/py39.py", "path_type": "hardlink", "sha256": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "sha256_in_prefix": "794cbbfc5fba2914ce20a97ebdeb2152ee88d0475349d059321d04574959d7e8", "size_in_bytes": 135}, {"_path": "Lib/site-packages/setuptools/tests/config/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "869753f8c7abb63c736584b6fbbfa1aa55f3269c8271017a9c606283f7b12d19", "sha256_in_prefix": "869753f8c7abb63c736584b6fbbfa1aa55f3269c8271017a9c606283f7b12d19", "size_in_bytes": 146}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_apply_pyprojecttoml.cpython-310.pyc", "path_type": "hardlink", "sha256": "0b7092a3e74186b2c9cbe72f8aa61bdc9e7309a5d047fa81d8a5b30ec8387c9d", "sha256_in_prefix": "0b7092a3e74186b2c9cbe72f8aa61bdc9e7309a5d047fa81d8a5b30ec8387c9d", "size_in_bytes": 26224}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_expand.cpython-310.pyc", "path_type": "hardlink", "sha256": "fe77550c934ba1bbde74f3d1343b45868ddf7242338f81dbdf24955e89aa4148", "sha256_in_prefix": "fe77550c934ba1bbde74f3d1343b45868ddf7242338f81dbdf24955e89aa4148", "size_in_bytes": 7774}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml.cpython-310.pyc", "path_type": "hardlink", "sha256": "6f4a34c5566cbabb75ce616fdf6230dcfafe6f28c7f1af9064a7850905ed8c58", "sha256_in_prefix": "6f4a34c5566cbabb75ce616fdf6230dcfafe6f28c7f1af9064a7850905ed8c58", "size_in_bytes": 11062}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_pyprojecttoml_dynamic_deps.cpython-310.pyc", "path_type": "hardlink", "sha256": "b346406ef2b4c17565804039158e18c53d328087bbd4c2b0178c3fd439acf1f5", "sha256_in_prefix": "b346406ef2b4c17565804039158e18c53d328087bbd4c2b0178c3fd439acf1f5", "size_in_bytes": 3360}, {"_path": "Lib/site-packages/setuptools/tests/config/__pycache__/test_setupcfg.cpython-310.pyc", "path_type": "hardlink", "sha256": "7e7787baf66e497811508d06a5ad572aa8b942d207c29f19e97706793560b6d8", "sha256_in_prefix": "7e7787baf66e497811508d06a5ad572aa8b942d207c29f19e97706793560b6d8", "size_in_bytes": 28126}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__init__.py", "path_type": "hardlink", "sha256": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "sha256_in_prefix": "f62c670c47722ff6ab29b5337ee8897ed023f5b1b12b3f0cf5a94e159323c7d6", "size_in_bytes": 1827}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "bc584c9db75032f91b14c7c82a5772f6367a7f2c30d497d5b49613062e77c473", "sha256_in_prefix": "bc584c9db75032f91b14c7c82a5772f6367a7f2c30d497d5b49613062e77c473", "size_in_bytes": 2136}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/__pycache__/preload.cpython-310.pyc", "path_type": "hardlink", "sha256": "65dd7e5f23264f9e4cb7a9476ac0600bf0b7253fc1851545abbcafd3459a9f88", "sha256_in_prefix": "65dd7e5f23264f9e4cb7a9476ac0600bf0b7253fc1851545abbcafd3459a9f88", "size_in_bytes": 639}, {"_path": "Lib/site-packages/setuptools/tests/config/downloads/preload.py", "path_type": "hardlink", "sha256": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "sha256_in_prefix": "b081866696377263293308896186181c6da27d9264bc9804a2d445b62ba55752", "size_in_bytes": 450}, {"_path": "Lib/site-packages/setuptools/tests/config/setupcfg_examples.txt", "path_type": "hardlink", "sha256": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "sha256_in_prefix": "7006d5bc26e4159b9350beb1451cd182ac81d2b2ef2537efc370f7d20968d8e1", "size_in_bytes": 1912}, {"_path": "Lib/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py", "path_type": "hardlink", "sha256": "97a9c4e1df162d4fde49646273b552a2a78abfd062ec26461dc12e0767a1936c", "sha256_in_prefix": "97a9c4e1df162d4fde49646273b552a2a78abfd062ec26461dc12e0767a1936c", "size_in_bytes": 28807}, {"_path": "Lib/site-packages/setuptools/tests/config/test_expand.py", "path_type": "hardlink", "sha256": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "sha256_in_prefix": "4b4a13e89be003fa2e8d1e184b8454b9fe6098eb75093415eba4500f357cc5de", "size_in_bytes": 8933}, {"_path": "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml.py", "path_type": "hardlink", "sha256": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "sha256_in_prefix": "d0b79f4a58d4840e8caad279015ccb8689aa65c62214a76eff57240de313d4b6", "size_in_bytes": 12406}, {"_path": "Lib/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py", "path_type": "hardlink", "sha256": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "sha256_in_prefix": "f56ef7fb22e16499af0a23b8ad3890a01a594f9c0d03dd176dde67d870ac85de", "size_in_bytes": 3271}, {"_path": "Lib/site-packages/setuptools/tests/config/test_setupcfg.py", "path_type": "hardlink", "sha256": "66f37e3bed838289f569da7aa0cea297c2567604fdcb5f7a7d1bea11253910b2", "sha256_in_prefix": "66f37e3bed838289f569da7aa0cea297c2567604fdcb5f7a7d1bea11253910b2", "size_in_bytes": 33427}, {"_path": "Lib/site-packages/setuptools/tests/contexts.py", "path_type": "hardlink", "sha256": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "sha256_in_prefix": "4c07592b19a6a1dc75131315a34d68e10a518e9229a385f72162aafc19e3c695", "size_in_bytes": 3480}, {"_path": "Lib/site-packages/setuptools/tests/environment.py", "path_type": "hardlink", "sha256": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "sha256_in_prefix": "f79fd4b536918aebf0602f5e5ca1076e7d36903b59cacbd9ab75192663d48f52", "size_in_bytes": 3102}, {"_path": "Lib/site-packages/setuptools/tests/fixtures.py", "path_type": "hardlink", "sha256": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "sha256_in_prefix": "f95ee20fa05e136134470e9d56f4ce0a6dfa246f194d39eb5e13741884a582b8", "size_in_bytes": 5197}, {"_path": "Lib/site-packages/setuptools/tests/indexes/test_links_priority/external.html", "path_type": "hardlink", "sha256": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "sha256_in_prefix": "78bf5eb8eb84f7724a65daa55f104e9476cac08b8db8876aec6051a6c68f31c5", "size_in_bytes": 92}, {"_path": "Lib/site-packages/setuptools/tests/indexes/test_links_priority/simple/foobar/index.html", "path_type": "hardlink", "sha256": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "sha256_in_prefix": "0c3f932abed4538cc08c71f3e157b1603352033476ee57af4a1d5cfa4dd974b1", "size_in_bytes": 174}, {"_path": "Lib/site-packages/setuptools/tests/integration/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "0490b5f4a20ea7b7d273313fb4bc54cd1e5d5c9ed5ee4ab62566b73de6c0ec83", "sha256_in_prefix": "0490b5f4a20ea7b7d273313fb4bc54cd1e5d5c9ed5ee4ab62566b73de6c0ec83", "size_in_bytes": 151}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/helpers.cpython-310.pyc", "path_type": "hardlink", "sha256": "41993c198792a01d071d435050c01f4f108cf98221023dc972130659a3ade06e", "sha256_in_prefix": "41993c198792a01d071d435050c01f4f108cf98221023dc972130659a3ade06e", "size_in_bytes": 3169}, {"_path": "Lib/site-packages/setuptools/tests/integration/__pycache__/test_pip_install_sdist.cpython-310.pyc", "path_type": "hardlink", "sha256": "f23fed348955f23debebc6921b72769f6c9496f5c7c79fb6830349ee8f74cb75", "sha256_in_prefix": "f23fed348955f23debebc6921b72769f6c9496f5c7c79fb6830349ee8f74cb75", "size_in_bytes": 6071}, {"_path": "Lib/site-packages/setuptools/tests/integration/helpers.py", "path_type": "hardlink", "sha256": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "sha256_in_prefix": "dcf1dc4bd48203e7f05499943f669de4d40eb6d240113239367a1cff1ae83b99", "size_in_bytes": 2522}, {"_path": "Lib/site-packages/setuptools/tests/integration/test_pip_install_sdist.py", "path_type": "hardlink", "sha256": "4856efb9817f843cede8eb6c4391a314d9f19a827f78495fbe962c8b2c8627e8", "sha256_in_prefix": "4856efb9817f843cede8eb6c4391a314d9f19a827f78495fbe962c8b2c8627e8", "size_in_bytes": 8256}, {"_path": "Lib/site-packages/setuptools/tests/mod_with_constant.py", "path_type": "hardlink", "sha256": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "sha256_in_prefix": "5ff2a3f34339e70d6d990e1feee658f7565300ba3884a553e841f1818a1c50c4", "size_in_bytes": 22}, {"_path": "Lib/site-packages/setuptools/tests/namespaces.py", "path_type": "hardlink", "sha256": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "sha256_in_prefix": "1cf708de74793021565e96800c82757f02b1ca671080192ec3cec87393d44804", "size_in_bytes": 2774}, {"_path": "Lib/site-packages/setuptools/tests/script-with-bom.py", "path_type": "hardlink", "sha256": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "sha256_in_prefix": "851460222cc450b1a21bf653368318e3a1e12a1c6959fcb9146703e906e1e5f7", "size_in_bytes": 18}, {"_path": "Lib/site-packages/setuptools/tests/server.py", "path_type": "hardlink", "sha256": "d050d97f471222708fe67d6168aec0c47a378c3dbad512bb0f7f918cff85e779", "sha256_in_prefix": "d050d97f471222708fe67d6168aec0c47a378c3dbad512bb0f7f918cff85e779", "size_in_bytes": 2397}, {"_path": "Lib/site-packages/setuptools/tests/test_archive_util.py", "path_type": "hardlink", "sha256": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "sha256_in_prefix": "6eeb8a758f17916dba3dedc8280a014461c6d0c0ee9a7b8da0f8365ac010cc88", "size_in_bytes": 845}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_deprecations.py", "path_type": "hardlink", "sha256": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "sha256_in_prefix": "ef95eade0627efd2c8232bac125b5b1da9f46c4800b767bf09a2fb28b4bcf8a4", "size_in_bytes": 775}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_egg.py", "path_type": "hardlink", "sha256": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "sha256_in_prefix": "e8f6983751772436c8875b8ad2eaefef2245731f7ccf9767f52389f0cbfdd65f", "size_in_bytes": 1957}, {"_path": "Lib/site-packages/setuptools/tests/test_bdist_wheel.py", "path_type": "hardlink", "sha256": "759f5aece4ff53246f2e7a028b62861086edce11108ccdd8bad60c03a6427b3b", "sha256_in_prefix": "759f5aece4ff53246f2e7a028b62861086edce11108ccdd8bad60c03a6427b3b", "size_in_bytes": 23083}, {"_path": "Lib/site-packages/setuptools/tests/test_build.py", "path_type": "hardlink", "sha256": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "sha256_in_prefix": "c0980ccf68701c00dc2c583e9d7af045586eb3b8639807841a0ae9210c021a70", "size_in_bytes": 798}, {"_path": "Lib/site-packages/setuptools/tests/test_build_clib.py", "path_type": "hardlink", "sha256": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "sha256_in_prefix": "6d7e755d101fe2e3bb22e1c5a6378f9e82bc984ef837682ca1e12a17ea1f830b", "size_in_bytes": 3123}, {"_path": "Lib/site-packages/setuptools/tests/test_build_ext.py", "path_type": "hardlink", "sha256": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "sha256_in_prefix": "7b8652c6c60f079cead4a4aa184b804d9d2dd0f250ccc8638e4289fa12237207", "size_in_bytes": 10099}, {"_path": "Lib/site-packages/setuptools/tests/test_build_meta.py", "path_type": "hardlink", "sha256": "21a929a7d32272f8718bdfc5d913f2636367081d46f746b7f2ce0ee40dc2ba21", "sha256_in_prefix": "21a929a7d32272f8718bdfc5d913f2636367081d46f746b7f2ce0ee40dc2ba21", "size_in_bytes": 34118}, {"_path": "Lib/site-packages/setuptools/tests/test_build_py.py", "path_type": "hardlink", "sha256": "8286cc13f0afcdfe94831abbd2259f5de91bff1cb24fad648708c5abcce4c1fc", "sha256_in_prefix": "8286cc13f0afcdfe94831abbd2259f5de91bff1cb24fad648708c5abcce4c1fc", "size_in_bytes": 14186}, {"_path": "Lib/site-packages/setuptools/tests/test_config_discovery.py", "path_type": "hardlink", "sha256": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "sha256_in_prefix": "16a57e94eb64a9a23e6b2cd4db3a1c49d0f94da4408026678b13438a5280e854", "size_in_bytes": 22580}, {"_path": "Lib/site-packages/setuptools/tests/test_core_metadata.py", "path_type": "hardlink", "sha256": "bdb549e7f2ecc7f86c3bf19d07a9d01172518c0db2771ebfa926ebe4ba617800", "sha256_in_prefix": "bdb549e7f2ecc7f86c3bf19d07a9d01172518c0db2771ebfa926ebe4ba617800", "size_in_bytes": 20881}, {"_path": "Lib/site-packages/setuptools/tests/test_depends.py", "path_type": "hardlink", "sha256": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "sha256_in_prefix": "c90057a106cd425262b7a99b455a33e816f9e777f7b0daead369598a6373e576", "size_in_bytes": 424}, {"_path": "Lib/site-packages/setuptools/tests/test_develop.py", "path_type": "hardlink", "sha256": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "sha256_in_prefix": "08bcd767cf9be7e5454ee6aee0fe325c474bc7551dc9315c39fad5d2ac9421d1", "size_in_bytes": 5142}, {"_path": "Lib/site-packages/setuptools/tests/test_dist.py", "path_type": "hardlink", "sha256": "1858f22f67ad031bd5337abb36114419c5d2e60c8a8fc5736ea71b2b3a6a6ce9", "sha256_in_prefix": "1858f22f67ad031bd5337abb36114419c5d2e60c8a8fc5736ea71b2b3a6a6ce9", "size_in_bytes": 8901}, {"_path": "Lib/site-packages/setuptools/tests/test_dist_info.py", "path_type": "hardlink", "sha256": "e640518fdb6e06c56b781b18db61f67de30efc9419b12a0e64c53f3097d47af6", "sha256_in_prefix": "e640518fdb6e06c56b781b18db61f67de30efc9419b12a0e64c53f3097d47af6", "size_in_bytes": 7077}, {"_path": "Lib/site-packages/setuptools/tests/test_distutils_adoption.py", "path_type": "hardlink", "sha256": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "sha256_in_prefix": "fdeca7ace7f212a5c51268d4261ce97bc1973f24d43ef35239bb38a80026072f", "size_in_bytes": 5987}, {"_path": "Lib/site-packages/setuptools/tests/test_easy_install.py", "path_type": "hardlink", "sha256": "8f1e25a45c9e7b41b8df671d9f0068c370242f889bc3ed1020bc25770bf94822", "sha256_in_prefix": "8f1e25a45c9e7b41b8df671d9f0068c370242f889bc3ed1020bc25770bf94822", "size_in_bytes": 53534}, {"_path": "Lib/site-packages/setuptools/tests/test_editable_install.py", "path_type": "hardlink", "sha256": "ede4c4b694f493b41e572660eb87a1de4667f928dc92e07d2dca243ae577ec32", "sha256_in_prefix": "ede4c4b694f493b41e572660eb87a1de4667f928dc92e07d2dca243ae577ec32", "size_in_bytes": 43383}, {"_path": "Lib/site-packages/setuptools/tests/test_egg_info.py", "path_type": "hardlink", "sha256": "402ce850e905a1c99b9304ba5d4ec5f16373284f02184311c5806a28b81f52b7", "sha256_in_prefix": "402ce850e905a1c99b9304ba5d4ec5f16373284f02184311c5806a28b81f52b7", "size_in_bytes": 44866}, {"_path": "Lib/site-packages/setuptools/tests/test_extern.py", "path_type": "hardlink", "sha256": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "sha256_in_prefix": "ae9294ea809c92cba62f07f94de2a50e5b854344d47db3f04cb41ba71705ac25", "size_in_bytes": 296}, {"_path": "Lib/site-packages/setuptools/tests/test_find_packages.py", "path_type": "hardlink", "sha256": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "sha256_in_prefix": "0932c0713cd619604b09c776680b14564bcede26eb96a7b114174328e58fa2af", "size_in_bytes": 7819}, {"_path": "Lib/site-packages/setuptools/tests/test_find_py_modules.py", "path_type": "hardlink", "sha256": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "sha256_in_prefix": "cd08ee8481b94d03764893e2c7d011a380cbff0f382e7f10b070d48e36ebb404", "size_in_bytes": 2404}, {"_path": "Lib/site-packages/setuptools/tests/test_glob.py", "path_type": "hardlink", "sha256": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "sha256_in_prefix": "3f726fa47fa45d0e01677cef445fb32b13a0c325b3c08690233d161ddc52d249", "size_in_bytes": 887}, {"_path": "Lib/site-packages/setuptools/tests/test_install_scripts.py", "path_type": "hardlink", "sha256": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "sha256_in_prefix": "b1c22b27a6bfb2c2aa838bc804d6948e600a1c460b51467d58a9cf78a9c4ea07", "size_in_bytes": 3433}, {"_path": "Lib/site-packages/setuptools/tests/test_logging.py", "path_type": "hardlink", "sha256": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "sha256_in_prefix": "ce51390e595dba40bb25ce7814dbc357feeec7712b024adfacde424ac9cd3944", "size_in_bytes": 2099}, {"_path": "Lib/site-packages/setuptools/tests/test_manifest.py", "path_type": "hardlink", "sha256": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "sha256_in_prefix": "78c83ae69200e760e2cc1ea6a64b5253e6fc0a3c1a3424b931280bfd5d4bac52", "size_in_bytes": 18562}, {"_path": "Lib/site-packages/setuptools/tests/test_namespaces.py", "path_type": "hardlink", "sha256": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "sha256_in_prefix": "63abada1ee4f1c7a8bfc39606b0a81f45f17a6c5033efbf0d6c40c7a72b4e1ed", "size_in_bytes": 4515}, {"_path": "Lib/site-packages/setuptools/tests/test_packageindex.py", "path_type": "hardlink", "sha256": "a848cb1e94aeda00247a0c04b2dcc7413f8e9b5b902188c0f3378dcc45fbf6ea", "sha256_in_prefix": "a848cb1e94aeda00247a0c04b2dcc7413f8e9b5b902188c0f3378dcc45fbf6ea", "size_in_bytes": 8775}, {"_path": "Lib/site-packages/setuptools/tests/test_sandbox.py", "path_type": "hardlink", "sha256": "b2151613b7cb4d67bb27375f8ba36178159ab86de852e91b515e3a700ac3d2ed", "sha256_in_prefix": "b2151613b7cb4d67bb27375f8ba36178159ab86de852e91b515e3a700ac3d2ed", "size_in_bytes": 4330}, {"_path": "Lib/site-packages/setuptools/tests/test_sdist.py", "path_type": "hardlink", "sha256": "4582ef3dafe77f20b5666a229f3a8ccc9ca74c31b846d3d80b5f7fd0b53aa6fb", "sha256_in_prefix": "4582ef3dafe77f20b5666a229f3a8ccc9ca74c31b846d3d80b5f7fd0b53aa6fb", "size_in_bytes": 32872}, {"_path": "Lib/site-packages/setuptools/tests/test_setopt.py", "path_type": "hardlink", "sha256": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "sha256_in_prefix": "dd5c713380137cff8fe001a70e3a160a71ebe7e8bd0921104c5614d7e1539ef2", "size_in_bytes": 1365}, {"_path": "Lib/site-packages/setuptools/tests/test_setuptools.py", "path_type": "hardlink", "sha256": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "sha256_in_prefix": "fde221a8a7f8e7e3ad1eac517f6d0a9dd39926525d4b43ee14b5c13b733e2cdf", "size_in_bytes": 9008}, {"_path": "Lib/site-packages/setuptools/tests/test_shutil_wrapper.py", "path_type": "hardlink", "sha256": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "sha256_in_prefix": "835e44d753ed6711be227076056345c87facbce6d7c765dc32180c2c93ee1677", "size_in_bytes": 641}, {"_path": "Lib/site-packages/setuptools/tests/test_unicode_utils.py", "path_type": "hardlink", "sha256": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "sha256_in_prefix": "c567c4125f239100adf68b615135c97c599dc804c0160809b36b53c636ee99bc", "size_in_bytes": 316}, {"_path": "Lib/site-packages/setuptools/tests/test_virtualenv.py", "path_type": "hardlink", "sha256": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "sha256_in_prefix": "83e9e30bff494c0b35615c7fd5d189fd0e919489cee2a295bbdf9702035be936", "size_in_bytes": 3730}, {"_path": "Lib/site-packages/setuptools/tests/test_warnings.py", "path_type": "hardlink", "sha256": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "sha256_in_prefix": "cf0476cdc9c2782783a882d994938f01cbb23c7a03bc6bb53ad3956222cc93be", "size_in_bytes": 3347}, {"_path": "Lib/site-packages/setuptools/tests/test_wheel.py", "path_type": "hardlink", "sha256": "27ef375b529d5d38008c5644dc7fb2b68861bc31358aa75b139605e632d09464", "sha256_in_prefix": "27ef375b529d5d38008c5644dc7fb2b68861bc31358aa75b139605e632d09464", "size_in_bytes": 19370}, {"_path": "Lib/site-packages/setuptools/tests/test_windows_wrappers.py", "path_type": "hardlink", "sha256": "685e944e8c0ddf2cc281d061f670d056f6087d262882b4caefbe931325c406a8", "sha256_in_prefix": "685e944e8c0ddf2cc281d061f670d056f6087d262882b4caefbe931325c406a8", "size_in_bytes": 7881}, {"_path": "Lib/site-packages/setuptools/tests/text.py", "path_type": "hardlink", "sha256": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "sha256_in_prefix": "6b5db5f7ba4c553bc1e85016434ba34fc7c84222c8589945025d5409a0d40df8", "size_in_bytes": 123}, {"_path": "Lib/site-packages/setuptools/tests/textwrap.py", "path_type": "hardlink", "sha256": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "sha256_in_prefix": "14d34dabf322684271f3c3e7b1b250211c668f5aa681c00e0975d1b0e0cf24de", "size_in_bytes": 98}, {"_path": "Lib/site-packages/setuptools/unicode_utils.py", "path_type": "hardlink", "sha256": "ba430687ca44030e85fc4cdbf8ae43ddcfb4efc46003f19c174a16ea5838952b", "sha256_in_prefix": "ba430687ca44030e85fc4cdbf8ae43ddcfb4efc46003f19c174a16ea5838952b", "size_in_bytes": 3189}, {"_path": "Lib/site-packages/setuptools/version.py", "path_type": "hardlink", "sha256": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "sha256_in_prefix": "58909e52ecaaef80289364de2bdf8e7b164ebbc5eb950cbbfb2d0112e58da2f4", "size_in_bytes": 161}, {"_path": "Lib/site-packages/setuptools/warnings.py", "path_type": "hardlink", "sha256": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "sha256_in_prefix": "a18d127b978eaa37bf144ca34e0a2751cd171b082cac8e5c826d64930ba5cffc", "size_in_bytes": 3796}, {"_path": "Lib/site-packages/setuptools/wheel.py", "path_type": "hardlink", "sha256": "c6402dbe09bbb8f4f2615db3a95990d3003c90bc0ec914f625eb35cc0cb4ecab", "sha256_in_prefix": "c6402dbe09bbb8f4f2615db3a95990d3003c90bc0ec914f625eb35cc0cb4ecab", "size_in_bytes": 8624}, {"_path": "Lib/site-packages/setuptools/windows_support.py", "path_type": "hardlink", "sha256": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "sha256_in_prefix": "c16e0860b33506fed9d4c69ab8fdb198f8f2cbec249909d7772bd7b1c01ff5fc", "size_in_bytes": 726}], "paths_version": 1}, "requested_spec": "None", "sha256": "870c4fb2112996ccda63d8a54a6eace0591b02a7732bf816156a82ccdfc34c54", "size": 1784668, "subdir": "win-64", "timestamp": 1746024861584, "track_features": "", "url": "https://repo.anaconda.com/pkgs/main/win-64/setuptools-78.1.1-py310haa95532_0.conda", "version": "78.1.1"}