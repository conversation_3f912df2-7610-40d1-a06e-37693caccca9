"""
测试迷宫密封-转子系统程序
"""

import numpy as np
import matplotlib.pyplot as plt
from labyrinth_seal_rotor_system import LabyrinthSealRotorSystem

def test_basic_functionality():
    """测试基本功能"""
    print("测试基本功能...")
    
    # 创建系统
    system = LabyrinthSealRotorSystem()
    
    # 测试密封系数计算
    omega = 340  # rad/s
    eM = 0.1
    K, D, tau_M, mf_eff = system.calculate_seal_coefficients(omega, eM)
    print(f"密封刚度 K: {K:.2e} N/m")
    print(f"密封阻尼 D: {D:.2e} N·s/m")
    print(f"流体惯性系数 tau_M: {tau_M:.4f}")
    print(f"有效质量 mf_eff: {mf_eff:.4f} kg")
    
    # 测试密封力计算
    x, y = 0.0001, 0.0001  # 位移
    vx, vy = 0.01, 0.01    # 速度
    ax, ay = 0, 0          # 加速度
    t = 0                  # 时间
    
    Fsx, Fsy = system.calculate_seal_force(x, y, vx, vy, ax, ay, omega, t)
    print(f"密封力 Fsx: {Fsx:.2f} N")
    print(f"密封力 Fsy: {Fsy:.2f} N")
    
    # 测试联轴器摩擦力计算
    Fx_friction, Fy_friction = system.calculate_coupling_friction_force(vx, vy)
    print(f"摩擦力 Fx: {Fx_friction:.2f} N")
    print(f"摩擦力 Fy: {Fy_friction:.2f} N")
    
    print("基本功能测试完成！\n")

def test_system_solution():
    """测试系统求解"""
    print("测试系统求解...")
    
    system = LabyrinthSealRotorSystem()
    
    # 设置参数
    omega = 340  # rad/s
    t_span = 1.0  # 时间跨度
    initial_conditions = [0.0001, 0.0001, 0, 0]  # 初始条件
    
    # 求解系统
    t, solution = system.solve_system(omega, t_span, initial_conditions)
    x, y, vx, vy = solution.T
    
    print(f"时间点数: {len(t)}")
    print(f"最大位移: {np.max(np.sqrt(x**2 + y**2))*1000:.4f} mm")
    print(f"最大速度: {np.max(np.sqrt(vx**2 + vy**2)):.4f} m/s")
    
    # 简单绘图
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.plot(t, x*1000, 'b-', label='x')
    plt.plot(t, y*1000, 'r-', label='y')
    plt.xlabel('时间 (s)')
    plt.ylabel('位移 (mm)')
    plt.legend()
    plt.grid(True)
    plt.title('位移时间历程')
    
    plt.subplot(1, 3, 2)
    plt.plot(x*1000, y*1000, 'b-')
    plt.xlabel('x (mm)')
    plt.ylabel('y (mm)')
    plt.grid(True)
    plt.title('轨道图')
    plt.axis('equal')
    
    plt.subplot(1, 3, 3)
    displacement = np.sqrt(x**2 + y**2)
    plt.plot(t, displacement*1000, 'k-')
    plt.xlabel('时间 (s)')
    plt.ylabel('位移幅值 (mm)')
    plt.grid(True)
    plt.title('位移幅值')
    
    plt.tight_layout()
    plt.savefig('test_system_solution.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("系统求解测试完成！\n")

def test_parameter_analysis():
    """测试参数分析"""
    print("测试参数分析...")
    
    system = LabyrinthSealRotorSystem()
    
    # 测试动态特性系数计算
    omega_range = np.linspace(200, 500, 10)
    K1, K2, D1, D2 = system.calculate_dynamic_coefficients(omega_range)
    
    print(f"K1 范围: {np.min(K1):.2e} - {np.max(K1):.2e} N/m")
    print(f"K2 范围: {np.min(K2):.2e} - {np.max(K2):.2e} N/m")
    print(f"D1 范围: {np.min(D1):.2e} - {np.max(D1):.2e} N·s/m")
    print(f"D2 范围: {np.min(D2):.2e} - {np.max(D2):.2e} N·s/m")
    
    # 简单绘图
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(omega_range, K1, 'b-o')
    plt.xlabel('转速 (rad/s)')
    plt.ylabel('K1 (N/m)')
    plt.grid(True)
    plt.title('直接刚度 K1')
    
    plt.subplot(2, 2, 2)
    plt.plot(omega_range, K2, 'r-o')
    plt.xlabel('转速 (rad/s)')
    plt.ylabel('K2 (N/m)')
    plt.grid(True)
    plt.title('交叉刚度 K2')
    
    plt.subplot(2, 2, 3)
    plt.plot(omega_range, D1, 'g-o')
    plt.xlabel('转速 (rad/s)')
    plt.ylabel('D1 (N·s/m)')
    plt.grid(True)
    plt.title('直接阻尼 D1')
    
    plt.subplot(2, 2, 4)
    plt.plot(omega_range, D2, 'm-o')
    plt.xlabel('转速 (rad/s)')
    plt.ylabel('D2 (N·s/m)')
    plt.grid(True)
    plt.title('交叉阻尼 D2')
    
    plt.tight_layout()
    plt.savefig('test_parameter_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("参数分析测试完成！\n")

if __name__ == "__main__":
    print("=" * 50)
    print("迷宫密封-转子系统测试程序")
    print("=" * 50)
    
    try:
        test_basic_functionality()
        test_system_solution()
        test_parameter_analysis()
        
        print("=" * 50)
        print("所有测试完成！程序运行正常。")
        print("=" * 50)
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
