# 迷宫密封-转子系统非线性动力学分析

## 项目简介

本项目基于论文 "Nonlinear dynamics analysis of labyrinth seal-rotor system considering internal friction in coupling" (<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>) 实现了考虑联轴器内摩擦的迷宫密封-转子系统非线性动力学分析。

## 主要功能

### 1. 物理模型
- **密封力模型**: 基于Muszynska模型的迷宫密封力计算
- **联轴器内摩擦模型**: 齿套联轴器内齿面滑动产生的摩擦力
- **转子系统动力学**: 考虑密封力和内摩擦的非线性动力学方程

### 2. 分析功能
- 动态特性系数随转速变化分析
- 轴向压降对系统特性的影响分析
- 密封间隙对系统特性的影响分析
- 密封长度对系统特性的影响分析
- 密封力时间历程分析
- 分岔图分析
- 频谱分析
- 轨道图和时间历程分析

### 3. 可视化输出
- 动态特性系数图表
- 分岔图
- 时间历程图
- 轨道图
- 频谱图
- 参数影响分析图

## 文件结构

```
├── labyrinth_seal_rotor_system.py  # 主程序文件
├── test_system.py                  # 测试程序
├── demo_analysis.py               # 演示分析程序
├── README.md                      # 说明文档
└── 1-s2.0-S1007570425000516-main.pdf  # 原始论文
```

## 系统参数

### 结构参数
- 密封半径: 250 mm
- 径向间隙: 0.1 mm
- 齿高: 3 mm
- 密封长度: 50 mm
- 轴向压降: 0.2 MPa
- 齿形压力角: 20°
- 弹性模量: 210 GPa
- 联轴器齿数: 50

### 转子参数
- 转子质量: 100 kg
- 密封盘质量: 10 kg
- 转子刚度: 1 MN/m
- 转子外阻尼: 100 N·s/m
- 不平衡质量: 0.1 kg
- 不平衡半径: 1 mm

## 使用方法

### 1. 基本测试
```bash
python test_system.py
```
运行基本功能测试，验证程序是否正常工作。

### 2. 演示分析
```bash
python demo_analysis.py
```
运行单个案例的详细分析演示。

### 3. 完整分析
```bash
python labyrinth_seal_rotor_system.py
```
运行完整的分析程序，可以选择：
- 完整分析 (复现论文所有图表)
- 单个案例演示

### 4. 自定义分析
```python
from labyrinth_seal_rotor_system import LabyrinthSealRotorSystem

# 创建系统实例
system = LabyrinthSealRotorSystem()

# 修改参数
system.delta_p = 0.4e6  # 修改轴向压降
system.Cr = 0.15e-3     # 修改密封间隙

# 进行分析
omega_range = np.linspace(100, 600, 50)
system.plot_dynamic_coefficients_vs_speed(omega_range)
```

## 主要分析结果

### 1. 动态特性系数
- 直接刚度 K1 和交叉刚度 K2
- 直接阻尼 D1 和交叉阻尼 D2
- 随转速、压降、间隙、长度的变化规律

### 2. 系统稳定性
- 分岔点识别
- 不稳定转速确定
- 多周期运动分析

### 3. 密封力特性
- 水平和垂直密封力
- 时间历程分析
- 频谱特性

### 4. 参数影响
- 轴向压降: 线性影响动态特性系数，提前分岔点
- 密封间隙: 非线性影响，存在最优间隙
- 密封长度: 提高密封效果但降低系统稳定性

## 技术特点

### 1. 数值方法
- 使用Runge-Kutta方法求解微分方程
- 采用FFT进行频谱分析
- 实现分岔图绘制算法

### 2. 物理建模
- 考虑偏心率对密封系数的影响
- 包含联轴器内摩擦的非线性效应
- 综合考虑不平衡力、重力、密封力等多种激励

### 3. 可视化
- 高质量图表输出
- 多种分析图表类型
- 支持参数扫描分析

## 依赖库

```
numpy
matplotlib
scipy
```

## 安装依赖

```bash
pip install numpy matplotlib scipy
```

## 注意事项

1. 程序中的某些经验系数可能需要根据具体应用调整
2. 计算时间较长的分析建议减少参数点数
3. 图表中的中文显示可能需要配置字体

## 论文对应关系

- 图4: `plot_dynamic_coefficients_vs_speed()`
- 图5: `analyze_pressure_drop_effect()`
- 图6: `analyze_seal_force_time_history()`
- 图7: `plot_bifurcation_diagram()`
- 图8: `frequency_analysis()`
- 图9: `plot_orbit_and_time_history()`
- 图10: `analyze_clearance_effect()`
- 图13: `analyze_length_effect()`

## 联系方式

如有问题或建议，请参考原始论文或相关文献。

## 许可证

本项目仅用于学术研究和教学目的。
