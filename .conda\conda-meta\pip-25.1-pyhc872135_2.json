{"build": "pyhc872135_2", "build_number": 2, "channel": "https://repo.anaconda.com/pkgs/main/noarch", "constrains": [], "depends": ["python >=3.9,<3.14.0a0", "setuptools", "wheel"], "extracted_package_dir": "C:\\Users\\<USER>\\anaconda3\\pkgs\\pip-25.1-pyhc872135_2", "features": "", "files": ["Lib/site-packages/pip-25.1.dist-info/INSTALLER", "Lib/site-packages/pip-25.1.dist-info/METADATA", "Lib/site-packages/pip-25.1.dist-info/RECORD", "Lib/site-packages/pip-25.1.dist-info/REQUESTED", "Lib/site-packages/pip-25.1.dist-info/WHEEL", "Lib/site-packages/pip-25.1.dist-info/direct_url.json", "Lib/site-packages/pip-25.1.dist-info/entry_points.txt", "Lib/site-packages/pip-25.1.dist-info/licenses/AUTHORS.txt", "Lib/site-packages/pip-25.1.dist-info/licenses/LICENSE.txt", "Lib/site-packages/pip-25.1.dist-info/top_level.txt", "Lib/site-packages/pip/__init__.py", "Lib/site-packages/pip/__main__.py", "Lib/site-packages/pip/__pip-runner__.py", "Lib/site-packages/pip/_internal/__init__.py", "Lib/site-packages/pip/_internal/build_env.py", "Lib/site-packages/pip/_internal/cache.py", "Lib/site-packages/pip/_internal/cli/__init__.py", "Lib/site-packages/pip/_internal/cli/autocompletion.py", "Lib/site-packages/pip/_internal/cli/base_command.py", "Lib/site-packages/pip/_internal/cli/cmdoptions.py", "Lib/site-packages/pip/_internal/cli/command_context.py", "Lib/site-packages/pip/_internal/cli/index_command.py", "Lib/site-packages/pip/_internal/cli/main.py", "Lib/site-packages/pip/_internal/cli/main_parser.py", "Lib/site-packages/pip/_internal/cli/parser.py", "Lib/site-packages/pip/_internal/cli/progress_bars.py", "Lib/site-packages/pip/_internal/cli/req_command.py", "Lib/site-packages/pip/_internal/cli/spinners.py", "Lib/site-packages/pip/_internal/cli/status_codes.py", "Lib/site-packages/pip/_internal/commands/__init__.py", "Lib/site-packages/pip/_internal/commands/cache.py", "Lib/site-packages/pip/_internal/commands/check.py", "Lib/site-packages/pip/_internal/commands/completion.py", "Lib/site-packages/pip/_internal/commands/configuration.py", "Lib/site-packages/pip/_internal/commands/debug.py", "Lib/site-packages/pip/_internal/commands/download.py", "Lib/site-packages/pip/_internal/commands/freeze.py", "Lib/site-packages/pip/_internal/commands/hash.py", "Lib/site-packages/pip/_internal/commands/help.py", "Lib/site-packages/pip/_internal/commands/index.py", "Lib/site-packages/pip/_internal/commands/inspect.py", "Lib/site-packages/pip/_internal/commands/install.py", "Lib/site-packages/pip/_internal/commands/list.py", "Lib/site-packages/pip/_internal/commands/lock.py", "Lib/site-packages/pip/_internal/commands/search.py", "Lib/site-packages/pip/_internal/commands/show.py", "Lib/site-packages/pip/_internal/commands/uninstall.py", "Lib/site-packages/pip/_internal/commands/wheel.py", "Lib/site-packages/pip/_internal/configuration.py", "Lib/site-packages/pip/_internal/distributions/__init__.py", "Lib/site-packages/pip/_internal/distributions/base.py", "Lib/site-packages/pip/_internal/distributions/installed.py", "Lib/site-packages/pip/_internal/distributions/sdist.py", "Lib/site-packages/pip/_internal/distributions/wheel.py", "Lib/site-packages/pip/_internal/exceptions.py", "Lib/site-packages/pip/_internal/index/__init__.py", "Lib/site-packages/pip/_internal/index/collector.py", "Lib/site-packages/pip/_internal/index/package_finder.py", "Lib/site-packages/pip/_internal/index/sources.py", "Lib/site-packages/pip/_internal/locations/__init__.py", "Lib/site-packages/pip/_internal/locations/_distutils.py", "Lib/site-packages/pip/_internal/locations/_sysconfig.py", "Lib/site-packages/pip/_internal/locations/base.py", "Lib/site-packages/pip/_internal/main.py", "Lib/site-packages/pip/_internal/metadata/__init__.py", "Lib/site-packages/pip/_internal/metadata/_json.py", "Lib/site-packages/pip/_internal/metadata/base.py", "Lib/site-packages/pip/_internal/metadata/importlib/__init__.py", "Lib/site-packages/pip/_internal/metadata/importlib/_compat.py", "Lib/site-packages/pip/_internal/metadata/importlib/_dists.py", "Lib/site-packages/pip/_internal/metadata/importlib/_envs.py", "Lib/site-packages/pip/_internal/metadata/pkg_resources.py", "Lib/site-packages/pip/_internal/models/__init__.py", "Lib/site-packages/pip/_internal/models/candidate.py", "Lib/site-packages/pip/_internal/models/direct_url.py", "Lib/site-packages/pip/_internal/models/format_control.py", "Lib/site-packages/pip/_internal/models/index.py", "Lib/site-packages/pip/_internal/models/installation_report.py", "Lib/site-packages/pip/_internal/models/link.py", "Lib/site-packages/pip/_internal/models/pylock.py", "Lib/site-packages/pip/_internal/models/scheme.py", "Lib/site-packages/pip/_internal/models/search_scope.py", "Lib/site-packages/pip/_internal/models/selection_prefs.py", "Lib/site-packages/pip/_internal/models/target_python.py", "Lib/site-packages/pip/_internal/models/wheel.py", "Lib/site-packages/pip/_internal/network/__init__.py", "Lib/site-packages/pip/_internal/network/auth.py", "Lib/site-packages/pip/_internal/network/cache.py", "Lib/site-packages/pip/_internal/network/download.py", "Lib/site-packages/pip/_internal/network/lazy_wheel.py", "Lib/site-packages/pip/_internal/network/session.py", "Lib/site-packages/pip/_internal/network/utils.py", "Lib/site-packages/pip/_internal/network/xmlrpc.py", "Lib/site-packages/pip/_internal/operations/__init__.py", "Lib/site-packages/pip/_internal/operations/build/__init__.py", "Lib/site-packages/pip/_internal/operations/build/build_tracker.py", "Lib/site-packages/pip/_internal/operations/build/metadata.py", "Lib/site-packages/pip/_internal/operations/build/metadata_editable.py", "Lib/site-packages/pip/_internal/operations/build/metadata_legacy.py", "Lib/site-packages/pip/_internal/operations/build/wheel.py", "Lib/site-packages/pip/_internal/operations/build/wheel_editable.py", "Lib/site-packages/pip/_internal/operations/build/wheel_legacy.py", "Lib/site-packages/pip/_internal/operations/check.py", "Lib/site-packages/pip/_internal/operations/freeze.py", "Lib/site-packages/pip/_internal/operations/install/__init__.py", "Lib/site-packages/pip/_internal/operations/install/editable_legacy.py", "Lib/site-packages/pip/_internal/operations/install/wheel.py", "Lib/site-packages/pip/_internal/operations/prepare.py", "Lib/site-packages/pip/_internal/pyproject.py", "Lib/site-packages/pip/_internal/req/__init__.py", "Lib/site-packages/pip/_internal/req/constructors.py", "Lib/site-packages/pip/_internal/req/req_dependency_group.py", "Lib/site-packages/pip/_internal/req/req_file.py", "Lib/site-packages/pip/_internal/req/req_install.py", "Lib/site-packages/pip/_internal/req/req_set.py", "Lib/site-packages/pip/_internal/req/req_uninstall.py", "Lib/site-packages/pip/_internal/resolution/__init__.py", "Lib/site-packages/pip/_internal/resolution/base.py", "Lib/site-packages/pip/_internal/resolution/legacy/__init__.py", "Lib/site-packages/pip/_internal/resolution/legacy/resolver.py", "Lib/site-packages/pip/_internal/resolution/resolvelib/__init__.py", "Lib/site-packages/pip/_internal/resolution/resolvelib/base.py", "Lib/site-packages/pip/_internal/resolution/resolvelib/candidates.py", "Lib/site-packages/pip/_internal/resolution/resolvelib/factory.py", "Lib/site-packages/pip/_internal/resolution/resolvelib/found_candidates.py", "Lib/site-packages/pip/_internal/resolution/resolvelib/provider.py", "Lib/site-packages/pip/_internal/resolution/resolvelib/reporter.py", "Lib/site-packages/pip/_internal/resolution/resolvelib/requirements.py", "Lib/site-packages/pip/_internal/resolution/resolvelib/resolver.py", "Lib/site-packages/pip/_internal/self_outdated_check.py", "Lib/site-packages/pip/_internal/utils/__init__.py", "Lib/site-packages/pip/_internal/utils/_jaraco_text.py", "Lib/site-packages/pip/_internal/utils/_log.py", "Lib/site-packages/pip/_internal/utils/appdirs.py", "Lib/site-packages/pip/_internal/utils/compat.py", "Lib/site-packages/pip/_internal/utils/compatibility_tags.py", "Lib/site-packages/pip/_internal/utils/datetime.py", "Lib/site-packages/pip/_internal/utils/deprecation.py", "Lib/site-packages/pip/_internal/utils/direct_url_helpers.py", "Lib/site-packages/pip/_internal/utils/egg_link.py", "Lib/site-packages/pip/_internal/utils/entrypoints.py", "Lib/site-packages/pip/_internal/utils/filesystem.py", "Lib/site-packages/pip/_internal/utils/filetypes.py", "Lib/site-packages/pip/_internal/utils/glibc.py", "Lib/site-packages/pip/_internal/utils/hashes.py", "Lib/site-packages/pip/_internal/utils/logging.py", "Lib/site-packages/pip/_internal/utils/misc.py", "Lib/site-packages/pip/_internal/utils/packaging.py", "Lib/site-packages/pip/_internal/utils/retry.py", "Lib/site-packages/pip/_internal/utils/setuptools_build.py", "Lib/site-packages/pip/_internal/utils/subprocess.py", "Lib/site-packages/pip/_internal/utils/temp_dir.py", "Lib/site-packages/pip/_internal/utils/unpacking.py", "Lib/site-packages/pip/_internal/utils/urls.py", "Lib/site-packages/pip/_internal/utils/virtualenv.py", "Lib/site-packages/pip/_internal/utils/wheel.py", "Lib/site-packages/pip/_internal/vcs/__init__.py", "Lib/site-packages/pip/_internal/vcs/bazaar.py", "Lib/site-packages/pip/_internal/vcs/git.py", "Lib/site-packages/pip/_internal/vcs/mercurial.py", "Lib/site-packages/pip/_internal/vcs/subversion.py", "Lib/site-packages/pip/_internal/vcs/versioncontrol.py", "Lib/site-packages/pip/_internal/wheel_builder.py", "Lib/site-packages/pip/_vendor/__init__.py", "Lib/site-packages/pip/_vendor/cachecontrol/__init__.py", "Lib/site-packages/pip/_vendor/cachecontrol/_cmd.py", "Lib/site-packages/pip/_vendor/cachecontrol/adapter.py", "Lib/site-packages/pip/_vendor/cachecontrol/cache.py", "Lib/site-packages/pip/_vendor/cachecontrol/caches/__init__.py", "Lib/site-packages/pip/_vendor/cachecontrol/caches/file_cache.py", "Lib/site-packages/pip/_vendor/cachecontrol/caches/redis_cache.py", "Lib/site-packages/pip/_vendor/cachecontrol/controller.py", "Lib/site-packages/pip/_vendor/cachecontrol/filewrapper.py", "Lib/site-packages/pip/_vendor/cachecontrol/heuristics.py", "Lib/site-packages/pip/_vendor/cachecontrol/py.typed", "Lib/site-packages/pip/_vendor/cachecontrol/serialize.py", "Lib/site-packages/pip/_vendor/cachecontrol/wrapper.py", "Lib/site-packages/pip/_vendor/certifi/__init__.py", "Lib/site-packages/pip/_vendor/certifi/__main__.py", "Lib/site-packages/pip/_vendor/certifi/cacert.pem", "Lib/site-packages/pip/_vendor/certifi/core.py", "Lib/site-packages/pip/_vendor/certifi/py.typed", "Lib/site-packages/pip/_vendor/dependency_groups/__init__.py", "Lib/site-packages/pip/_vendor/dependency_groups/__main__.py", "Lib/site-packages/pip/_vendor/dependency_groups/_implementation.py", "Lib/site-packages/pip/_vendor/dependency_groups/_lint_dependency_groups.py", "Lib/site-packages/pip/_vendor/dependency_groups/_pip_wrapper.py", "Lib/site-packages/pip/_vendor/dependency_groups/_toml_compat.py", "Lib/site-packages/pip/_vendor/dependency_groups/py.typed", "Lib/site-packages/pip/_vendor/distlib/__init__.py", "Lib/site-packages/pip/_vendor/distlib/compat.py", "Lib/site-packages/pip/_vendor/distlib/database.py", "Lib/site-packages/pip/_vendor/distlib/index.py", "Lib/site-packages/pip/_vendor/distlib/locators.py", "Lib/site-packages/pip/_vendor/distlib/manifest.py", "Lib/site-packages/pip/_vendor/distlib/markers.py", "Lib/site-packages/pip/_vendor/distlib/metadata.py", "Lib/site-packages/pip/_vendor/distlib/resources.py", "Lib/site-packages/pip/_vendor/distlib/scripts.py", "Lib/site-packages/pip/_vendor/distlib/t32.exe", "Lib/site-packages/pip/_vendor/distlib/t64-arm.exe", "Lib/site-packages/pip/_vendor/distlib/t64.exe", "Lib/site-packages/pip/_vendor/distlib/util.py", "Lib/site-packages/pip/_vendor/distlib/version.py", "Lib/site-packages/pip/_vendor/distlib/w32.exe", "Lib/site-packages/pip/_vendor/distlib/w64-arm.exe", "Lib/site-packages/pip/_vendor/distlib/w64.exe", "Lib/site-packages/pip/_vendor/distlib/wheel.py", "Lib/site-packages/pip/_vendor/distro/__init__.py", "Lib/site-packages/pip/_vendor/distro/__main__.py", "Lib/site-packages/pip/_vendor/distro/distro.py", "Lib/site-packages/pip/_vendor/distro/py.typed", "Lib/site-packages/pip/_vendor/idna/__init__.py", "Lib/site-packages/pip/_vendor/idna/codec.py", "Lib/site-packages/pip/_vendor/idna/compat.py", "Lib/site-packages/pip/_vendor/idna/core.py", "Lib/site-packages/pip/_vendor/idna/idnadata.py", "Lib/site-packages/pip/_vendor/idna/intranges.py", "Lib/site-packages/pip/_vendor/idna/package_data.py", "Lib/site-packages/pip/_vendor/idna/py.typed", "Lib/site-packages/pip/_vendor/idna/uts46data.py", "Lib/site-packages/pip/_vendor/msgpack/__init__.py", "Lib/site-packages/pip/_vendor/msgpack/exceptions.py", "Lib/site-packages/pip/_vendor/msgpack/ext.py", "Lib/site-packages/pip/_vendor/msgpack/fallback.py", "Lib/site-packages/pip/_vendor/packaging/__init__.py", "Lib/site-packages/pip/_vendor/packaging/_elffile.py", "Lib/site-packages/pip/_vendor/packaging/_manylinux.py", "Lib/site-packages/pip/_vendor/packaging/_musllinux.py", "Lib/site-packages/pip/_vendor/packaging/_parser.py", "Lib/site-packages/pip/_vendor/packaging/_structures.py", "Lib/site-packages/pip/_vendor/packaging/_tokenizer.py", "Lib/site-packages/pip/_vendor/packaging/licenses/__init__.py", "Lib/site-packages/pip/_vendor/packaging/licenses/_spdx.py", "Lib/site-packages/pip/_vendor/packaging/markers.py", "Lib/site-packages/pip/_vendor/packaging/metadata.py", "Lib/site-packages/pip/_vendor/packaging/py.typed", "Lib/site-packages/pip/_vendor/packaging/requirements.py", "Lib/site-packages/pip/_vendor/packaging/specifiers.py", "Lib/site-packages/pip/_vendor/packaging/tags.py", "Lib/site-packages/pip/_vendor/packaging/utils.py", "Lib/site-packages/pip/_vendor/packaging/version.py", "Lib/site-packages/pip/_vendor/pkg_resources/__init__.py", "Lib/site-packages/pip/_vendor/platformdirs/__init__.py", "Lib/site-packages/pip/_vendor/platformdirs/__main__.py", "Lib/site-packages/pip/_vendor/platformdirs/android.py", "Lib/site-packages/pip/_vendor/platformdirs/api.py", "Lib/site-packages/pip/_vendor/platformdirs/macos.py", "Lib/site-packages/pip/_vendor/platformdirs/py.typed", "Lib/site-packages/pip/_vendor/platformdirs/unix.py", "Lib/site-packages/pip/_vendor/platformdirs/version.py", "Lib/site-packages/pip/_vendor/platformdirs/windows.py", "Lib/site-packages/pip/_vendor/pygments/__init__.py", "Lib/site-packages/pip/_vendor/pygments/__main__.py", "Lib/site-packages/pip/_vendor/pygments/console.py", "Lib/site-packages/pip/_vendor/pygments/filter.py", "Lib/site-packages/pip/_vendor/pygments/filters/__init__.py", "Lib/site-packages/pip/_vendor/pygments/formatter.py", "Lib/site-packages/pip/_vendor/pygments/formatters/__init__.py", "Lib/site-packages/pip/_vendor/pygments/formatters/_mapping.py", "Lib/site-packages/pip/_vendor/pygments/lexer.py", "Lib/site-packages/pip/_vendor/pygments/lexers/__init__.py", "Lib/site-packages/pip/_vendor/pygments/lexers/_mapping.py", "Lib/site-packages/pip/_vendor/pygments/lexers/python.py", "Lib/site-packages/pip/_vendor/pygments/modeline.py", "Lib/site-packages/pip/_vendor/pygments/plugin.py", "Lib/site-packages/pip/_vendor/pygments/regexopt.py", "Lib/site-packages/pip/_vendor/pygments/scanner.py", "Lib/site-packages/pip/_vendor/pygments/sphinxext.py", "Lib/site-packages/pip/_vendor/pygments/style.py", "Lib/site-packages/pip/_vendor/pygments/styles/__init__.py", "Lib/site-packages/pip/_vendor/pygments/styles/_mapping.py", "Lib/site-packages/pip/_vendor/pygments/token.py", "Lib/site-packages/pip/_vendor/pygments/unistring.py", "Lib/site-packages/pip/_vendor/pygments/util.py", "Lib/site-packages/pip/_vendor/pyproject_hooks/__init__.py", "Lib/site-packages/pip/_vendor/pyproject_hooks/_impl.py", "Lib/site-packages/pip/_vendor/pyproject_hooks/_in_process/__init__.py", "Lib/site-packages/pip/_vendor/pyproject_hooks/_in_process/_in_process.py", "Lib/site-packages/pip/_vendor/pyproject_hooks/py.typed", "Lib/site-packages/pip/_vendor/requests/__init__.py", "Lib/site-packages/pip/_vendor/requests/__version__.py", "Lib/site-packages/pip/_vendor/requests/_internal_utils.py", "Lib/site-packages/pip/_vendor/requests/adapters.py", "Lib/site-packages/pip/_vendor/requests/api.py", "Lib/site-packages/pip/_vendor/requests/auth.py", "Lib/site-packages/pip/_vendor/requests/certs.py", "Lib/site-packages/pip/_vendor/requests/compat.py", "Lib/site-packages/pip/_vendor/requests/cookies.py", "Lib/site-packages/pip/_vendor/requests/exceptions.py", "Lib/site-packages/pip/_vendor/requests/help.py", "Lib/site-packages/pip/_vendor/requests/hooks.py", "Lib/site-packages/pip/_vendor/requests/models.py", "Lib/site-packages/pip/_vendor/requests/packages.py", "Lib/site-packages/pip/_vendor/requests/sessions.py", "Lib/site-packages/pip/_vendor/requests/status_codes.py", "Lib/site-packages/pip/_vendor/requests/structures.py", "Lib/site-packages/pip/_vendor/requests/utils.py", "Lib/site-packages/pip/_vendor/resolvelib/__init__.py", "Lib/site-packages/pip/_vendor/resolvelib/providers.py", "Lib/site-packages/pip/_vendor/resolvelib/py.typed", "Lib/site-packages/pip/_vendor/resolvelib/reporters.py", "Lib/site-packages/pip/_vendor/resolvelib/resolvers/__init__.py", "Lib/site-packages/pip/_vendor/resolvelib/resolvers/abstract.py", "Lib/site-packages/pip/_vendor/resolvelib/resolvers/criterion.py", "Lib/site-packages/pip/_vendor/resolvelib/resolvers/exceptions.py", "Lib/site-packages/pip/_vendor/resolvelib/resolvers/resolution.py", "Lib/site-packages/pip/_vendor/resolvelib/structs.py", "Lib/site-packages/pip/_vendor/rich/__init__.py", "Lib/site-packages/pip/_vendor/rich/__main__.py", "Lib/site-packages/pip/_vendor/rich/_cell_widths.py", "Lib/site-packages/pip/_vendor/rich/_emoji_codes.py", "Lib/site-packages/pip/_vendor/rich/_emoji_replace.py", "Lib/site-packages/pip/_vendor/rich/_export_format.py", "Lib/site-packages/pip/_vendor/rich/_extension.py", "Lib/site-packages/pip/_vendor/rich/_fileno.py", "Lib/site-packages/pip/_vendor/rich/_inspect.py", "Lib/site-packages/pip/_vendor/rich/_log_render.py", "Lib/site-packages/pip/_vendor/rich/_loop.py", "Lib/site-packages/pip/_vendor/rich/_null_file.py", "Lib/site-packages/pip/_vendor/rich/_palettes.py", "Lib/site-packages/pip/_vendor/rich/_pick.py", "Lib/site-packages/pip/_vendor/rich/_ratio.py", "Lib/site-packages/pip/_vendor/rich/_spinners.py", "Lib/site-packages/pip/_vendor/rich/_stack.py", "Lib/site-packages/pip/_vendor/rich/_timer.py", "Lib/site-packages/pip/_vendor/rich/_win32_console.py", "Lib/site-packages/pip/_vendor/rich/_windows.py", "Lib/site-packages/pip/_vendor/rich/_windows_renderer.py", "Lib/site-packages/pip/_vendor/rich/_wrap.py", "Lib/site-packages/pip/_vendor/rich/abc.py", "Lib/site-packages/pip/_vendor/rich/align.py", "Lib/site-packages/pip/_vendor/rich/ansi.py", "Lib/site-packages/pip/_vendor/rich/bar.py", "Lib/site-packages/pip/_vendor/rich/box.py", "Lib/site-packages/pip/_vendor/rich/cells.py", "Lib/site-packages/pip/_vendor/rich/color.py", "Lib/site-packages/pip/_vendor/rich/color_triplet.py", "Lib/site-packages/pip/_vendor/rich/columns.py", "Lib/site-packages/pip/_vendor/rich/console.py", "Lib/site-packages/pip/_vendor/rich/constrain.py", "Lib/site-packages/pip/_vendor/rich/containers.py", "Lib/site-packages/pip/_vendor/rich/control.py", "Lib/site-packages/pip/_vendor/rich/default_styles.py", "Lib/site-packages/pip/_vendor/rich/diagnose.py", "Lib/site-packages/pip/_vendor/rich/emoji.py", "Lib/site-packages/pip/_vendor/rich/errors.py", "Lib/site-packages/pip/_vendor/rich/file_proxy.py", "Lib/site-packages/pip/_vendor/rich/filesize.py", "Lib/site-packages/pip/_vendor/rich/highlighter.py", "Lib/site-packages/pip/_vendor/rich/json.py", "Lib/site-packages/pip/_vendor/rich/jupyter.py", "Lib/site-packages/pip/_vendor/rich/layout.py", "Lib/site-packages/pip/_vendor/rich/live.py", "Lib/site-packages/pip/_vendor/rich/live_render.py", "Lib/site-packages/pip/_vendor/rich/logging.py", "Lib/site-packages/pip/_vendor/rich/markup.py", "Lib/site-packages/pip/_vendor/rich/measure.py", "Lib/site-packages/pip/_vendor/rich/padding.py", "Lib/site-packages/pip/_vendor/rich/pager.py", "Lib/site-packages/pip/_vendor/rich/palette.py", "Lib/site-packages/pip/_vendor/rich/panel.py", "Lib/site-packages/pip/_vendor/rich/pretty.py", "Lib/site-packages/pip/_vendor/rich/progress.py", "Lib/site-packages/pip/_vendor/rich/progress_bar.py", "Lib/site-packages/pip/_vendor/rich/prompt.py", "Lib/site-packages/pip/_vendor/rich/protocol.py", "Lib/site-packages/pip/_vendor/rich/py.typed", "Lib/site-packages/pip/_vendor/rich/region.py", "Lib/site-packages/pip/_vendor/rich/repr.py", "Lib/site-packages/pip/_vendor/rich/rule.py", "Lib/site-packages/pip/_vendor/rich/scope.py", "Lib/site-packages/pip/_vendor/rich/screen.py", "Lib/site-packages/pip/_vendor/rich/segment.py", "Lib/site-packages/pip/_vendor/rich/spinner.py", "Lib/site-packages/pip/_vendor/rich/status.py", "Lib/site-packages/pip/_vendor/rich/style.py", "Lib/site-packages/pip/_vendor/rich/styled.py", "Lib/site-packages/pip/_vendor/rich/syntax.py", "Lib/site-packages/pip/_vendor/rich/table.py", "Lib/site-packages/pip/_vendor/rich/terminal_theme.py", "Lib/site-packages/pip/_vendor/rich/text.py", "Lib/site-packages/pip/_vendor/rich/theme.py", "Lib/site-packages/pip/_vendor/rich/themes.py", "Lib/site-packages/pip/_vendor/rich/traceback.py", "Lib/site-packages/pip/_vendor/rich/tree.py", "Lib/site-packages/pip/_vendor/tomli/__init__.py", "Lib/site-packages/pip/_vendor/tomli/_parser.py", "Lib/site-packages/pip/_vendor/tomli/_re.py", "Lib/site-packages/pip/_vendor/tomli/_types.py", "Lib/site-packages/pip/_vendor/tomli/py.typed", "Lib/site-packages/pip/_vendor/tomli_w/__init__.py", "Lib/site-packages/pip/_vendor/tomli_w/_writer.py", "Lib/site-packages/pip/_vendor/tomli_w/py.typed", "Lib/site-packages/pip/_vendor/truststore/__init__.py", "Lib/site-packages/pip/_vendor/truststore/_api.py", "Lib/site-packages/pip/_vendor/truststore/_macos.py", "Lib/site-packages/pip/_vendor/truststore/_openssl.py", "Lib/site-packages/pip/_vendor/truststore/_ssl_constants.py", "Lib/site-packages/pip/_vendor/truststore/_windows.py", "Lib/site-packages/pip/_vendor/truststore/py.typed", "Lib/site-packages/pip/_vendor/typing_extensions.py", "Lib/site-packages/pip/_vendor/urllib3/__init__.py", "Lib/site-packages/pip/_vendor/urllib3/_collections.py", "Lib/site-packages/pip/_vendor/urllib3/_version.py", "Lib/site-packages/pip/_vendor/urllib3/connection.py", "Lib/site-packages/pip/_vendor/urllib3/connectionpool.py", "Lib/site-packages/pip/_vendor/urllib3/contrib/__init__.py", "Lib/site-packages/pip/_vendor/urllib3/contrib/_appengine_environ.py", "Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__init__.py", "Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/bindings.py", "Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/low_level.py", "Lib/site-packages/pip/_vendor/urllib3/contrib/appengine.py", "Lib/site-packages/pip/_vendor/urllib3/contrib/ntlmpool.py", "Lib/site-packages/pip/_vendor/urllib3/contrib/pyopenssl.py", "Lib/site-packages/pip/_vendor/urllib3/contrib/securetransport.py", "Lib/site-packages/pip/_vendor/urllib3/contrib/socks.py", "Lib/site-packages/pip/_vendor/urllib3/exceptions.py", "Lib/site-packages/pip/_vendor/urllib3/fields.py", "Lib/site-packages/pip/_vendor/urllib3/filepost.py", "Lib/site-packages/pip/_vendor/urllib3/packages/__init__.py", "Lib/site-packages/pip/_vendor/urllib3/packages/backports/__init__.py", "Lib/site-packages/pip/_vendor/urllib3/packages/backports/makefile.py", "Lib/site-packages/pip/_vendor/urllib3/packages/backports/weakref_finalize.py", "Lib/site-packages/pip/_vendor/urllib3/packages/six.py", "Lib/site-packages/pip/_vendor/urllib3/poolmanager.py", "Lib/site-packages/pip/_vendor/urllib3/request.py", "Lib/site-packages/pip/_vendor/urllib3/response.py", "Lib/site-packages/pip/_vendor/urllib3/util/__init__.py", "Lib/site-packages/pip/_vendor/urllib3/util/connection.py", "Lib/site-packages/pip/_vendor/urllib3/util/proxy.py", "Lib/site-packages/pip/_vendor/urllib3/util/queue.py", "Lib/site-packages/pip/_vendor/urllib3/util/request.py", "Lib/site-packages/pip/_vendor/urllib3/util/response.py", "Lib/site-packages/pip/_vendor/urllib3/util/retry.py", "Lib/site-packages/pip/_vendor/urllib3/util/ssl_.py", "Lib/site-packages/pip/_vendor/urllib3/util/ssl_match_hostname.py", "Lib/site-packages/pip/_vendor/urllib3/util/ssltransport.py", "Lib/site-packages/pip/_vendor/urllib3/util/timeout.py", "Lib/site-packages/pip/_vendor/urllib3/util/url.py", "Lib/site-packages/pip/_vendor/urllib3/util/wait.py", "Lib/site-packages/pip/_vendor/vendor.txt", "Lib/site-packages/pip/py.typed", ".nonadmin", "Lib/site-packages/pip/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/pip/__pycache__/__pip-runner__.cpython-310.pyc", "Lib/site-packages/pip/_internal/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/__pycache__/build_env.cpython-310.pyc", "Lib/site-packages/pip/_internal/__pycache__/cache.cpython-310.pyc", "Lib/site-packages/pip/_internal/cli/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/cli/__pycache__/autocompletion.cpython-310.pyc", "Lib/site-packages/pip/_internal/cli/__pycache__/base_command.cpython-310.pyc", "Lib/site-packages/pip/_internal/cli/__pycache__/cmdoptions.cpython-310.pyc", "Lib/site-packages/pip/_internal/cli/__pycache__/command_context.cpython-310.pyc", "Lib/site-packages/pip/_internal/cli/__pycache__/index_command.cpython-310.pyc", "Lib/site-packages/pip/_internal/cli/__pycache__/main.cpython-310.pyc", "Lib/site-packages/pip/_internal/cli/__pycache__/main_parser.cpython-310.pyc", "Lib/site-packages/pip/_internal/cli/__pycache__/parser.cpython-310.pyc", "Lib/site-packages/pip/_internal/cli/__pycache__/progress_bars.cpython-310.pyc", "Lib/site-packages/pip/_internal/cli/__pycache__/req_command.cpython-310.pyc", "Lib/site-packages/pip/_internal/cli/__pycache__/spinners.cpython-310.pyc", "Lib/site-packages/pip/_internal/cli/__pycache__/status_codes.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/cache.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/check.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/completion.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/configuration.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/debug.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/download.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/freeze.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/hash.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/help.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/index.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/inspect.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/install.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/list.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/lock.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/search.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/show.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/uninstall.cpython-310.pyc", "Lib/site-packages/pip/_internal/commands/__pycache__/wheel.cpython-310.pyc", "Lib/site-packages/pip/_internal/__pycache__/configuration.cpython-310.pyc", "Lib/site-packages/pip/_internal/distributions/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/distributions/__pycache__/base.cpython-310.pyc", "Lib/site-packages/pip/_internal/distributions/__pycache__/installed.cpython-310.pyc", "Lib/site-packages/pip/_internal/distributions/__pycache__/sdist.cpython-310.pyc", "Lib/site-packages/pip/_internal/distributions/__pycache__/wheel.cpython-310.pyc", "Lib/site-packages/pip/_internal/__pycache__/exceptions.cpython-310.pyc", "Lib/site-packages/pip/_internal/index/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/index/__pycache__/collector.cpython-310.pyc", "Lib/site-packages/pip/_internal/index/__pycache__/package_finder.cpython-310.pyc", "Lib/site-packages/pip/_internal/index/__pycache__/sources.cpython-310.pyc", "Lib/site-packages/pip/_internal/locations/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/locations/__pycache__/_distutils.cpython-310.pyc", "Lib/site-packages/pip/_internal/locations/__pycache__/_sysconfig.cpython-310.pyc", "Lib/site-packages/pip/_internal/locations/__pycache__/base.cpython-310.pyc", "Lib/site-packages/pip/_internal/__pycache__/main.cpython-310.pyc", "Lib/site-packages/pip/_internal/metadata/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/metadata/__pycache__/_json.cpython-310.pyc", "Lib/site-packages/pip/_internal/metadata/__pycache__/base.cpython-310.pyc", "Lib/site-packages/pip/_internal/metadata/importlib/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/metadata/importlib/__pycache__/_compat.cpython-310.pyc", "Lib/site-packages/pip/_internal/metadata/importlib/__pycache__/_dists.cpython-310.pyc", "Lib/site-packages/pip/_internal/metadata/importlib/__pycache__/_envs.cpython-310.pyc", "Lib/site-packages/pip/_internal/metadata/__pycache__/pkg_resources.cpython-310.pyc", "Lib/site-packages/pip/_internal/models/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/models/__pycache__/candidate.cpython-310.pyc", "Lib/site-packages/pip/_internal/models/__pycache__/direct_url.cpython-310.pyc", "Lib/site-packages/pip/_internal/models/__pycache__/format_control.cpython-310.pyc", "Lib/site-packages/pip/_internal/models/__pycache__/index.cpython-310.pyc", "Lib/site-packages/pip/_internal/models/__pycache__/installation_report.cpython-310.pyc", "Lib/site-packages/pip/_internal/models/__pycache__/link.cpython-310.pyc", "Lib/site-packages/pip/_internal/models/__pycache__/pylock.cpython-310.pyc", "Lib/site-packages/pip/_internal/models/__pycache__/scheme.cpython-310.pyc", "Lib/site-packages/pip/_internal/models/__pycache__/search_scope.cpython-310.pyc", "Lib/site-packages/pip/_internal/models/__pycache__/selection_prefs.cpython-310.pyc", "Lib/site-packages/pip/_internal/models/__pycache__/target_python.cpython-310.pyc", "Lib/site-packages/pip/_internal/models/__pycache__/wheel.cpython-310.pyc", "Lib/site-packages/pip/_internal/network/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/network/__pycache__/auth.cpython-310.pyc", "Lib/site-packages/pip/_internal/network/__pycache__/cache.cpython-310.pyc", "Lib/site-packages/pip/_internal/network/__pycache__/download.cpython-310.pyc", "Lib/site-packages/pip/_internal/network/__pycache__/lazy_wheel.cpython-310.pyc", "Lib/site-packages/pip/_internal/network/__pycache__/session.cpython-310.pyc", "Lib/site-packages/pip/_internal/network/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/pip/_internal/network/__pycache__/xmlrpc.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/build/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/build/__pycache__/build_tracker.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/build/__pycache__/metadata.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/build/__pycache__/metadata_editable.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/build/__pycache__/metadata_legacy.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/build/__pycache__/wheel.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/build/__pycache__/wheel_editable.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/build/__pycache__/wheel_legacy.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/__pycache__/check.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/__pycache__/freeze.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/install/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/install/__pycache__/editable_legacy.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/install/__pycache__/wheel.cpython-310.pyc", "Lib/site-packages/pip/_internal/operations/__pycache__/prepare.cpython-310.pyc", "Lib/site-packages/pip/_internal/__pycache__/pyproject.cpython-310.pyc", "Lib/site-packages/pip/_internal/req/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/req/__pycache__/constructors.cpython-310.pyc", "Lib/site-packages/pip/_internal/req/__pycache__/req_dependency_group.cpython-310.pyc", "Lib/site-packages/pip/_internal/req/__pycache__/req_file.cpython-310.pyc", "Lib/site-packages/pip/_internal/req/__pycache__/req_install.cpython-310.pyc", "Lib/site-packages/pip/_internal/req/__pycache__/req_set.cpython-310.pyc", "Lib/site-packages/pip/_internal/req/__pycache__/req_uninstall.cpython-310.pyc", "Lib/site-packages/pip/_internal/resolution/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/resolution/__pycache__/base.cpython-310.pyc", "Lib/site-packages/pip/_internal/resolution/legacy/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/resolution/legacy/__pycache__/resolver.cpython-310.pyc", "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/base.cpython-310.pyc", "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/candidates.cpython-310.pyc", "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/factory.cpython-310.pyc", "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/found_candidates.cpython-310.pyc", "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/provider.cpython-310.pyc", "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/reporter.cpython-310.pyc", "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/requirements.cpython-310.pyc", "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/resolver.cpython-310.pyc", "Lib/site-packages/pip/_internal/__pycache__/self_outdated_check.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/_jaraco_text.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/_log.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/appdirs.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/compat.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/compatibility_tags.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/datetime.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/deprecation.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/direct_url_helpers.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/egg_link.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/entrypoints.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/filesystem.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/filetypes.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/glibc.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/hashes.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/logging.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/misc.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/packaging.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/retry.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/setuptools_build.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/subprocess.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/temp_dir.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/unpacking.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/urls.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/virtualenv.cpython-310.pyc", "Lib/site-packages/pip/_internal/utils/__pycache__/wheel.cpython-310.pyc", "Lib/site-packages/pip/_internal/vcs/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_internal/vcs/__pycache__/bazaar.cpython-310.pyc", "Lib/site-packages/pip/_internal/vcs/__pycache__/git.cpython-310.pyc", "Lib/site-packages/pip/_internal/vcs/__pycache__/mercurial.cpython-310.pyc", "Lib/site-packages/pip/_internal/vcs/__pycache__/subversion.cpython-310.pyc", "Lib/site-packages/pip/_internal/vcs/__pycache__/versioncontrol.cpython-310.pyc", "Lib/site-packages/pip/_internal/__pycache__/wheel_builder.cpython-310.pyc", "Lib/site-packages/pip/_vendor/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/_cmd.cpython-310.pyc", "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/adapter.cpython-310.pyc", "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/cache.cpython-310.pyc", "Lib/site-packages/pip/_vendor/cachecontrol/caches/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/cachecontrol/caches/__pycache__/file_cache.cpython-310.pyc", "Lib/site-packages/pip/_vendor/cachecontrol/caches/__pycache__/redis_cache.cpython-310.pyc", "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/controller.cpython-310.pyc", "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/filewrapper.cpython-310.pyc", "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/heuristics.cpython-310.pyc", "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/serialize.cpython-310.pyc", "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/wrapper.cpython-310.pyc", "Lib/site-packages/pip/_vendor/certifi/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/certifi/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/certifi/__pycache__/core.cpython-310.pyc", "Lib/site-packages/pip/_vendor/dependency_groups/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/dependency_groups/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/dependency_groups/__pycache__/_implementation.cpython-310.pyc", "Lib/site-packages/pip/_vendor/dependency_groups/__pycache__/_lint_dependency_groups.cpython-310.pyc", "Lib/site-packages/pip/_vendor/dependency_groups/__pycache__/_pip_wrapper.cpython-310.pyc", "Lib/site-packages/pip/_vendor/dependency_groups/__pycache__/_toml_compat.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distlib/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distlib/__pycache__/compat.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distlib/__pycache__/database.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distlib/__pycache__/index.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distlib/__pycache__/locators.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distlib/__pycache__/manifest.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distlib/__pycache__/markers.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distlib/__pycache__/metadata.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distlib/__pycache__/resources.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distlib/__pycache__/scripts.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distlib/__pycache__/util.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distlib/__pycache__/version.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distlib/__pycache__/wheel.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distro/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distro/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/distro/__pycache__/distro.cpython-310.pyc", "Lib/site-packages/pip/_vendor/idna/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/idna/__pycache__/codec.cpython-310.pyc", "Lib/site-packages/pip/_vendor/idna/__pycache__/compat.cpython-310.pyc", "Lib/site-packages/pip/_vendor/idna/__pycache__/core.cpython-310.pyc", "Lib/site-packages/pip/_vendor/idna/__pycache__/idnadata.cpython-310.pyc", "Lib/site-packages/pip/_vendor/idna/__pycache__/intranges.cpython-310.pyc", "Lib/site-packages/pip/_vendor/idna/__pycache__/package_data.cpython-310.pyc", "Lib/site-packages/pip/_vendor/idna/__pycache__/uts46data.cpython-310.pyc", "Lib/site-packages/pip/_vendor/msgpack/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/msgpack/__pycache__/exceptions.cpython-310.pyc", "Lib/site-packages/pip/_vendor/msgpack/__pycache__/ext.cpython-310.pyc", "Lib/site-packages/pip/_vendor/msgpack/__pycache__/fallback.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/_elffile.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/_manylinux.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/_musllinux.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/_parser.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/_structures.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/_tokenizer.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/licenses/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/licenses/__pycache__/_spdx.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/markers.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/metadata.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/requirements.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/specifiers.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/tags.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/pip/_vendor/packaging/__pycache__/version.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pkg_resources/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/android.cpython-310.pyc", "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/api.cpython-310.pyc", "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/macos.cpython-310.pyc", "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/unix.cpython-310.pyc", "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/version.cpython-310.pyc", "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/windows.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/console.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/filter.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/filters/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/formatter.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/formatters/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/formatters/__pycache__/_mapping.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/lexer.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/lexers/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/lexers/__pycache__/_mapping.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/lexers/__pycache__/python.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/modeline.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/plugin.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/regexopt.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/scanner.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/sphinxext.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/style.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/styles/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/styles/__pycache__/_mapping.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/token.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/unistring.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pygments/__pycache__/util.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pyproject_hooks/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pyproject_hooks/__pycache__/_impl.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pyproject_hooks/_in_process/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/pyproject_hooks/_in_process/__pycache__/_in_process.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/__version__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/_internal_utils.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/adapters.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/api.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/auth.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/certs.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/compat.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/cookies.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/exceptions.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/help.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/hooks.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/models.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/packages.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/sessions.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/status_codes.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/structures.cpython-310.pyc", "Lib/site-packages/pip/_vendor/requests/__pycache__/utils.cpython-310.pyc", "Lib/site-packages/pip/_vendor/resolvelib/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/resolvelib/__pycache__/providers.cpython-310.pyc", "Lib/site-packages/pip/_vendor/resolvelib/__pycache__/reporters.cpython-310.pyc", "Lib/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/abstract.cpython-310.pyc", "Lib/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/criterion.cpython-310.pyc", "Lib/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/exceptions.cpython-310.pyc", "Lib/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/resolution.cpython-310.pyc", "Lib/site-packages/pip/_vendor/resolvelib/__pycache__/structs.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_cell_widths.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_emoji_codes.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_emoji_replace.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_export_format.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_extension.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_fileno.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_inspect.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_log_render.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_loop.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_null_file.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_palettes.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_pick.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_ratio.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_spinners.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_stack.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_timer.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_win32_console.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_windows.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_windows_renderer.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/_wrap.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/abc.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/align.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/ansi.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/bar.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/box.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/cells.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/color.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/color_triplet.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/columns.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/console.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/constrain.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/containers.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/control.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/default_styles.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/diagnose.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/emoji.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/errors.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/file_proxy.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/filesize.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/highlighter.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/json.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/jupyter.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/layout.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/live.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/live_render.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/logging.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/markup.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/measure.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/padding.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/pager.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/palette.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/panel.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/pretty.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/progress.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/progress_bar.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/prompt.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/protocol.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/region.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/repr.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/rule.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/scope.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/screen.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/segment.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/spinner.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/status.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/style.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/styled.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/syntax.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/table.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/terminal_theme.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/text.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/theme.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/themes.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/traceback.cpython-310.pyc", "Lib/site-packages/pip/_vendor/rich/__pycache__/tree.cpython-310.pyc", "Lib/site-packages/pip/_vendor/tomli/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/tomli/__pycache__/_parser.cpython-310.pyc", "Lib/site-packages/pip/_vendor/tomli/__pycache__/_re.cpython-310.pyc", "Lib/site-packages/pip/_vendor/tomli/__pycache__/_types.cpython-310.pyc", "Lib/site-packages/pip/_vendor/tomli_w/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/tomli_w/__pycache__/_writer.cpython-310.pyc", "Lib/site-packages/pip/_vendor/truststore/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/truststore/__pycache__/_api.cpython-310.pyc", "Lib/site-packages/pip/_vendor/truststore/__pycache__/_macos.cpython-310.pyc", "Lib/site-packages/pip/_vendor/truststore/__pycache__/_openssl.cpython-310.pyc", "Lib/site-packages/pip/_vendor/truststore/__pycache__/_ssl_constants.cpython-310.pyc", "Lib/site-packages/pip/_vendor/truststore/__pycache__/_windows.cpython-310.pyc", "Lib/site-packages/pip/_vendor/__pycache__/typing_extensions.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/__pycache__/_collections.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/__pycache__/_version.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/__pycache__/connection.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/__pycache__/connectionpool.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/_appengine_environ.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__pycache__/bindings.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__pycache__/low_level.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/appengine.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/ntlmpool.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/pyopenssl.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/securetransport.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/socks.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/__pycache__/exceptions.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/__pycache__/fields.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/__pycache__/filepost.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/packages/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/makefile.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/weakref_finalize.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/packages/__pycache__/six.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/__pycache__/poolmanager.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/__pycache__/request.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/__pycache__/response.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/connection.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/proxy.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/queue.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/request.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/response.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/retry.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/ssl_.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/ssl_match_hostname.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/ssltransport.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/timeout.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/url.cpython-310.pyc", "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/wait.cpython-310.pyc", "Scripts/pip-script.py", "Scripts/pip3-script.py", "Scripts/pip.exe", "Scripts/pip3.exe"], "fn": "pip-25.1-pyhc872135_2.conda", "legacy_bz2_md5": "6d57138fda2463f117532525e85b7aba", "license": "MIT", "license_family": "MIT", "link": {"source": "C:\\Users\\<USER>\\anaconda3\\pkgs\\pip-25.1-pyhc872135_2", "type": 1}, "md5": "2778327d2a700153fefe0e69438b18e1", "name": "pip", "noarch": "python", "package_tarball_full_path": "C:\\Users\\<USER>\\anaconda3\\pkgs\\pip-25.1-pyhc872135_2.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/pip-25.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/pip-25.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "1ccd0bc8398ed106dfb3c58184aa609158cb0d922901ea8b9620141d6dbb8fc2", "sha256_in_prefix": "1ccd0bc8398ed106dfb3c58184aa609158cb0d922901ea8b9620141d6dbb8fc2", "size_in_bytes": 3647}, {"_path": "site-packages/pip-25.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "ade7d41f95ad89d47162a56fd8668de9e6b068322301d6b944cf636fbc09cd8a", "sha256_in_prefix": "ade7d41f95ad89d47162a56fd8668de9e6b068322301d6b944cf636fbc09cd8a", "size_in_bytes": 66021}, {"_path": "site-packages/pip-25.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip-25.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "95353a07a7887d8a224094d935cf9fc9a47a0692fa7a14f3537c466319f69fc9", "sha256_in_prefix": "95353a07a7887d8a224094d935cf9fc9a47a0692fa7a14f3537c466319f69fc9", "size_in_bytes": 91}, {"_path": "site-packages/pip-25.1.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "0ce697992e61b64f5a5a804b52920b5464e4da654844b3adae509d9515359a86", "sha256_in_prefix": "0ce697992e61b64f5a5a804b52920b5464e4da654844b3adae509d9515359a86", "size_in_bytes": 63}, {"_path": "site-packages/pip-25.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "79e223bb37e77d1d8fae16e39dbcc553a327492ef49192f1c1a1c7aba33e6c3d", "sha256_in_prefix": "79e223bb37e77d1d8fae16e39dbcc553a327492ef49192f1c1a1c7aba33e6c3d", "size_in_bytes": 87}, {"_path": "site-packages/pip-25.1.dist-info/licenses/AUTHORS.txt", "path_type": "hardlink", "sha256": "6330c54d8a5e627c2998e0c315383229934a59c7dd3b5d0be44c7f53a9092d17", "sha256_in_prefix": "6330c54d8a5e627c2998e0c315383229934a59c7dd3b5d0be44c7f53a9092d17", "size_in_bytes": 11223}, {"_path": "site-packages/pip-25.1.dist-info/licenses/LICENSE.txt", "path_type": "hardlink", "sha256": "634300a669d49aeae65b12c6c48c924c51a4cdf3d1ff086dc3456dc8bcaa2104", "sha256_in_prefix": "634300a669d49aeae65b12c6c48c924c51a4cdf3d1ff086dc3456dc8bcaa2104", "size_in_bytes": 1093}, {"_path": "site-packages/pip-25.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "ceebae7b8927a3227e5303cf5e0f1f7b34bb542ad7250ac03fbcde36ec2f1508", "sha256_in_prefix": "ceebae7b8927a3227e5303cf5e0f1f7b34bb542ad7250ac03fbcde36ec2f1508", "size_in_bytes": 4}, {"_path": "site-packages/pip/__init__.py", "path_type": "hardlink", "sha256": "ab57e104db96775c0b87005823acf3c9c886a95953574eb250a0a5be35671f19", "sha256_in_prefix": "ab57e104db96775c0b87005823acf3c9c886a95953574eb250a0a5be35671f19", "size_in_bytes": 355}, {"_path": "site-packages/pip/__main__.py", "path_type": "hardlink", "sha256": "5b36e11d74db484ea0058d7d98d37d9b8b39a3fdfae4b3af4d84a0aa06dd0611", "sha256_in_prefix": "5b36e11d74db484ea0058d7d98d37d9b8b39a3fdfae4b3af4d84a0aa06dd0611", "size_in_bytes": 854}, {"_path": "site-packages/pip/__pip-runner__.py", "path_type": "hardlink", "sha256": "24ea04653c2bb6fee345a5c1920490280134e323727c59861f1aa91e2187bcbd", "sha256_in_prefix": "24ea04653c2bb6fee345a5c1920490280134e323727c59861f1aa91e2187bcbd", "size_in_bytes": 1450}, {"_path": "site-packages/pip/_internal/__init__.py", "path_type": "hardlink", "sha256": "31f7283a5b8367c40c08561a974e08a8e27daba9b657b6b468eb2723e58ec54a", "sha256_in_prefix": "31f7283a5b8367c40c08561a974e08a8e27daba9b657b6b468eb2723e58ec54a", "size_in_bytes": 513}, {"_path": "site-packages/pip/_internal/build_env.py", "path_type": "hardlink", "sha256": "eb4fdeb292c8f57dc2d9d6f75b0da021cca3364d9c00f35c73982c54ee033aa8", "sha256_in_prefix": "eb4fdeb292c8f57dc2d9d6f75b0da021cca3364d9c00f35c73982c54ee033aa8", "size_in_bytes": 10924}, {"_path": "site-packages/pip/_internal/cache.py", "path_type": "hardlink", "sha256": "4a38492b50ba35ba27ad4e00c985ca4ce1f40863a4e5c26b62deb499100d9cf3", "sha256_in_prefix": "4a38492b50ba35ba27ad4e00c985ca4ce1f40863a4e5c26b62deb499100d9cf3", "size_in_bytes": 10368}, {"_path": "site-packages/pip/_internal/cli/__init__.py", "path_type": "hardlink", "sha256": "22a83fb4a03bef55ee30ed4fe2dfec0c79d228fce451bf43d03aae9c09b0fe4a", "sha256_in_prefix": "22a83fb4a03bef55ee30ed4fe2dfec0c79d228fce451bf43d03aae9c09b0fe4a", "size_in_bytes": 131}, {"_path": "site-packages/pip/_internal/cli/autocompletion.py", "path_type": "hardlink", "sha256": "7ecd16cb5e866b9b57d4864abf0c390438bb8b9cf2cdf08fbeeedc8179605f2b", "sha256_in_prefix": "7ecd16cb5e866b9b57d4864abf0c390438bb8b9cf2cdf08fbeeedc8179605f2b", "size_in_bytes": 6864}, {"_path": "site-packages/pip/_internal/cli/base_command.py", "path_type": "hardlink", "sha256": "d00f18b895498766325d4f29756d1e89d2e0d5e925096e5c534d66a48f850310", "sha256_in_prefix": "d00f18b895498766325d4f29756d1e89d2e0d5e925096e5c534d66a48f850310", "size_in_bytes": 8351}, {"_path": "site-packages/pip/_internal/cli/cmdoptions.py", "path_type": "hardlink", "sha256": "fbf57882331ad1cdd4f3ebd7280c9be71555889373140c41237671ffa0ece7e8", "sha256_in_prefix": "fbf57882331ad1cdd4f3ebd7280c9be71555889373140c41237671ffa0ece7e8", "size_in_bytes": 31909}, {"_path": "site-packages/pip/_internal/cli/command_context.py", "path_type": "hardlink", "sha256": "4478083f0b4e6e1e4a84cadddd8653925f336d51bee8e92697b61b157e04860d", "sha256_in_prefix": "4478083f0b4e6e1e4a84cadddd8653925f336d51bee8e92697b61b157e04860d", "size_in_bytes": 774}, {"_path": "site-packages/pip/_internal/cli/index_command.py", "path_type": "hardlink", "sha256": "929964bac520099cbbe63342a3e7ad6839921bc52fa9c4765b9d002c3766e9d9", "sha256_in_prefix": "929964bac520099cbbe63342a3e7ad6839921bc52fa9c4765b9d002c3766e9d9", "size_in_bytes": 5720}, {"_path": "site-packages/pip/_internal/cli/main.py", "path_type": "hardlink", "sha256": "d5b5c2eee2f7b5d6ff11994654ab3afd38330bdb65294ef384066c6d903e2336", "sha256_in_prefix": "d5b5c2eee2f7b5d6ff11994654ab3afd38330bdb65294ef384066c6d903e2336", "size_in_bytes": 2816}, {"_path": "site-packages/pip/_internal/cli/main_parser.py", "path_type": "hardlink", "sha256": "72166a3660ae3bf258b7cca7042ba687872b511691a17059d11c68498408c02c", "sha256_in_prefix": "72166a3660ae3bf258b7cca7042ba687872b511691a17059d11c68498408c02c", "size_in_bytes": 4337}, {"_path": "site-packages/pip/_internal/cli/parser.py", "path_type": "hardlink", "sha256": "54232d76ecc409457ceca68736efb127ec0b34bf36c93df1d7a5785c1c4e02a2", "sha256_in_prefix": "54232d76ecc409457ceca68736efb127ec0b34bf36c93df1d7a5785c1c4e02a2", "size_in_bytes": 10825}, {"_path": "site-packages/pip/_internal/cli/progress_bars.py", "path_type": "hardlink", "sha256": "afd043e13dbe7a07089c1d5487d263c3710ff45dc8372e646611b049e3e6f63a", "sha256_in_prefix": "afd043e13dbe7a07089c1d5487d263c3710ff45dc8372e646611b049e3e6f63a", "size_in_bytes": 4435}, {"_path": "site-packages/pip/_internal/cli/req_command.py", "path_type": "hardlink", "sha256": "d727ecb01be750a35eafc0fb893dce1ea74924d742a918700ea5055a089ea699", "sha256_in_prefix": "d727ecb01be750a35eafc0fb893dce1ea74924d742a918700ea5055a089ea699", "size_in_bytes": 12934}, {"_path": "site-packages/pip/_internal/cli/spinners.py", "path_type": "hardlink", "sha256": "84827cdc67ab74580509da1b200db726081eb5e825fee0b84a9e7cea7cc56cf1", "sha256_in_prefix": "84827cdc67ab74580509da1b200db726081eb5e825fee0b84a9e7cea7cc56cf1", "size_in_bytes": 5118}, {"_path": "site-packages/pip/_internal/cli/status_codes.py", "path_type": "hardlink", "sha256": "b0414751a5096eabfc880acbdc702d733b5666618e157d358537ac4b2b43121d", "sha256_in_prefix": "b0414751a5096eabfc880acbdc702d733b5666618e157d358537ac4b2b43121d", "size_in_bytes": 116}, {"_path": "site-packages/pip/_internal/commands/__init__.py", "path_type": "hardlink", "sha256": "df8d392b216fe25d2bbb1785ebda28b056a7c4d41c0bf7c7046bfb469b743d78", "sha256_in_prefix": "df8d392b216fe25d2bbb1785ebda28b056a7c4d41c0bf7c7046bfb469b743d78", "size_in_bytes": 4009}, {"_path": "site-packages/pip/_internal/commands/cache.py", "path_type": "hardlink", "sha256": "20e7b34e27078c6139b16741c769f03d581b8ee247337b3e059124a592de9ae6", "sha256_in_prefix": "20e7b34e27078c6139b16741c769f03d581b8ee247337b3e059124a592de9ae6", "size_in_bytes": 8107}, {"_path": "site-packages/pip/_internal/commands/check.py", "path_type": "hardlink", "sha256": "1ebff87a231df5c8150e012f8ed21dc3dd793662fb44e2165bc7a792bf2c94f4", "sha256_in_prefix": "1ebff87a231df5c8150e012f8ed21dc3dd793662fb44e2165bc7a792bf2c94f4", "size_in_bytes": 2268}, {"_path": "site-packages/pip/_internal/commands/completion.py", "path_type": "hardlink", "sha256": "5bd4054133cb8f2dad3c0089ff2de0f44801d696ec87ba6f0945fca1cb8874f8", "sha256_in_prefix": "5bd4054133cb8f2dad3c0089ff2de0f44801d696ec87ba6f0945fca1cb8874f8", "size_in_bytes": 4554}, {"_path": "site-packages/pip/_internal/commands/configuration.py", "path_type": "hardlink", "sha256": "9fdf1e9f0a7acb46f91ba7e24508da668e3716524a62f7bf75a32137ee0144d7", "sha256_in_prefix": "9fdf1e9f0a7acb46f91ba7e24508da668e3716524a62f7bf75a32137ee0144d7", "size_in_bytes": 9766}, {"_path": "site-packages/pip/_internal/commands/debug.py", "path_type": "hardlink", "sha256": "0cd0d1804f58b0aadb633534b3754a8bcac7b4a1785f5dc227f6ebffc3d45ced", "sha256_in_prefix": "0cd0d1804f58b0aadb633534b3754a8bcac7b4a1785f5dc227f6ebffc3d45ced", "size_in_bytes": 6797}, {"_path": "site-packages/pip/_internal/commands/download.py", "path_type": "hardlink", "sha256": "d2a0749f2b3a6443eca20e39d650ec8cbe41c7b67deedf81f34a0564a869cca3", "sha256_in_prefix": "d2a0749f2b3a6443eca20e39d650ec8cbe41c7b67deedf81f34a0564a869cca3", "size_in_bytes": 5273}, {"_path": "site-packages/pip/_internal/commands/freeze.py", "path_type": "hardlink", "sha256": "616f9a326033cce681596a1ba3d8380cf2ae5a9d1d4c2df695632a5f32852f31", "sha256_in_prefix": "616f9a326033cce681596a1ba3d8380cf2ae5a9d1d4c2df695632a5f32852f31", "size_in_bytes": 3144}, {"_path": "site-packages/pip/_internal/commands/hash.py", "path_type": "hardlink", "sha256": "11554ebaf1ada0f11d162f1236799daa5090ae10b157e909b1dc2d75c0a75c64", "sha256_in_prefix": "11554ebaf1ada0f11d162f1236799daa5090ae10b157e909b1dc2d75c0a75c64", "size_in_bytes": 1703}, {"_path": "site-packages/pip/_internal/commands/help.py", "path_type": "hardlink", "sha256": "81c73a40391c80730eb809f9531699c004adb1106b9c64a7ff2c634b9ec92283", "sha256_in_prefix": "81c73a40391c80730eb809f9531699c004adb1106b9c64a7ff2c634b9ec92283", "size_in_bytes": 1132}, {"_path": "site-packages/pip/_internal/commands/index.py", "path_type": "hardlink", "sha256": "f14b9c155c31e8598cf1c35b68f63c888e6465d5777fa8e1a836be4bc6868298", "sha256_in_prefix": "f14b9c155c31e8598cf1c35b68f63c888e6465d5777fa8e1a836be4bc6868298", "size_in_bytes": 5068}, {"_path": "site-packages/pip/_internal/commands/inspect.py", "path_type": "hardlink", "sha256": "3c6ad8f53453442337cb9325f01764f0310e5eab9645fb1caf80d1a352ce4cf7", "sha256_in_prefix": "3c6ad8f53453442337cb9325f01764f0310e5eab9645fb1caf80d1a352ce4cf7", "size_in_bytes": 3189}, {"_path": "site-packages/pip/_internal/commands/install.py", "path_type": "hardlink", "sha256": "cfcd3727af6aa6d53195fca3f3e848944d5f30464ef209133331383280f5240b", "sha256_in_prefix": "cfcd3727af6aa6d53195fca3f3e848944d5f30464ef209133331383280f5240b", "size_in_bytes": 29755}, {"_path": "site-packages/pip/_internal/commands/list.py", "path_type": "hardlink", "sha256": "470b5ff01d1dd3e5ab90ced0b2fe35f9d5a0f08febf412ee655df4c125a7ce05", "sha256_in_prefix": "470b5ff01d1dd3e5ab90ced0b2fe35f9d5a0f08febf412ee655df4c125a7ce05", "size_in_bytes": 13274}, {"_path": "site-packages/pip/_internal/commands/lock.py", "path_type": "hardlink", "sha256": "6d462baf229aefaf545cceb58a6a23a1e55581cff56472bef5ad212099a69428", "sha256_in_prefix": "6d462baf229aefaf545cceb58a6a23a1e55581cff56472bef5ad212099a69428", "size_in_bytes": 5941}, {"_path": "site-packages/pip/_internal/commands/search.py", "path_type": "hardlink", "sha256": "22b7efc5c4424a8658f40e57025085d70b65ff2d873dc5ec950747723cf030d9", "sha256_in_prefix": "22b7efc5c4424a8658f40e57025085d70b65ff2d873dc5ec950747723cf030d9", "size_in_bytes": 5784}, {"_path": "site-packages/pip/_internal/commands/show.py", "path_type": "hardlink", "sha256": "621e6b198851d88a394e42f47c508c584d55aaa338c613c78e16dd4b742011a7", "sha256_in_prefix": "621e6b198851d88a394e42f47c508c584d55aaa338c613c78e16dd4b742011a7", "size_in_bytes": 8028}, {"_path": "site-packages/pip/_internal/commands/uninstall.py", "path_type": "hardlink", "sha256": "ee9391ede9caefa8229b2c506f3c5c1b53acc8b5cbdc3bd7f77f7198cf05bed8", "sha256_in_prefix": "ee9391ede9caefa8229b2c506f3c5c1b53acc8b5cbdc3bd7f77f7198cf05bed8", "size_in_bytes": 3892}, {"_path": "site-packages/pip/_internal/commands/wheel.py", "path_type": "hardlink", "sha256": "3447da545e1fe355413529fddd12fc8247c210399d1a16cff71bbf744e9c7549", "sha256_in_prefix": "3447da545e1fe355413529fddd12fc8247c210399d1a16cff71bbf744e9c7549", "size_in_bytes": 6346}, {"_path": "site-packages/pip/_internal/configuration.py", "path_type": "hardlink", "sha256": "f8a3a893a8e1de11735cc3d014f275fc416306902c81ef914ab790b8b1cb9b3a", "sha256_in_prefix": "f8a3a893a8e1de11735cc3d014f275fc416306902c81ef914ab790b8b1cb9b3a", "size_in_bytes": 14005}, {"_path": "site-packages/pip/_internal/distributions/__init__.py", "path_type": "hardlink", "sha256": "1eaea4b7a8170608cd8ade614d358b03378234e2a807e374a46612a9e86b962f", "sha256_in_prefix": "1eaea4b7a8170608cd8ade614d358b03378234e2a807e374a46612a9e86b962f", "size_in_bytes": 858}, {"_path": "site-packages/pip/_internal/distributions/base.py", "path_type": "hardlink", "sha256": "41e07daaf2970c88cb74f0431397cc8297c6a8c302afe828be7ba84271ae885f", "sha256_in_prefix": "41e07daaf2970c88cb74f0431397cc8297c6a8c302afe828be7ba84271ae885f", "size_in_bytes": 1783}, {"_path": "site-packages/pip/_internal/distributions/installed.py", "path_type": "hardlink", "sha256": "4229c715b58043ca04d296c3f0c1595a4c259df5354184dc700d6f9e1ae560e5", "sha256_in_prefix": "4229c715b58043ca04d296c3f0c1595a4c259df5354184dc700d6f9e1ae560e5", "size_in_bytes": 842}, {"_path": "site-packages/pip/_internal/distributions/sdist.py", "path_type": "hardlink", "sha256": "3e570fe1aebe47a73df179ce33e6fa2e46f7aecbe1f621b8a24f2c85a6a7af3b", "sha256_in_prefix": "3e570fe1aebe47a73df179ce33e6fa2e46f7aecbe1f621b8a24f2c85a6a7af3b", "size_in_bytes": 6751}, {"_path": "site-packages/pip/_internal/distributions/wheel.py", "path_type": "hardlink", "sha256": "4c70587e7bfb555b7c99884c614b47d774b513b143c2d0f20df994725f1a8b41", "sha256_in_prefix": "4c70587e7bfb555b7c99884c614b47d774b513b143c2d0f20df994725f1a8b41", "size_in_bytes": 1317}, {"_path": "site-packages/pip/_internal/exceptions.py", "path_type": "hardlink", "sha256": "c29135d47d1ee0bf46e801fbd2c446d78f73f365fbc17e77d072bef5e03f0c84", "sha256_in_prefix": "c29135d47d1ee0bf46e801fbd2c446d78f73f365fbc17e77d072bef5e03f0c84", "size_in_bytes": 28464}, {"_path": "site-packages/pip/_internal/index/__init__.py", "path_type": "hardlink", "sha256": "b73c0c1ff7e141eb9bc0ca877528af6ac83571181349b360d8288c567ccc9b25", "sha256_in_prefix": "b73c0c1ff7e141eb9bc0ca877528af6ac83571181349b360d8288c567ccc9b25", "size_in_bytes": 29}, {"_path": "site-packages/pip/_internal/index/collector.py", "path_type": "hardlink", "sha256": "45d3ced092c0966c8158f0166073f24681a3cf718d01e4e78023646c67b2fe61", "sha256_in_prefix": "45d3ced092c0966c8158f0166073f24681a3cf718d01e4e78023646c67b2fe61", "size_in_bytes": 16265}, {"_path": "site-packages/pip/_internal/index/package_finder.py", "path_type": "hardlink", "sha256": "468851cf32c4c685e5ed00dd4e2ab2c4869011c1d49fa50d3abf4acc2d6f8cbd", "sha256_in_prefix": "468851cf32c4c685e5ed00dd4e2ab2c4869011c1d49fa50d3abf4acc2d6f8cbd", "size_in_bytes": 38446}, {"_path": "site-packages/pip/_internal/index/sources.py", "path_type": "hardlink", "sha256": "94f04b2b95e2cbc43a210322a36e9697ba1c7d938a9201a494804dc94276ddf2", "sha256_in_prefix": "94f04b2b95e2cbc43a210322a36e9697ba1c7d938a9201a494804dc94276ddf2", "size_in_bytes": 8632}, {"_path": "site-packages/pip/_internal/locations/__init__.py", "path_type": "hardlink", "sha256": "bef4cc3718214f46845eb49daa936db910c6c74f1bcc9c5f0c05147d0d156f40", "sha256_in_prefix": "bef4cc3718214f46845eb49daa936db910c6c74f1bcc9c5f0c05147d0d156f40", "size_in_bytes": 14309}, {"_path": "site-packages/pip/_internal/locations/_distutils.py", "path_type": "hardlink", "sha256": "c7a9f254b8fb5f5d58e2484875ffa6165c4c97615669db5512079bf2ea5cfd62", "sha256_in_prefix": "c7a9f254b8fb5f5d58e2484875ffa6165c4c97615669db5512079bf2ea5cfd62", "size_in_bytes": 6013}, {"_path": "site-packages/pip/_internal/locations/_sysconfig.py", "path_type": "hardlink", "sha256": "206cddb3ad2ab059de468802fa8781698edb121de53edfefe3b90c2428505ec5", "sha256_in_prefix": "206cddb3ad2ab059de468802fa8781698edb121de53edfefe3b90c2428505ec5", "size_in_bytes": 7724}, {"_path": "site-packages/pip/_internal/locations/base.py", "path_type": "hardlink", "sha256": "45088f8b5778155336071934e1d4215d9d8faa47a58c42f67d967d498a8843bf", "sha256_in_prefix": "45088f8b5778155336071934e1d4215d9d8faa47a58c42f67d967d498a8843bf", "size_in_bytes": 2556}, {"_path": "site-packages/pip/_internal/main.py", "path_type": "hardlink", "sha256": "afe52751ef072e8e57149cfc8a74dc38e4e2bbfb313618076fa57094652594e2", "sha256_in_prefix": "afe52751ef072e8e57149cfc8a74dc38e4e2bbfb313618076fa57094652594e2", "size_in_bytes": 340}, {"_path": "site-packages/pip/_internal/metadata/__init__.py", "path_type": "hardlink", "sha256": "9c65ae66f8d09481ee765333ffe6ec52cd8b0c0d9928d3c67af66810671deb86", "sha256_in_prefix": "9c65ae66f8d09481ee765333ffe6ec52cd8b0c0d9928d3c67af66810671deb86", "size_in_bytes": 5723}, {"_path": "site-packages/pip/_internal/metadata/_json.py", "path_type": "hardlink", "sha256": "7b3ac861acc708834cd90524d5e03dc0400c8f769e19678356019a9605332a97", "sha256_in_prefix": "7b3ac861acc708834cd90524d5e03dc0400c8f769e19678356019a9605332a97", "size_in_bytes": 2707}, {"_path": "site-packages/pip/_internal/metadata/base.py", "path_type": "hardlink", "sha256": "8c26f374873c3205a73d1e2b7ebbd2421495cd2a0ec8a397863df17bc0681bc7", "sha256_in_prefix": "8c26f374873c3205a73d1e2b7ebbd2421495cd2a0ec8a397863df17bc0681bc7", "size_in_bytes": 25467}, {"_path": "site-packages/pip/_internal/metadata/importlib/__init__.py", "path_type": "hardlink", "sha256": "8d4522768c671dc7c84c71da0161b51b68b97dd058925bffb89723a36c7b5581", "sha256_in_prefix": "8d4522768c671dc7c84c71da0161b51b68b97dd058925bffb89723a36c7b5581", "size_in_bytes": 135}, {"_path": "site-packages/pip/_internal/metadata/importlib/_compat.py", "path_type": "hardlink", "sha256": "73a6aff2c3fc0418c066e152268c358967f28145cd337c514c29f99eac3a07d3", "sha256_in_prefix": "73a6aff2c3fc0418c066e152268c358967f28145cd337c514c29f99eac3a07d3", "size_in_bytes": 2796}, {"_path": "site-packages/pip/_internal/metadata/importlib/_dists.py", "path_type": "hardlink", "sha256": "7ed9988b27d41942239d5c2de96f888ec8a6cb9736f0a8265e5cb9439210d8fe", "sha256_in_prefix": "7ed9988b27d41942239d5c2de96f888ec8a6cb9736f0a8265e5cb9439210d8fe", "size_in_bytes": 8279}, {"_path": "site-packages/pip/_internal/metadata/importlib/_envs.py", "path_type": "hardlink", "sha256": "5fadc291d00f24260f85e7d848b890ccf7fd8a33179b99cbfc0fd9ebcca9dbec", "sha256_in_prefix": "5fadc291d00f24260f85e7d848b890ccf7fd8a33179b99cbfc0fd9ebcca9dbec", "size_in_bytes": 5297}, {"_path": "site-packages/pip/_internal/metadata/pkg_resources.py", "path_type": "hardlink", "sha256": "534ec44c020d4867924417d6506f77138b5965b696fdfecf1b312a64dd21ba57", "sha256_in_prefix": "534ec44c020d4867924417d6506f77138b5965b696fdfecf1b312a64dd21ba57", "size_in_bytes": 10542}, {"_path": "site-packages/pip/_internal/models/__init__.py", "path_type": "hardlink", "sha256": "023982101c57fcc1fd7ff8d58c818d08524a60261e4847b5f32caf371e2e44a4", "sha256_in_prefix": "023982101c57fcc1fd7ff8d58c818d08524a60261e4847b5f32caf371e2e44a4", "size_in_bytes": 62}, {"_path": "site-packages/pip/_internal/models/candidate.py", "path_type": "hardlink", "sha256": "cf380546ec3f9163e32a91b0ecb0b4654303d8243611b7ab50862cf22ce37420", "sha256_in_prefix": "cf380546ec3f9163e32a91b0ecb0b4654303d8243611b7ab50862cf22ce37420", "size_in_bytes": 753}, {"_path": "site-packages/pip/_internal/models/direct_url.py", "path_type": "hardlink", "sha256": "949d5f2154e0939506e52cd3351d05a604860108c28651fede8b60884240848b", "sha256_in_prefix": "949d5f2154e0939506e52cd3351d05a604860108c28651fede8b60884240848b", "size_in_bytes": 6576}, {"_path": "site-packages/pip/_internal/models/format_control.py", "path_type": "hardlink", "sha256": "c2db10a922bd1da522371404b81f82eb67958a6c3a1b8fd5405c55f7efca0c11", "sha256_in_prefix": "c2db10a922bd1da522371404b81f82eb67958a6c3a1b8fd5405c55f7efca0c11", "size_in_bytes": 2486}, {"_path": "site-packages/pip/_internal/models/index.py", "path_type": "hardlink", "sha256": "b589cbf28c468b8692356babd261bc0c03fbac2eb2ba16bf33024ef31c3472b2", "sha256_in_prefix": "b589cbf28c468b8692356babd261bc0c03fbac2eb2ba16bf33024ef31c3472b2", "size_in_bytes": 1030}, {"_path": "site-packages/pip/_internal/models/installation_report.py", "path_type": "hardlink", "sha256": "cd1559a1acfedafb2b7b38ff1f784b3a131908af5ced36f35a00be8ce6a50f4d", "sha256_in_prefix": "cd1559a1acfedafb2b7b38ff1f784b3a131908af5ced36f35a00be8ce6a50f4d", "size_in_bytes": 2818}, {"_path": "site-packages/pip/_internal/models/link.py", "path_type": "hardlink", "sha256": "c08020c618aed39c9c2e16ed0226e49d75f92fa5fd8eefcf3cb56731cbe1f41e", "sha256_in_prefix": "c08020c618aed39c9c2e16ed0226e49d75f92fa5fd8eefcf3cb56731cbe1f41e", "size_in_bytes": 21511}, {"_path": "site-packages/pip/_internal/models/pylock.py", "path_type": "hardlink", "sha256": "9f7f88dba6dfdaff8a9faa9cc7801307f65e97648b85a5316419ac31e1a0600a", "sha256_in_prefix": "9f7f88dba6dfdaff8a9faa9cc7801307f65e97648b85a5316419ac31e1a0600a", "size_in_bytes": 6196}, {"_path": "site-packages/pip/_internal/models/scheme.py", "path_type": "hardlink", "sha256": "3da9261c93377bc38e592645b5fcf5033edfd6678e3499e41ae431165b77c011", "sha256_in_prefix": "3da9261c93377bc38e592645b5fcf5033edfd6678e3499e41ae431165b77c011", "size_in_bytes": 575}, {"_path": "site-packages/pip/_internal/models/search_scope.py", "path_type": "hardlink", "sha256": "ebb3449ec618f38efce12f8c33b7a442ea3d2972c7fbb333167b578daa6f028d", "sha256_in_prefix": "ebb3449ec618f38efce12f8c33b7a442ea3d2972c7fbb333167b578daa6f028d", "size_in_bytes": 4531}, {"_path": "site-packages/pip/_internal/models/selection_prefs.py", "path_type": "hardlink", "sha256": "a9a15f0ecddc8aaa173e0eb1c78e4dd633cba9c70b270e0dd2ce0fd0fc874d0f", "sha256_in_prefix": "a9a15f0ecddc8aaa173e0eb1c78e4dd633cba9c70b270e0dd2ce0fd0fc874d0f", "size_in_bytes": 2015}, {"_path": "site-packages/pip/_internal/models/target_python.py", "path_type": "hardlink", "sha256": "d97687dab679645f8ae707096c4306125ed2aab4d3a030cd92bb50daffefffe4", "sha256_in_prefix": "d97687dab679645f8ae707096c4306125ed2aab4d3a030cd92bb50daffefffe4", "size_in_bytes": 4271}, {"_path": "site-packages/pip/_internal/models/wheel.py", "path_type": "hardlink", "sha256": "d743544d72118dfd8ff287b55b3a25bfca14bfbf98babace76e54550868385d3", "sha256_in_prefix": "d743544d72118dfd8ff287b55b3a25bfca14bfbf98babace76e54550868385d3", "size_in_bytes": 5506}, {"_path": "site-packages/pip/_internal/network/__init__.py", "path_type": "hardlink", "sha256": "14ccb4e8ffffcba8cc8d473ccf765c41d285fa9999db3335e3fbc17873c68542", "sha256_in_prefix": "14ccb4e8ffffcba8cc8d473ccf765c41d285fa9999db3335e3fbc17873c68542", "size_in_bytes": 49}, {"_path": "site-packages/pip/_internal/network/auth.py", "path_type": "hardlink", "sha256": "0f88004a352baa80c5952b7a810efaeca0008efe8f532254d29b839615cd5511", "sha256_in_prefix": "0f88004a352baa80c5952b7a810efaeca0008efe8f532254d29b839615cd5511", "size_in_bytes": 20809}, {"_path": "site-packages/pip/_internal/network/cache.py", "path_type": "hardlink", "sha256": "246613f8151a48c744070208fcad5413aab2048b73ee1cc69322e5fc94739016", "sha256_in_prefix": "246613f8151a48c744070208fcad5413aab2048b73ee1cc69322e5fc94739016", "size_in_bytes": 4613}, {"_path": "site-packages/pip/_internal/network/download.py", "path_type": "hardlink", "sha256": "e88759ca8111588b175c55052ff87f7bfc62f19d06d1496992874fcbca8abf65", "sha256_in_prefix": "e88759ca8111588b175c55052ff87f7bfc62f19d06d1496992874fcbca8abf65", "size_in_bytes": 11078}, {"_path": "site-packages/pip/_internal/network/lazy_wheel.py", "path_type": "hardlink", "sha256": "3c176832835040803ce058609dedfc8d6179d96e31fcab6c1e3c60bf876444a0", "sha256_in_prefix": "3c176832835040803ce058609dedfc8d6179d96e31fcab6c1e3c60bf876444a0", "size_in_bytes": 7622}, {"_path": "site-packages/pip/_internal/network/session.py", "path_type": "hardlink", "sha256": "9ac3387acd7a2e698d118364ad8ca0f1f4dcee00076ca165b5ac1f28fdbb2ce2", "sha256_in_prefix": "9ac3387acd7a2e698d118364ad8ca0f1f4dcee00076ca165b5ac1f28fdbb2ce2", "size_in_bytes": 18771}, {"_path": "site-packages/pip/_internal/network/utils.py", "path_type": "hardlink", "sha256": "2276b17a5f8dc41bb83d05a48f212b7677dec2c1427201e987b773475f856e86", "sha256_in_prefix": "2276b17a5f8dc41bb83d05a48f212b7677dec2c1427201e987b773475f856e86", "size_in_bytes": 4088}, {"_path": "site-packages/pip/_internal/network/xmlrpc.py", "path_type": "hardlink", "sha256": "8d6f680d259a98c95dde264e3bd45ba27542f194ad907ca9a42b33a1ebe4b898", "sha256_in_prefix": "8d6f680d259a98c95dde264e3bd45ba27542f194ad907ca9a42b33a1ebe4b898", "size_in_bytes": 1837}, {"_path": "site-packages/pip/_internal/operations/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_internal/operations/build/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_internal/operations/build/build_tracker.py", "path_type": "hardlink", "sha256": "f80456fd37231c2397ec3d8d50e1a7b41e0581ce9be1aa25b179002ba0562fbc", "sha256_in_prefix": "f80456fd37231c2397ec3d8d50e1a7b41e0581ce9be1aa25b179002ba0562fbc", "size_in_bytes": 4774}, {"_path": "site-packages/pip/_internal/operations/build/metadata.py", "path_type": "hardlink", "sha256": "20d1da7a245f3a22d80970297c3351a3d0b0db12385704dcd0a22dbdf75f3a39", "sha256_in_prefix": "20d1da7a245f3a22d80970297c3351a3d0b0db12385704dcd0a22dbdf75f3a39", "size_in_bytes": 1421}, {"_path": "site-packages/pip/_internal/operations/build/metadata_editable.py", "path_type": "hardlink", "sha256": "a16b9d32c9e3cb89683bf272ee0e0df67c6c9da117fe20e5551802cbba6ed6bb", "sha256_in_prefix": "a16b9d32c9e3cb89683bf272ee0e0df67c6c9da117fe20e5551802cbba6ed6bb", "size_in_bytes": 1509}, {"_path": "site-packages/pip/_internal/operations/build/metadata_legacy.py", "path_type": "hardlink", "sha256": "c2ff1c140d304ea17ad89966f50c25a1866ca1f3b243bb160419af09c56f9f59", "sha256_in_prefix": "c2ff1c140d304ea17ad89966f50c25a1866ca1f3b243bb160419af09c56f9f59", "size_in_bytes": 2189}, {"_path": "site-packages/pip/_internal/operations/build/wheel.py", "path_type": "hardlink", "sha256": "b13d761412c0c430bac32ac3a2b87c92f719d631b9a889c2456cf33fe5242624", "sha256_in_prefix": "b13d761412c0c430bac32ac3a2b87c92f719d631b9a889c2456cf33fe5242624", "size_in_bytes": 1075}, {"_path": "site-packages/pip/_internal/operations/build/wheel_editable.py", "path_type": "hardlink", "sha256": "c8eb681face9024a0a60452dafc161ceb62790d1d0690063590d8761a7b53108", "sha256_in_prefix": "c8eb681face9024a0a60452dafc161ceb62790d1d0690063590d8761a7b53108", "size_in_bytes": 1417}, {"_path": "site-packages/pip/_internal/operations/build/wheel_legacy.py", "path_type": "hardlink", "sha256": "297a72198a0241872e757359be884b5e058768293819fdf3d1d6d2f68978baed", "sha256_in_prefix": "297a72198a0241872e757359be884b5e058768293819fdf3d1d6d2f68978baed", "size_in_bytes": 3620}, {"_path": "site-packages/pip/_internal/operations/check.py", "path_type": "hardlink", "sha256": "e1c9c3ff67a096c0dee6cd82a189310ede077118ad4f2c332cc7d367eb464385", "sha256_in_prefix": "e1c9c3ff67a096c0dee6cd82a189310ede077118ad4f2c332cc7d367eb464385", "size_in_bytes": 5911}, {"_path": "site-packages/pip/_internal/operations/freeze.py", "path_type": "hardlink", "sha256": "d7f33bf630102a70b15abf8a082987b955ce54519a5091e6a162df173821ecae", "sha256_in_prefix": "d7f33bf630102a70b15abf8a082987b955ce54519a5091e6a162df173821ecae", "size_in_bytes": 9843}, {"_path": "site-packages/pip/_internal/operations/install/__init__.py", "path_type": "hardlink", "sha256": "6a4f941137103ca94565a5a860a5aee505576e9141be0d2c5dcde2d0ee2f4986", "sha256_in_prefix": "6a4f941137103ca94565a5a860a5aee505576e9141be0d2c5dcde2d0ee2f4986", "size_in_bytes": 50}, {"_path": "site-packages/pip/_internal/operations/install/editable_legacy.py", "path_type": "hardlink", "sha256": "4c8eb04fcb0ba834e9ad664b60438139eedaebed41f6ec0a6fb9130712c86966", "sha256_in_prefix": "4c8eb04fcb0ba834e9ad664b60438139eedaebed41f6ec0a6fb9130712c86966", "size_in_bytes": 1282}, {"_path": "site-packages/pip/_internal/operations/install/wheel.py", "path_type": "hardlink", "sha256": "cbfc39e2d9013050a841299ea01bfb00dbfbfbfcad2c84a96cc5d9edf6192705", "sha256_in_prefix": "cbfc39e2d9013050a841299ea01bfb00dbfbfbfcad2c84a96cc5d9edf6192705", "size_in_bytes": 27513}, {"_path": "site-packages/pip/_internal/operations/prepare.py", "path_type": "hardlink", "sha256": "fa2f5d630c0926337b87ab194da6dccfce2d8b3827ec402c634b079cb01fb374", "sha256_in_prefix": "fa2f5d630c0926337b87ab194da6dccfce2d8b3827ec402c634b079cb01fb374", "size_in_bytes": 28363}, {"_path": "site-packages/pip/_internal/pyproject.py", "path_type": "hardlink", "sha256": "18b27aad6452e7fda7a0a75a8e88682f20edcb9ed9ed05e17140188219939d67", "sha256_in_prefix": "18b27aad6452e7fda7a0a75a8e88682f20edcb9ed9ed05e17140188219939d67", "size_in_bytes": 7286}, {"_path": "site-packages/pip/_internal/req/__init__.py", "path_type": "hardlink", "sha256": "757d901a57c3c04a84e692e339e33e7f6a84817167e9fd95762ff3207018cb59", "sha256_in_prefix": "757d901a57c3c04a84e692e339e33e7f6a84817167e9fd95762ff3207018cb59", "size_in_bytes": 3096}, {"_path": "site-packages/pip/_internal/req/constructors.py", "path_type": "hardlink", "sha256": "bf5ab308dd66225770c7e9c2acf73c24ee25c649b716ff0ce515afb7c2c84a37", "sha256_in_prefix": "bf5ab308dd66225770c7e9c2acf73c24ee25c649b716ff0ce515afb7c2c84a37", "size_in_bytes": 18430}, {"_path": "site-packages/pip/_internal/req/req_dependency_group.py", "path_type": "hardlink", "sha256": "d752e48e10465821ef210afc4d78f5daa385f34806480c89e9c6e6438e0e1781", "sha256_in_prefix": "d752e48e10465821ef210afc4d78f5daa385f34806480c89e9c6e6438e0e1781", "size_in_bytes": 2589}, {"_path": "site-packages/pip/_internal/req/req_file.py", "path_type": "hardlink", "sha256": "7b2b3cd8c72068808e1a27365194478c3ef6d2988a24fc2679261d5e55b097ac", "sha256_in_prefix": "7b2b3cd8c72068808e1a27365194478c3ef6d2988a24fc2679261d5e55b097ac", "size_in_bytes": 20234}, {"_path": "site-packages/pip/_internal/req/req_install.py", "path_type": "hardlink", "sha256": "80ca056a4f73ae18c794768e4313c585e1cab48a1ba69160ab55ab29e97f9d31", "sha256_in_prefix": "80ca056a4f73ae18c794768e4313c585e1cab48a1ba69160ab55ab29e97f9d31", "size_in_bytes": 35788}, {"_path": "site-packages/pip/_internal/req/req_set.py", "path_type": "hardlink", "sha256": "8f77ac1b4b3a4b3a1545e5fdad69f8ae960db72113fdfc316f024f4629af471a", "sha256_in_prefix": "8f77ac1b4b3a4b3a1545e5fdad69f8ae960db72113fdfc316f024f4629af471a", "size_in_bytes": 2858}, {"_path": "site-packages/pip/_internal/req/req_uninstall.py", "path_type": "hardlink", "sha256": "3d0e92ca8703c9c518b0b02c4e98e46ddc0efea8dda3ecbc06f41f67902f76bc", "sha256_in_prefix": "3d0e92ca8703c9c518b0b02c4e98e46ddc0efea8dda3ecbc06f41f67902f76bc", "size_in_bytes": 24075}, {"_path": "site-packages/pip/_internal/resolution/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_internal/resolution/base.py", "path_type": "hardlink", "sha256": "aa59a1df6e520557ef1ba31ef6073936c879b1dc07070cc706ae9a117b4ab0b0", "sha256_in_prefix": "aa59a1df6e520557ef1ba31ef6073936c879b1dc07070cc706ae9a117b4ab0b0", "size_in_bytes": 583}, {"_path": "site-packages/pip/_internal/resolution/legacy/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_internal/resolution/legacy/resolver.py", "path_type": "hardlink", "sha256": "dc766224145dd454cdea3429238a913bcf936cb61e21b5134ba3c5bd79d7b36c", "sha256_in_prefix": "dc766224145dd454cdea3429238a913bcf936cb61e21b5134ba3c5bd79d7b36c", "size_in_bytes": 24068}, {"_path": "site-packages/pip/_internal/resolution/resolvelib/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_internal/resolution/resolvelib/base.py", "path_type": "hardlink", "sha256": "0c27faebd16cab2418e6ea9779e3c31d06357b840efa9073587f0ed2cf7e2bde", "sha256_in_prefix": "0c27faebd16cab2418e6ea9779e3c31d06357b840efa9073587f0ed2cf7e2bde", "size_in_bytes": 5023}, {"_path": "site-packages/pip/_internal/resolution/resolvelib/candidates.py", "path_type": "hardlink", "sha256": "537429f378e133f46226f8b2ae53c28a36e9b3ac183ba56c4c36939c27ff077a", "sha256_in_prefix": "537429f378e133f46226f8b2ae53c28a36e9b3ac183ba56c4c36939c27ff077a", "size_in_bytes": 20241}, {"_path": "site-packages/pip/_internal/resolution/resolvelib/factory.py", "path_type": "hardlink", "sha256": "142bc773d33c509ffb894eb742d3e21eeabf0667dd9c188c27c59a0c124d1719", "sha256_in_prefix": "142bc773d33c509ffb894eb742d3e21eeabf0667dd9c188c27c59a0c124d1719", "size_in_bytes": 32668}, {"_path": "site-packages/pip/_internal/resolution/resolvelib/found_candidates.py", "path_type": "hardlink", "sha256": "ea5005fe92d0dbf674081387205c46a7a35fbd3d6ec08a42ba2e9ef86808e6d9", "sha256_in_prefix": "ea5005fe92d0dbf674081387205c46a7a35fbd3d6ec08a42ba2e9ef86808e6d9", "size_in_bytes": 6000}, {"_path": "site-packages/pip/_internal/resolution/resolvelib/provider.py", "path_type": "hardlink", "sha256": "f29b5838e8df6b7dfa0f8159ef9d444074742c3abc8c98481895c3a2ef02bfc6", "sha256_in_prefix": "f29b5838e8df6b7dfa0f8159ef9d444074742c3abc8c98481895c3a2ef02bfc6", "size_in_bytes": 11190}, {"_path": "site-packages/pip/_internal/resolution/resolvelib/reporter.py", "path_type": "hardlink", "sha256": "1302401ce8d667c7931d05acb3bcc98e64048faa283faa164b0c155c5b60a6a4", "sha256_in_prefix": "1302401ce8d667c7931d05acb3bcc98e64048faa283faa164b0c155c5b60a6a4", "size_in_bytes": 3260}, {"_path": "site-packages/pip/_internal/resolution/resolvelib/requirements.py", "path_type": "hardlink", "sha256": "ec91b867bd9ee58938bd4d12e6e946bdba93cb814c406621639cd0857f734ed6", "sha256_in_prefix": "ec91b867bd9ee58938bd4d12e6e946bdba93cb814c406621639cd0857f734ed6", "size_in_bytes": 8065}, {"_path": "site-packages/pip/_internal/resolution/resolvelib/resolver.py", "path_type": "hardlink", "sha256": "f73711e1ced4655d63d882d399bebc0a4ff91ddbd0bdfe1c993044d9b58790a8", "sha256_in_prefix": "f73711e1ced4655d63d882d399bebc0a4ff91ddbd0bdfe1c993044d9b58790a8", "size_in_bytes": 12785}, {"_path": "site-packages/pip/_internal/self_outdated_check.py", "path_type": "hardlink", "sha256": "d4f16db6dbcb01ec82551ded3e806adac3a53c3d0827e292a94e9b7351d493d7", "sha256_in_prefix": "d4f16db6dbcb01ec82551ded3e806adac3a53c3d0827e292a94e9b7351d493d7", "size_in_bytes": 8318}, {"_path": "site-packages/pip/_internal/utils/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_internal/utils/_jaraco_text.py", "path_type": "hardlink", "sha256": "335e6e50f221e4da4fd6d754181c516aeeaad59004b48f3e5f22c4113b1c15f1", "sha256_in_prefix": "335e6e50f221e4da4fd6d754181c516aeeaad59004b48f3e5f22c4113b1c15f1", "size_in_bytes": 3350}, {"_path": "site-packages/pip/_internal/utils/_log.py", "path_type": "hardlink", "sha256": "fa31cb384fd31da673e4115c0a7a122fd11802d2749d77a6e3db3da1fe23bcac", "sha256_in_prefix": "fa31cb384fd31da673e4115c0a7a122fd11802d2749d77a6e3db3da1fe23bcac", "size_in_bytes": 1015}, {"_path": "site-packages/pip/_internal/utils/appdirs.py", "path_type": "hardlink", "sha256": "ceb2084829f6431957630fb32596414eb14d4f2cbfd1634a888f933a837ac09a", "sha256_in_prefix": "ceb2084829f6431957630fb32596414eb14d4f2cbfd1634a888f933a837ac09a", "size_in_bytes": 1705}, {"_path": "site-packages/pip/_internal/utils/compat.py", "path_type": "hardlink", "sha256": "724905bde0626108d15a390db1a8edfe858f4b9eed26f13c5f1a02e0e2188026", "sha256_in_prefix": "724905bde0626108d15a390db1a8edfe858f4b9eed26f13c5f1a02e0e2188026", "size_in_bytes": 2399}, {"_path": "site-packages/pip/_internal/utils/compatibility_tags.py", "path_type": "hardlink", "sha256": "ab95bb22b365a82e647b402a5911ba697e698a68aa87efb1b8f08237008a3ec5", "sha256_in_prefix": "ab95bb22b365a82e647b402a5911ba697e698a68aa87efb1b8f08237008a3ec5", "size_in_bytes": 6662}, {"_path": "site-packages/pip/_internal/utils/datetime.py", "path_type": "hardlink", "sha256": "1addbd325e13a0f48cf3c8f9e22bb8dd62adad4f40fa6a0fe1098c8a2ab379d5", "sha256_in_prefix": "1addbd325e13a0f48cf3c8f9e22bb8dd62adad4f40fa6a0fe1098c8a2ab379d5", "size_in_bytes": 241}, {"_path": "site-packages/pip/_internal/utils/deprecation.py", "path_type": "hardlink", "sha256": "93b420fd404069a4ddcaaf3661501103a0fb4667064d71afedf9df7208a08f84", "sha256_in_prefix": "93b420fd404069a4ddcaaf3661501103a0fb4667064d71afedf9df7208a08f84", "size_in_bytes": 3707}, {"_path": "site-packages/pip/_internal/utils/direct_url_helpers.py", "path_type": "hardlink", "sha256": "af6311b64543002bfd006a983830540bd0a3c20b6c514d6cebc86681f08932d0", "sha256_in_prefix": "af6311b64543002bfd006a983830540bd0a3c20b6c514d6cebc86681f08932d0", "size_in_bytes": 3196}, {"_path": "site-packages/pip/_internal/utils/egg_link.py", "path_type": "hardlink", "sha256": "d0578f6685182afe11190dadeb1ef0e59e36ef06c0fd4a375999c092b82cbaaa", "sha256_in_prefix": "d0578f6685182afe11190dadeb1ef0e59e36ef06c0fd4a375999c092b82cbaaa", "size_in_bytes": 2463}, {"_path": "site-packages/pip/_internal/utils/entrypoints.py", "path_type": "hardlink", "sha256": "e0285e67cd4e04f3cb6f71a7f97fd610fb6595489ba700d5ce5550e11161ecf3", "sha256_in_prefix": "e0285e67cd4e04f3cb6f71a7f97fd610fb6595489ba700d5ce5550e11161ecf3", "size_in_bytes": 3325}, {"_path": "site-packages/pip/_internal/utils/filesystem.py", "path_type": "hardlink", "sha256": "6a3bc0faae28725896f643e9f18aae87ee2fb2c5dbbbe50a6e8e4557d5785fae", "sha256_in_prefix": "6a3bc0faae28725896f643e9f18aae87ee2fb2c5dbbbe50a6e8e4557d5785fae", "size_in_bytes": 4950}, {"_path": "site-packages/pip/_internal/utils/filetypes.py", "path_type": "hardlink", "sha256": "3823f3531ab701aea4ffde4c888f0362090eceeb53b38edf03ebfebd8509a7d1", "sha256_in_prefix": "3823f3531ab701aea4ffde4c888f0362090eceeb53b38edf03ebfebd8509a7d1", "size_in_bytes": 715}, {"_path": "site-packages/pip/_internal/utils/glibc.py", "path_type": "hardlink", "sha256": "bd4916abfd6926ecdc60d70628b9509800685228ac2bc9e8618d7273c5aae30e", "sha256_in_prefix": "bd4916abfd6926ecdc60d70628b9509800685228ac2bc9e8618d7273c5aae30e", "size_in_bytes": 3734}, {"_path": "site-packages/pip/_internal/utils/hashes.py", "path_type": "hardlink", "sha256": "5c618b2f4006f3e4615a7cb3f3bc45e8c159fbe04a69d1d4df90f8ede02908a2", "sha256_in_prefix": "5c618b2f4006f3e4615a7cb3f3bc45e8c159fbe04a69d1d4df90f8ede02908a2", "size_in_bytes": 4972}, {"_path": "site-packages/pip/_internal/utils/logging.py", "path_type": "hardlink", "sha256": "ccc64ad4dc5f84ce10306532694f6ad60acdb8384b4956d29060af6412a668fc", "sha256_in_prefix": "ccc64ad4dc5f84ce10306532694f6ad60acdb8384b4956d29060af6412a668fc", "size_in_bytes": 12076}, {"_path": "site-packages/pip/_internal/utils/misc.py", "path_type": "hardlink", "sha256": "ef2ddb012ddb5af06a616b7a57bab2006382192ae72d5b12f178a40246d7adac", "sha256_in_prefix": "ef2ddb012ddb5af06a616b7a57bab2006382192ae72d5b12f178a40246d7adac", "size_in_bytes": 22959}, {"_path": "site-packages/pip/_internal/utils/packaging.py", "path_type": "hardlink", "sha256": "0a324ea8b344356f94f3ca233a59552f8d1fd5a6f65b6066dca1c26afc0d35fc", "sha256_in_prefix": "0a324ea8b344356f94f3ca233a59552f8d1fd5a6f65b6066dca1c26afc0d35fc", "size_in_bytes": 1603}, {"_path": "site-packages/pip/_internal/utils/retry.py", "path_type": "hardlink", "sha256": "9a115bca45e38539d97e0cdebb2faf97d73c9c40a7627fc232dc0d257dad6334", "sha256_in_prefix": "9a115bca45e38539d97e0cdebb2faf97d73c9c40a7627fc232dc0d257dad6334", "size_in_bytes": 1392}, {"_path": "site-packages/pip/_internal/utils/setuptools_build.py", "path_type": "hardlink", "sha256": "27d13245a9ed560bb857ec52fea7d0cb699c3cb55433b03edb6ed074a1946420", "sha256_in_prefix": "27d13245a9ed560bb857ec52fea7d0cb699c3cb55433b03edb6ed074a1946420", "size_in_bytes": 4482}, {"_path": "site-packages/pip/_internal/utils/subprocess.py", "path_type": "hardlink", "sha256": "12cbea49189230717df13f13c66bba34b53753ef8ca534d08ed36028fd0ffbe3", "sha256_in_prefix": "12cbea49189230717df13f13c66bba34b53753ef8ca534d08ed36028fd0ffbe3", "size_in_bytes": 8988}, {"_path": "site-packages/pip/_internal/utils/temp_dir.py", "path_type": "hardlink", "sha256": "e6a3977bc33825e63abda15033cebb779ce4a756d2c0c67e293e63ca698fd198", "sha256_in_prefix": "e6a3977bc33825e63abda15033cebb779ce4a756d2c0c67e293e63ca698fd198", "size_in_bytes": 9310}, {"_path": "site-packages/pip/_internal/utils/unpacking.py", "path_type": "hardlink", "sha256": "e320aa951008db3c65e6d7f19672e858b35c127fa4d5cdef69eb7fa1a278da22", "sha256_in_prefix": "e320aa951008db3c65e6d7f19672e858b35c127fa4d5cdef69eb7fa1a278da22", "size_in_bytes": 11926}, {"_path": "site-packages/pip/_internal/utils/urls.py", "path_type": "hardlink", "sha256": "a9c7923996f995b343ac736cbfbfd2e0be18b6cce36b93703ca50c9d91db6273", "sha256_in_prefix": "a9c7923996f995b343ac736cbfbfd2e0be18b6cce36b93703ca50c9d91db6273", "size_in_bytes": 1599}, {"_path": "site-packages/pip/_internal/utils/virtualenv.py", "path_type": "hardlink", "sha256": "4ba7fb72c628ad1a620fa72f9f78c849961cdc8f0f242e371f988c1694401035", "sha256_in_prefix": "4ba7fb72c628ad1a620fa72f9f78c849961cdc8f0f242e371f988c1694401035", "size_in_bytes": 3456}, {"_path": "site-packages/pip/_internal/utils/wheel.py", "path_type": "hardlink", "sha256": "30739b627e9ded5c992f5d22f96d71a09676853e7ec01d5690823bd00b18504f", "sha256_in_prefix": "30739b627e9ded5c992f5d22f96d71a09676853e7ec01d5690823bd00b18504f", "size_in_bytes": 4493}, {"_path": "site-packages/pip/_internal/vcs/__init__.py", "path_type": "hardlink", "sha256": "500aafce96e2d156d9a3751beac904799030fa8a08651fb35ff5a909bc720a85", "sha256_in_prefix": "500aafce96e2d156d9a3751beac904799030fa8a08651fb35ff5a909bc720a85", "size_in_bytes": 596}, {"_path": "site-packages/pip/_internal/vcs/bazaar.py", "path_type": "hardlink", "sha256": "10a4ad71068aa4dbb434ae29e50d7439ce316f70d4c45c34db85eb272e346c54", "sha256_in_prefix": "10a4ad71068aa4dbb434ae29e50d7439ce316f70d4c45c34db85eb272e346c54", "size_in_bytes": 3528}, {"_path": "site-packages/pip/_internal/vcs/git.py", "path_type": "hardlink", "sha256": "dca2cfacab032fdc597219b3e07d4e6e8f1f336161f0286b86d0c75a36e48fe2", "sha256_in_prefix": "dca2cfacab032fdc597219b3e07d4e6e8f1f336161f0286b86d0c75a36e48fe2", "size_in_bytes": 18591}, {"_path": "site-packages/pip/_internal/vcs/mercurial.py", "path_type": "hardlink", "sha256": "a142ce8732765227bed3a775a2690bfbf19cea6786694932a20bea1bd642c8fb", "sha256_in_prefix": "a142ce8732765227bed3a775a2690bfbf19cea6786694932a20bea1bd642c8fb", "size_in_bytes": 5249}, {"_path": "site-packages/pip/_internal/vcs/subversion.py", "path_type": "hardlink", "sha256": "75d4ee80706a1f357779b2a55394171cf378814aa5c976cec7cabc3605cabecf", "sha256_in_prefix": "75d4ee80706a1f357779b2a55394171cf378814aa5c976cec7cabc3605cabecf", "size_in_bytes": 11735}, {"_path": "site-packages/pip/_internal/vcs/versioncontrol.py", "path_type": "hardlink", "sha256": "72f7fffa19d302340b5c9dddd7b14c36141f70ed4070a594175d2d7eb6323fe7", "sha256_in_prefix": "72f7fffa19d302340b5c9dddd7b14c36141f70ed4070a594175d2d7eb6323fe7", "size_in_bytes": 22440}, {"_path": "site-packages/pip/_internal/wheel_builder.py", "path_type": "hardlink", "sha256": "67967600d0036ca7521d8f411eac3dcc6e7ed40c6cb4ee9833a9bdc8b59eed5c", "sha256_in_prefix": "67967600d0036ca7521d8f411eac3dcc6e7ed40c6cb4ee9833a9bdc8b59eed5c", "size_in_bytes": 11212}, {"_path": "site-packages/pip/_vendor/__init__.py", "path_type": "hardlink", "sha256": "5b3bac3d319620c790416495274876ada7c69154daf56289d874fedbe12866b5", "sha256_in_prefix": "5b3bac3d319620c790416495274876ada7c69154daf56289d874fedbe12866b5", "size_in_bytes": 4907}, {"_path": "site-packages/pip/_vendor/cachecontrol/__init__.py", "path_type": "hardlink", "sha256": "c7d79c5248b036f0067c4de0d1e691c7756b2599d958096e0362d172f69bdc74", "sha256_in_prefix": "c7d79c5248b036f0067c4de0d1e691c7756b2599d958096e0362d172f69bdc74", "size_in_bytes": 677}, {"_path": "site-packages/pip/_vendor/cachecontrol/_cmd.py", "path_type": "hardlink", "sha256": "8a2b2dd84a7326f0d5221300c57abc8859d306c89901dea2a65c5f98d6e83729", "sha256_in_prefix": "8a2b2dd84a7326f0d5221300c57abc8859d306c89901dea2a65c5f98d6e83729", "size_in_bytes": 1737}, {"_path": "site-packages/pip/_vendor/cachecontrol/adapter.py", "path_type": "hardlink", "sha256": "f32eab4cf5cecd51e60ca096e4247db22bcb56e0eff9ca5d19c65875158d68fc", "sha256_in_prefix": "f32eab4cf5cecd51e60ca096e4247db22bcb56e0eff9ca5d19c65875158d68fc", "size_in_bytes": 6599}, {"_path": "site-packages/pip/_vendor/cachecontrol/cache.py", "path_type": "hardlink", "sha256": "397c2fec59f60309ca3626a12479e3b6f68a2e776f54bbfffb33be96d955f6a2", "sha256_in_prefix": "397c2fec59f60309ca3626a12479e3b6f68a2e776f54bbfffb33be96d955f6a2", "size_in_bytes": 1953}, {"_path": "site-packages/pip/_vendor/cachecontrol/caches/__init__.py", "path_type": "hardlink", "sha256": "76daebae82b90670034751968c2675f5a674b45b0c7ef141b4b410535b29fda8", "sha256_in_prefix": "76daebae82b90670034751968c2675f5a674b45b0c7ef141b4b410535b29fda8", "size_in_bytes": 303}, {"_path": "site-packages/pip/_vendor/cachecontrol/caches/file_cache.py", "path_type": "hardlink", "sha256": "77cba9166cbfcf06829a56d61150652d715d76df19c3c739485a7178e66c75fc", "sha256_in_prefix": "77cba9166cbfcf06829a56d61150652d715d76df19c3c739485a7178e66c75fc", "size_in_bytes": 4117}, {"_path": "site-packages/pip/_vendor/cachecontrol/caches/redis_cache.py", "path_type": "hardlink", "sha256": "f6b9aac2d62efe58d5916ebfa0ba9b0bb11a5ff6bc613ff22ee9daf9e4b4760a", "sha256_in_prefix": "f6b9aac2d62efe58d5916ebfa0ba9b0bb11a5ff6bc613ff22ee9daf9e4b4760a", "size_in_bytes": 1386}, {"_path": "site-packages/pip/_vendor/cachecontrol/controller.py", "path_type": "hardlink", "sha256": "731d0797cc4b66052e5eecbbf068a1f406230adaaeae6623549c72038c96b7bc", "sha256_in_prefix": "731d0797cc4b66052e5eecbbf068a1f406230adaaeae6623549c72038c96b7bc", "size_in_bytes": 19101}, {"_path": "site-packages/pip/_vendor/cachecontrol/filewrapper.py", "path_type": "hardlink", "sha256": "da4b5734f1342aa9f2cc5db868eb0a080e7c1d0ab5c5e0ba97683aff3c238217", "sha256_in_prefix": "da4b5734f1342aa9f2cc5db868eb0a080e7c1d0ab5c5e0ba97683aff3c238217", "size_in_bytes": 4291}, {"_path": "site-packages/pip/_vendor/cachecontrol/heuristics.py", "path_type": "hardlink", "sha256": "82a31753cc34810b8442249dbb7620fb4bddf645bb9eb58a6cb71aef9ae17861", "sha256_in_prefix": "82a31753cc34810b8442249dbb7620fb4bddf645bb9eb58a6cb71aef9ae17861", "size_in_bytes": 4881}, {"_path": "site-packages/pip/_vendor/cachecontrol/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/cachecontrol/serialize.py", "path_type": "hardlink", "sha256": "1d0776225950d391f33e454b3174c5dae5f99a31108c3064c42a94254383a599", "sha256_in_prefix": "1d0776225950d391f33e454b3174c5dae5f99a31108c3064c42a94254383a599", "size_in_bytes": 5163}, {"_path": "site-packages/pip/_vendor/cachecontrol/wrapper.py", "path_type": "hardlink", "sha256": "86c19cee0f101904d3fb87fcb60cf700ce6ac12720e853b405274b491744be95", "sha256_in_prefix": "86c19cee0f101904d3fb87fcb60cf700ce6ac12720e853b405274b491744be95", "size_in_bytes": 1417}, {"_path": "site-packages/pip/_vendor/certifi/__init__.py", "path_type": "hardlink", "sha256": "9de21a01fec1337eb28264029b2f99b12ca39ef8d682115ebb5df0c040278e3d", "sha256_in_prefix": "9de21a01fec1337eb28264029b2f99b12ca39ef8d682115ebb5df0c040278e3d", "size_in_bytes": 94}, {"_path": "site-packages/pip/_vendor/certifi/__main__.py", "path_type": "hardlink", "sha256": "d64dc2afde6f0b1c464460e58eb5b7c0c76965d2f73617f4bb59fe936a9db026", "sha256_in_prefix": "d64dc2afde6f0b1c464460e58eb5b7c0c76965d2f73617f4bb59fe936a9db026", "size_in_bytes": 255}, {"_path": "site-packages/pip/_vendor/certifi/cacert.pem", "path_type": "hardlink", "sha256": "c55b21f907f7f86d48add093552fb5651749ff5f860508ccbb423d6c1fbd80c7", "sha256_in_prefix": "c55b21f907f7f86d48add093552fb5651749ff5f860508ccbb423d6c1fbd80c7", "size_in_bytes": 297255}, {"_path": "site-packages/pip/_vendor/certifi/core.py", "path_type": "hardlink", "sha256": "d92453e6b21c4028450db7b7ec141afa450bc40809f2a37a9758dfa93a781c8b", "sha256_in_prefix": "d92453e6b21c4028450db7b7ec141afa450bc40809f2a37a9758dfa93a781c8b", "size_in_bytes": 4486}, {"_path": "site-packages/pip/_vendor/certifi/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/dependency_groups/__init__.py", "path_type": "hardlink", "sha256": "0b7385bb4346c03cd0e0b3a69923853ec452be46c19fe99d7788ffe58a89c3eb", "sha256_in_prefix": "0b7385bb4346c03cd0e0b3a69923853ec452be46c19fe99d7788ffe58a89c3eb", "size_in_bytes": 250}, {"_path": "site-packages/pip/_vendor/dependency_groups/__main__.py", "path_type": "hardlink", "sha256": "50d4ccecfe667d5b53ef00e2f643935d6815ddfbb77bc6d3aedd50a758ef8caa", "sha256_in_prefix": "50d4ccecfe667d5b53ef00e2f643935d6815ddfbb77bc6d3aedd50a758ef8caa", "size_in_bytes": 1709}, {"_path": "site-packages/pip/_vendor/dependency_groups/_implementation.py", "path_type": "hardlink", "sha256": "5bcb14263d1877e990e0a1b83c1843d58a7873f43e7327beb241269fd5a541aa", "sha256_in_prefix": "5bcb14263d1877e990e0a1b83c1843d58a7873f43e7327beb241269fd5a541aa", "size_in_bytes": 8031}, {"_path": "site-packages/pip/_vendor/dependency_groups/_lint_dependency_groups.py", "path_type": "hardlink", "sha256": "ca9f830ea297b5b9034cd6b489f6be166380f2b6b6e253d91177ed5be47902e2", "sha256_in_prefix": "ca9f830ea297b5b9034cd6b489f6be166380f2b6b6e253d91177ed5be47902e2", "size_in_bytes": 1710}, {"_path": "site-packages/pip/_vendor/dependency_groups/_pip_wrapper.py", "path_type": "hardlink", "sha256": "9ee556ff0fe7b55c69136e842c4be780c634374e2c14bb228d747265944e146f", "sha256_in_prefix": "9ee556ff0fe7b55c69136e842c4be780c634374e2c14bb228d747265944e146f", "size_in_bytes": 1865}, {"_path": "site-packages/pip/_vendor/dependency_groups/_toml_compat.py", "path_type": "hardlink", "sha256": "0479d79c569c9b70dea25b00df91a323aaa40c0a6fb9afb5176d24bf705f6561", "sha256_in_prefix": "0479d79c569c9b70dea25b00df91a323aaa40c0a6fb9afb5176d24bf705f6561", "size_in_bytes": 285}, {"_path": "site-packages/pip/_vendor/dependency_groups/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/distlib/__init__.py", "path_type": "hardlink", "sha256": "75cc2060660642a0046b00573c3b48c7cd033bfddc3a616ff074dcf093339274", "sha256_in_prefix": "75cc2060660642a0046b00573c3b48c7cd033bfddc3a616ff074dcf093339274", "size_in_bytes": 625}, {"_path": "site-packages/pip/_vendor/distlib/compat.py", "path_type": "hardlink", "sha256": "da34528d1238a3ebe55de4cad8108621486473a7bd646852b1a711339a2c793c", "sha256_in_prefix": "da34528d1238a3ebe55de4cad8108621486473a7bd646852b1a711339a2c793c", "size_in_bytes": 41467}, {"_path": "site-packages/pip/_vendor/distlib/database.py", "path_type": "hardlink", "sha256": "987cbf2f189722c21545bf93d3e89d06b54bc3715f8a3e6d7870a96e3989f585", "sha256_in_prefix": "987cbf2f189722c21545bf93d3e89d06b54bc3715f8a3e6d7870a96e3989f585", "size_in_bytes": 51160}, {"_path": "site-packages/pip/_vendor/distlib/index.py", "path_type": "hardlink", "sha256": "9536f0dbaf2b4618fc770d6c89bdd567fd048521a0a093b714a27348530e69e0", "sha256_in_prefix": "9536f0dbaf2b4618fc770d6c89bdd567fd048521a0a093b714a27348530e69e0", "size_in_bytes": 20797}, {"_path": "site-packages/pip/_vendor/distlib/locators.py", "path_type": "hardlink", "sha256": "a0178066916e3d0498d3d3203672df4061805d7bd53bde8116967228cb8ae2d3", "sha256_in_prefix": "a0178066916e3d0498d3d3203672df4061805d7bd53bde8116967228cb8ae2d3", "size_in_bytes": 51026}, {"_path": "site-packages/pip/_vendor/distlib/manifest.py", "path_type": "hardlink", "sha256": "dea7e6026570c51a94d68db70257d7ad0199ce1ea0fc61b34c03ff1dbf42e734", "sha256_in_prefix": "dea7e6026570c51a94d68db70257d7ad0199ce1ea0fc61b34c03ff1dbf42e734", "size_in_bytes": 14168}, {"_path": "site-packages/pip/_vendor/distlib/markers.py", "path_type": "hardlink", "sha256": "5fab03be41467184bc8145bc85fb16b8a10a02a85064027b89738c2f14588d89", "sha256_in_prefix": "5fab03be41467184bc8145bc85fb16b8a10a02a85064027b89738c2f14588d89", "size_in_bytes": 5164}, {"_path": "site-packages/pip/_vendor/distlib/metadata.py", "path_type": "hardlink", "sha256": "ce2977b20d8451f2d75628258d8d9dff4dc826df894acee75feef77c408c5f6b", "sha256_in_prefix": "ce2977b20d8451f2d75628258d8d9dff4dc826df894acee75feef77c408c5f6b", "size_in_bytes": 38724}, {"_path": "site-packages/pip/_vendor/distlib/resources.py", "path_type": "hardlink", "sha256": "2f06cf92c73403524c6e2e979ee3dd301527f375fb04fb85356a8f184288ebdf", "sha256_in_prefix": "2f06cf92c73403524c6e2e979ee3dd301527f375fb04fb85356a8f184288ebdf", "size_in_bytes": 10820}, {"_path": "site-packages/pip/_vendor/distlib/scripts.py", "path_type": "hardlink", "sha256": "04996268301969507b580930a24802dc75f02c3da25a21da548e741fb0ba786f", "sha256_in_prefix": "04996268301969507b580930a24802dc75f02c3da25a21da548e741fb0ba786f", "size_in_bytes": 18608}, {"_path": "site-packages/pip/_vendor/distlib/t32.exe", "path_type": "hardlink", "sha256": "6b4195e640a85ac32eb6f9628822a622057df1e459df7c17a12f97aeabc9415b", "sha256_in_prefix": "6b4195e640a85ac32eb6f9628822a622057df1e459df7c17a12f97aeabc9415b", "size_in_bytes": 97792}, {"_path": "site-packages/pip/_vendor/distlib/t64-arm.exe", "path_type": "hardlink", "sha256": "ebc4c06b7d95e74e315419ee7e88e1d0f71e9e9477538c00a93a9ff8c66a6cfc", "sha256_in_prefix": "ebc4c06b7d95e74e315419ee7e88e1d0f71e9e9477538c00a93a9ff8c66a6cfc", "size_in_bytes": 182784}, {"_path": "site-packages/pip/_vendor/distlib/t64.exe", "path_type": "hardlink", "sha256": "81a618f21cb87db9076134e70388b6e9cb7c2106739011b6a51772d22cae06b7", "sha256_in_prefix": "81a618f21cb87db9076134e70388b6e9cb7c2106739011b6a51772d22cae06b7", "size_in_bytes": 108032}, {"_path": "site-packages/pip/_vendor/distlib/util.py", "path_type": "hardlink", "sha256": "bcc3c6bec4b88fd845e98f64dd3ca89b569a1cb6f4ac5999004cb378075e97dc", "sha256_in_prefix": "bcc3c6bec4b88fd845e98f64dd3ca89b569a1cb6f4ac5999004cb378075e97dc", "size_in_bytes": 66682}, {"_path": "site-packages/pip/_vendor/distlib/version.py", "path_type": "hardlink", "sha256": "b39548b3cc019f47f1cc6c5633f680d99672c79db91dc65b32f713953c99dd18", "sha256_in_prefix": "b39548b3cc019f47f1cc6c5633f680d99672c79db91dc65b32f713953c99dd18", "size_in_bytes": 23727}, {"_path": "site-packages/pip/_vendor/distlib/w32.exe", "path_type": "hardlink", "sha256": "47872cc77f8e18cf642f868f23340a468e537e64521d9a3a416c8b84384d064b", "sha256_in_prefix": "47872cc77f8e18cf642f868f23340a468e537e64521d9a3a416c8b84384d064b", "size_in_bytes": 91648}, {"_path": "site-packages/pip/_vendor/distlib/w64-arm.exe", "path_type": "hardlink", "sha256": "c5dc9884a8f458371550e09bd396e5418bf375820a31b9899f6499bf391c7b2e", "sha256_in_prefix": "c5dc9884a8f458371550e09bd396e5418bf375820a31b9899f6499bf391c7b2e", "size_in_bytes": 168448}, {"_path": "site-packages/pip/_vendor/distlib/w64.exe", "path_type": "hardlink", "sha256": "7a319ffaba23a017d7b1e18ba726ba6c54c53d6446db55f92af53c279894f8ad", "sha256_in_prefix": "7a319ffaba23a017d7b1e18ba726ba6c54c53d6446db55f92af53c279894f8ad", "size_in_bytes": 101888}, {"_path": "site-packages/pip/_vendor/distlib/wheel.py", "path_type": "hardlink", "sha256": "0c521582e1101c27719d27403b475f16c80c72f5598ad83b6c23ae2f067b03fb", "sha256_in_prefix": "0c521582e1101c27719d27403b475f16c80c72f5598ad83b6c23ae2f067b03fb", "size_in_bytes": 43979}, {"_path": "site-packages/pip/_vendor/distro/__init__.py", "path_type": "hardlink", "sha256": "d9f1e317e49f80fbe3c8d67588787fc23a96751fd8a393831f0642d232c13e17", "sha256_in_prefix": "d9f1e317e49f80fbe3c8d67588787fc23a96751fd8a393831f0642d232c13e17", "size_in_bytes": 981}, {"_path": "site-packages/pip/_vendor/distro/__main__.py", "path_type": "hardlink", "sha256": "6eef5ddd389fa0a72264572a441bb2815dc64ae4e19d50ff9b620ae1ccfde95b", "sha256_in_prefix": "6eef5ddd389fa0a72264572a441bb2815dc64ae4e19d50ff9b620ae1ccfde95b", "size_in_bytes": 64}, {"_path": "site-packages/pip/_vendor/distro/distro.py", "path_type": "hardlink", "sha256": "5ea6de7da7008434f8cebfedae76c0d79798f2f74ae064e08609af506ac433fe", "sha256_in_prefix": "5ea6de7da7008434f8cebfedae76c0d79798f2f74ae064e08609af506ac433fe", "size_in_bytes": 49430}, {"_path": "site-packages/pip/_vendor/distro/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/idna/__init__.py", "path_type": "hardlink", "sha256": "30fa8d0cb65b5ea19a35d5f1005862a853ca1105e3bb68cd42109ecbafb97893", "sha256_in_prefix": "30fa8d0cb65b5ea19a35d5f1005862a853ca1105e3bb68cd42109ecbafb97893", "size_in_bytes": 868}, {"_path": "site-packages/pip/_vendor/idna/codec.py", "path_type": "hardlink", "sha256": "3c47b0dc8b70ce35b887299b6ac9edcb6376397bcd7201c1f898eb06ec473d86", "sha256_in_prefix": "3c47b0dc8b70ce35b887299b6ac9edcb6376397bcd7201c1f898eb06ec473d86", "size_in_bytes": 3422}, {"_path": "site-packages/pip/_vendor/idna/compat.py", "path_type": "hardlink", "sha256": "4732f2e90402765f7bf3868585bd845fd10a1822638343f73e294675e5d7731f", "sha256_in_prefix": "4732f2e90402765f7bf3868585bd845fd10a1822638343f73e294675e5d7731f", "size_in_bytes": 316}, {"_path": "site-packages/pip/_vendor/idna/core.py", "path_type": "hardlink", "sha256": "60963200c9f089010f8d50b8f85aaefe9e0227ac8a2ae0c69a9a41350350a45b", "sha256_in_prefix": "60963200c9f089010f8d50b8f85aaefe9e0227ac8a2ae0c69a9a41350350a45b", "size_in_bytes": 13239}, {"_path": "site-packages/pip/_vendor/idna/idnadata.py", "path_type": "hardlink", "sha256": "5b7d067081afb4e598c008d98f8663ba8b94bad0ba7df80dbb28c9cbb7d9fa5a", "sha256_in_prefix": "5b7d067081afb4e598c008d98f8663ba8b94bad0ba7df80dbb28c9cbb7d9fa5a", "size_in_bytes": 78306}, {"_path": "site-packages/pip/_vendor/idna/intranges.py", "path_type": "hardlink", "sha256": "6a652d91d8587101bc66bf82a0c33f91545a731922bc2d568313756fadca29d5", "sha256_in_prefix": "6a652d91d8587101bc66bf82a0c33f91545a731922bc2d568313756fadca29d5", "size_in_bytes": 1898}, {"_path": "site-packages/pip/_vendor/idna/package_data.py", "path_type": "hardlink", "sha256": "ab9f52dce5ec739548f23eaf483d2c18133293acd9e2f58544413cf3208960ab", "sha256_in_prefix": "ab9f52dce5ec739548f23eaf483d2c18133293acd9e2f58544413cf3208960ab", "size_in_bytes": 21}, {"_path": "site-packages/pip/_vendor/idna/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/idna/uts46data.py", "path_type": "hardlink", "sha256": "aedf742bd278d20512c29a433c2ae18e08b9000ea958ceb974419149feab2213", "sha256_in_prefix": "aedf742bd278d20512c29a433c2ae18e08b9000ea958ceb974419149feab2213", "size_in_bytes": 239289}, {"_path": "site-packages/pip/_vendor/msgpack/__init__.py", "path_type": "hardlink", "sha256": "ade45a88eb44cd28cf9ebed3a718e022f6df967e6957ae8586b89c02cd9e0be8", "sha256_in_prefix": "ade45a88eb44cd28cf9ebed3a718e022f6df967e6957ae8586b89c02cd9e0be8", "size_in_bytes": 1109}, {"_path": "site-packages/pip/_vendor/msgpack/exceptions.py", "path_type": "hardlink", "sha256": "7424d67a2f1da64accb100dc8d093be004e5f47b08047d326edf3338f36a3187", "sha256_in_prefix": "7424d67a2f1da64accb100dc8d093be004e5f47b08047d326edf3338f36a3187", "size_in_bytes": 1081}, {"_path": "site-packages/pip/_vendor/msgpack/ext.py", "path_type": "hardlink", "sha256": "92d789bf4de7f6d633779a28df1628a554e8e2f45a031a27050409857a21659a", "sha256_in_prefix": "92d789bf4de7f6d633779a28df1628a554e8e2f45a031a27050409857a21659a", "size_in_bytes": 5726}, {"_path": "site-packages/pip/_vendor/msgpack/fallback.py", "path_type": "hardlink", "sha256": "d20d4fce9d2fb66044989e70f45decffe24c55444ff114b81b571ce5345a02c2", "sha256_in_prefix": "d20d4fce9d2fb66044989e70f45decffe24c55444ff114b81b571ce5345a02c2", "size_in_bytes": 32390}, {"_path": "site-packages/pip/_vendor/packaging/__init__.py", "path_type": "hardlink", "sha256": "ff470388f55fd92f9b35f566660bb1c739ab2185a5c804b1a6aa61e2ab095947", "sha256_in_prefix": "ff470388f55fd92f9b35f566660bb1c739ab2185a5c804b1a6aa61e2ab095947", "size_in_bytes": 494}, {"_path": "site-packages/pip/_vendor/packaging/_elffile.py", "path_type": "hardlink", "sha256": "524adb0ed5bb69eab7aaaa007d4d7aa23c87675c6c4ef1a47bf5aa31328029dd", "sha256_in_prefix": "524adb0ed5bb69eab7aaaa007d4d7aa23c87675c6c4ef1a47bf5aa31328029dd", "size_in_bytes": 3286}, {"_path": "site-packages/pip/_vendor/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "b78cbff9d4ce71faf7ea02d8fb3b623a9c7124518eda2902d751e07f2b06c623", "sha256_in_prefix": "b78cbff9d4ce71faf7ea02d8fb3b623a9c7124518eda2902d751e07f2b06c623", "size_in_bytes": 9596}, {"_path": "site-packages/pip/_vendor/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "sha256_in_prefix": "a7d66a35888e22d19e7bc29c64578717f61c76157018774aeabfbc9608b1bc64", "size_in_bytes": 2694}, {"_path": "site-packages/pip/_vendor/packaging/_parser.py", "path_type": "hardlink", "sha256": "8187e78f4a511df955738447662b75dca353c8df62895714d915021a2db60703", "sha256_in_prefix": "8187e78f4a511df955738447662b75dca353c8df62895714d915021a2db60703", "size_in_bytes": 10221}, {"_path": "site-packages/pip/_vendor/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "site-packages/pip/_vendor/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "398cedeea2b1ca538027eab45f22b5a80c9cc8f4582df30f74640a4579195b22", "sha256_in_prefix": "398cedeea2b1ca538027eab45f22b5a80c9cc8f4582df30f74640a4579195b22", "size_in_bytes": 5310}, {"_path": "site-packages/pip/_vendor/packaging/licenses/__init__.py", "path_type": "hardlink", "sha256": "ddbc7e82bca8e2c46fe4bb2bc00a68bb2eb9548b37bba8ab48e449cc02e4af35", "sha256_in_prefix": "ddbc7e82bca8e2c46fe4bb2bc00a68bb2eb9548b37bba8ab48e449cc02e4af35", "size_in_bytes": 5727}, {"_path": "site-packages/pip/_vendor/packaging/licenses/_spdx.py", "path_type": "hardlink", "sha256": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "sha256_in_prefix": "a009b5ced3c5c25b2608a7bb94002cbff38839f4b57160eef5b34191ebbeda7b", "size_in_bytes": 48398}, {"_path": "site-packages/pip/_vendor/packaging/markers.py", "path_type": "hardlink", "sha256": "3f4c1edbb8e6d71533806309c418ed50508859e0714ec31e253389e9c8599806", "sha256_in_prefix": "3f4c1edbb8e6d71533806309c418ed50508859e0714ec31e253389e9c8599806", "size_in_bytes": 12049}, {"_path": "site-packages/pip/_vendor/packaging/metadata.py", "path_type": "hardlink", "sha256": "f08644aea4109cd9b9ddd659b98ab8146538fdda728a731e1f540504858891f1", "sha256_in_prefix": "f08644aea4109cd9b9ddd659b98ab8146538fdda728a731e1f540504858891f1", "size_in_bytes": 34739}, {"_path": "site-packages/pip/_vendor/packaging/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/packaging/requirements.py", "path_type": "hardlink", "sha256": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "sha256_in_prefix": "818c9148075bac8c8a0d8ebaba02035108d132fc641f600b8a84e65f7b672faa", "size_in_bytes": 2947}, {"_path": "site-packages/pip/_vendor/packaging/specifiers.py", "path_type": "hardlink", "sha256": "c9cf43fccc9c2449b052966f72cd4e64bf477e23459b2c3445969e1d134790fc", "sha256_in_prefix": "c9cf43fccc9c2449b052966f72cd4e64bf477e23459b2c3445969e1d134790fc", "size_in_bytes": 40079}, {"_path": "site-packages/pip/_vendor/packaging/tags.py", "path_type": "hardlink", "sha256": "e35b3ded6f596adaead8477b45cdea7810da1def2928abd8ab69868f06a17d79", "sha256_in_prefix": "e35b3ded6f596adaead8477b45cdea7810da1def2928abd8ab69868f06a17d79", "size_in_bytes": 22745}, {"_path": "site-packages/pip/_vendor/packaging/utils.py", "path_type": "hardlink", "sha256": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "sha256_in_prefix": "d05dc787d385b9182b8538066549792b6d85bf560fdad665d73ff680eea42620", "size_in_bytes": 5050}, {"_path": "site-packages/pip/_vendor/packaging/version.py", "path_type": "hardlink", "sha256": "a221eacd352ffe9d768698e0b0b0d571a179853ee90da48e56250d303e064d6d", "sha256_in_prefix": "a221eacd352ffe9d768698e0b0b0d571a179853ee90da48e56250d303e064d6d", "size_in_bytes": 16688}, {"_path": "site-packages/pip/_vendor/pkg_resources/__init__.py", "path_type": "hardlink", "sha256": "8eb84345b3ae6cfef842e3d7c5ded4ecfa38d8f1f697e2d9d977dc3bb965a59e", "sha256_in_prefix": "8eb84345b3ae6cfef842e3d7c5ded4ecfa38d8f1f697e2d9d977dc3bb965a59e", "size_in_bytes": 124463}, {"_path": "site-packages/pip/_vendor/platformdirs/__init__.py", "path_type": "hardlink", "sha256": "51f7921d697c01e4ed6ce04ea070312b874e0ce5a466d7f2fa6fe2edc59d27c7", "sha256_in_prefix": "51f7921d697c01e4ed6ce04ea070312b874e0ce5a466d7f2fa6fe2edc59d27c7", "size_in_bytes": 22344}, {"_path": "site-packages/pip/_vendor/platformdirs/__main__.py", "path_type": "hardlink", "sha256": "8c127ccdbecca71e5e6dca85f37c6ba4ef7831a782a4d18755ff5cbc337624b8", "sha256_in_prefix": "8c127ccdbecca71e5e6dca85f37c6ba4ef7831a782a4d18755ff5cbc337624b8", "size_in_bytes": 1505}, {"_path": "site-packages/pip/_vendor/platformdirs/android.py", "path_type": "hardlink", "sha256": "af40ec85505ff913b58d72465fc0b84e297b1755d6b7e6e47563209af1226988", "sha256_in_prefix": "af40ec85505ff913b58d72465fc0b84e297b1755d6b7e6e47563209af1226988", "size_in_bytes": 9013}, {"_path": "site-packages/pip/_vendor/platformdirs/api.py", "path_type": "hardlink", "sha256": "53d133237118c5c5d6502b48191965aab70df7d8b62d26359aadbe1adb14c044", "sha256_in_prefix": "53d133237118c5c5d6502b48191965aab70df7d8b62d26359aadbe1adb14c044", "size_in_bytes": 9277}, {"_path": "site-packages/pip/_vendor/platformdirs/macos.py", "path_type": "hardlink", "sha256": "5256f2159f11ceedf19dd0aa4041eb7ec613787c187456a9d48a33fb2c6f793e", "sha256_in_prefix": "5256f2159f11ceedf19dd0aa4041eb7ec613787c187456a9d48a33fb2c6f793e", "size_in_bytes": 6154}, {"_path": "site-packages/pip/_vendor/platformdirs/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/platformdirs/unix.py", "path_type": "hardlink", "sha256": "5999a4500fbe2f724d4469b3df6b37e587e80f789c6bac4a20f74257f1e12dcb", "sha256_in_prefix": "5999a4500fbe2f724d4469b3df6b37e587e80f789c6bac4a20f74257f1e12dcb", "size_in_bytes": 10458}, {"_path": "site-packages/pip/_vendor/platformdirs/version.py", "path_type": "hardlink", "sha256": "d1f9f0e258dab1cc7b3b93df21e676ca3eb0de9024ab0a74f7dbc3722bf1baf6", "sha256_in_prefix": "d1f9f0e258dab1cc7b3b93df21e676ca3eb0de9024ab0a74f7dbc3722bf1baf6", "size_in_bytes": 511}, {"_path": "site-packages/pip/_vendor/platformdirs/windows.py", "path_type": "hardlink", "sha256": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "sha256_in_prefix": "205a62a21501c313ed0b39722b036dc725b8264f2169ae96f28e7d99fac35d5a", "size_in_bytes": 10125}, {"_path": "site-packages/pip/_vendor/pygments/__init__.py", "path_type": "hardlink", "sha256": "a8c9bbf8a62a36932b9a3ca665aa9f1feffd889b639dec402a87646fd1b90f98", "sha256_in_prefix": "a8c9bbf8a62a36932b9a3ca665aa9f1feffd889b639dec402a87646fd1b90f98", "size_in_bytes": 2983}, {"_path": "site-packages/pip/_vendor/pygments/__main__.py", "path_type": "hardlink", "sha256": "5ab9dda527ba8b57245ff490d4a6b10fd093286cc3d04b82385c5c6f0169a0b5", "sha256_in_prefix": "5ab9dda527ba8b57245ff490d4a6b10fd093286cc3d04b82385c5c6f0169a0b5", "size_in_bytes": 353}, {"_path": "site-packages/pip/_vendor/pygments/console.py", "path_type": "hardlink", "sha256": "01a8035aac1e6b6c8159fd74282f69b4180ca4c8f12a9f3200102687e3503959", "sha256_in_prefix": "01a8035aac1e6b6c8159fd74282f69b4180ca4c8f12a9f3200102687e3503959", "size_in_bytes": 1718}, {"_path": "site-packages/pip/_vendor/pygments/filter.py", "path_type": "hardlink", "sha256": "60bb694e7662bb4ee7637a0af677d1e84f58d4504784fe4f5fc82f90959c7da8", "sha256_in_prefix": "60bb694e7662bb4ee7637a0af677d1e84f58d4504784fe4f5fc82f90959c7da8", "size_in_bytes": 1910}, {"_path": "site-packages/pip/_vendor/pygments/filters/__init__.py", "path_type": "hardlink", "sha256": "e14e23b40d17de23fcdee42707df9323e1c34b0f04f32f333181dad148db6da2", "sha256_in_prefix": "e14e23b40d17de23fcdee42707df9323e1c34b0f04f32f333181dad148db6da2", "size_in_bytes": 40392}, {"_path": "site-packages/pip/_vendor/pygments/formatter.py", "path_type": "hardlink", "sha256": "29940c9b2a3fc643889101bc83ae8b6049018756f1edad07c8608172f848f44c", "sha256_in_prefix": "29940c9b2a3fc643889101bc83ae8b6049018756f1edad07c8608172f848f44c", "size_in_bytes": 4390}, {"_path": "site-packages/pip/_vendor/pygments/formatters/__init__.py", "path_type": "hardlink", "sha256": "293c019a75e56a8a498500ce7a6547607b24883821baafb4f18b4feb13cd24f8", "sha256_in_prefix": "293c019a75e56a8a498500ce7a6547607b24883821baafb4f18b4feb13cd24f8", "size_in_bytes": 5385}, {"_path": "site-packages/pip/_vendor/pygments/formatters/_mapping.py", "path_type": "hardlink", "sha256": "d42c37ec5b9094d69c9f144a9ad94f5f89f22e85fdfedb64a39670b1c354659e", "sha256_in_prefix": "d42c37ec5b9094d69c9f144a9ad94f5f89f22e85fdfedb64a39670b1c354659e", "size_in_bytes": 4176}, {"_path": "site-packages/pip/_vendor/pygments/lexer.py", "path_type": "hardlink", "sha256": "fe406b389fcd4f94e5d0854cd2b03d73c7b2b0febfcab946cc4408d1e55807e0", "sha256_in_prefix": "fe406b389fcd4f94e5d0854cd2b03d73c7b2b0febfcab946cc4408d1e55807e0", "size_in_bytes": 35349}, {"_path": "site-packages/pip/_vendor/pygments/lexers/__init__.py", "path_type": "hardlink", "sha256": "c1b20c137e461fb6c8d41f6b34f245a964fe8a3fd102964360f52567271a2f30", "sha256_in_prefix": "c1b20c137e461fb6c8d41f6b34f245a964fe8a3fd102964360f52567271a2f30", "size_in_bytes": 12115}, {"_path": "site-packages/pip/_vendor/pygments/lexers/_mapping.py", "path_type": "hardlink", "sha256": "978b425ccf1ef5a3c2d810fab2322bd1d793f89fb3e6d1e00b02fea757d2d0f1", "sha256_in_prefix": "978b425ccf1ef5a3c2d810fab2322bd1d793f89fb3e6d1e00b02fea757d2d0f1", "size_in_bytes": 77602}, {"_path": "site-packages/pip/_vendor/pygments/lexers/python.py", "path_type": "hardlink", "sha256": "bf18e7d5c38772520a24ac68ca206c41363ae461db919b5946e290d8054229ac", "sha256_in_prefix": "bf18e7d5c38772520a24ac68ca206c41363ae461db919b5946e290d8054229ac", "size_in_bytes": 53853}, {"_path": "site-packages/pip/_vendor/pygments/modeline.py", "path_type": "hardlink", "sha256": "2b9792911f064b5af93a45d74c739c57468cffac6993d7963442005be38e2768", "sha256_in_prefix": "2b9792911f064b5af93a45d74c739c57468cffac6993d7963442005be38e2768", "size_in_bytes": 1005}, {"_path": "site-packages/pip/_vendor/pygments/plugin.py", "path_type": "hardlink", "sha256": "b4fc74ac9093219f62a1180b3581b8a627c26c0c1345465d76f2f0f8d7c0936c", "sha256_in_prefix": "b4fc74ac9093219f62a1180b3581b8a627c26c0c1345465d76f2f0f8d7c0936c", "size_in_bytes": 1891}, {"_path": "site-packages/pip/_vendor/pygments/regexopt.py", "path_type": "hardlink", "sha256": "c1768ff468e9fe1280767202aa80e447100e40949ce2fdd7ea6731c77cfe4cdb", "sha256_in_prefix": "c1768ff468e9fe1280767202aa80e447100e40949ce2fdd7ea6731c77cfe4cdb", "size_in_bytes": 3072}, {"_path": "site-packages/pip/_vendor/pygments/scanner.py", "path_type": "hardlink", "sha256": "9cd7044d1475b51ba24da1e61d24d310255814f70b7fa98366ed5ee2ef7503d1", "sha256_in_prefix": "9cd7044d1475b51ba24da1e61d24d310255814f70b7fa98366ed5ee2ef7503d1", "size_in_bytes": 3092}, {"_path": "site-packages/pip/_vendor/pygments/sphinxext.py", "path_type": "hardlink", "sha256": "e71ed987d62553a212277d5d33076e89a6a76f97566672a0dccc8442cb1e3674", "sha256_in_prefix": "e71ed987d62553a212277d5d33076e89a6a76f97566672a0dccc8442cb1e3674", "size_in_bytes": 7981}, {"_path": "site-packages/pip/_vendor/pygments/style.py", "path_type": "hardlink", "sha256": "3e5399aa5b274d5779f111b2e74be403671743f94fe5b1791063040539e8e830", "sha256_in_prefix": "3e5399aa5b274d5779f111b2e74be403671743f94fe5b1791063040539e8e830", "size_in_bytes": 6420}, {"_path": "site-packages/pip/_vendor/pygments/styles/__init__.py", "path_type": "hardlink", "sha256": "c7d79b72d7f2bc2005a4c4e5309e58c7070d601ce382aeb325a2a4366efcaf83", "sha256_in_prefix": "c7d79b72d7f2bc2005a4c4e5309e58c7070d601ce382aeb325a2a4366efcaf83", "size_in_bytes": 2042}, {"_path": "site-packages/pip/_vendor/pygments/styles/_mapping.py", "path_type": "hardlink", "sha256": "ea5a2f154136f6dcfa12c5775d8638860a3327bab524bedc7cedd43a58274bcc", "sha256_in_prefix": "ea5a2f154136f6dcfa12c5775d8638860a3327bab524bedc7cedd43a58274bcc", "size_in_bytes": 3312}, {"_path": "site-packages/pip/_vendor/pygments/token.py", "path_type": "hardlink", "sha256": "59b7561a1626fd5a2c6f40c3c56f651cd3e02135df593b10987b7a732f516dc3", "sha256_in_prefix": "59b7561a1626fd5a2c6f40c3c56f651cd3e02135df593b10987b7a732f516dc3", "size_in_bytes": 6226}, {"_path": "site-packages/pip/_vendor/pygments/unistring.py", "path_type": "hardlink", "sha256": "6a5fbfac17a646e1af8a7b2b33a6ad36c1d3989e8351bc36e2ad8ed91bb57017", "sha256_in_prefix": "6a5fbfac17a646e1af8a7b2b33a6ad36c1d3989e8351bc36e2ad8ed91bb57017", "size_in_bytes": 63208}, {"_path": "site-packages/pip/_vendor/pygments/util.py", "path_type": "hardlink", "sha256": "a11b52a62028e6333dba59ed92f55b81751d8805b9ee39ee60607bb7d7d8ba17", "sha256_in_prefix": "a11b52a62028e6333dba59ed92f55b81751d8805b9ee39ee60607bb7d7d8ba17", "size_in_bytes": 10031}, {"_path": "site-packages/pip/_vendor/pyproject_hooks/__init__.py", "path_type": "hardlink", "sha256": "70f07f6bd2d7cf9c6fb116d7d68daac807632dab5925d43f2dce4c70d5fe5fb6", "sha256_in_prefix": "70f07f6bd2d7cf9c6fb116d7d68daac807632dab5925d43f2dce4c70d5fe5fb6", "size_in_bytes": 691}, {"_path": "site-packages/pip/_vendor/pyproject_hooks/_impl.py", "path_type": "hardlink", "sha256": "8d8fab6b19e6c91c81e7baee022b6b25153311ec6e021193a6033282ac7aed9e", "sha256_in_prefix": "8d8fab6b19e6c91c81e7baee022b6b25153311ec6e021193a6033282ac7aed9e", "size_in_bytes": 14936}, {"_path": "site-packages/pip/_vendor/pyproject_hooks/_in_process/__init__.py", "path_type": "hardlink", "sha256": "30934fa5f23170ef85821c6905bc641b5ac58907fa1ce51b5785399aad07167b", "sha256_in_prefix": "30934fa5f23170ef85821c6905bc641b5ac58907fa1ce51b5785399aad07167b", "size_in_bytes": 557}, {"_path": "site-packages/pip/_vendor/pyproject_hooks/_in_process/_in_process.py", "path_type": "hardlink", "sha256": "a9c5cc866c7ffcc209ab5d201875b7980e1397c772f18cc731c7309cda0a970d", "sha256_in_prefix": "a9c5cc866c7ffcc209ab5d201875b7980e1397c772f18cc731c7309cda0a970d", "size_in_bytes": 12216}, {"_path": "site-packages/pip/_vendor/pyproject_hooks/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/requests/__init__.py", "path_type": "hardlink", "sha256": "1e507f1f386bcc6b5f0ff69a614c14875cd65cb67be7f6022f28adef9774573f", "sha256_in_prefix": "1e507f1f386bcc6b5f0ff69a614c14875cd65cb67be7f6022f28adef9774573f", "size_in_bytes": 5057}, {"_path": "site-packages/pip/_vendor/requests/__version__.py", "path_type": "hardlink", "sha256": "1557e09606663509e660f5e93a8843539f05e4451bffe5674936807ac4b5f3b8", "sha256_in_prefix": "1557e09606663509e660f5e93a8843539f05e4451bffe5674936807ac4b5f3b8", "size_in_bytes": 435}, {"_path": "site-packages/pip/_vendor/requests/_internal_utils.py", "path_type": "hardlink", "sha256": "9cc4329abe21b37d93a95a3901b0ab99c24486f3d487bc57965bb2ab0b252e24", "sha256_in_prefix": "9cc4329abe21b37d93a95a3901b0ab99c24486f3d487bc57965bb2ab0b252e24", "size_in_bytes": 1495}, {"_path": "site-packages/pip/_vendor/requests/adapters.py", "path_type": "hardlink", "sha256": "27b55e571281bdac1bb655f60c4455a34e49f415d371660b30735dd4169af9b9", "sha256_in_prefix": "27b55e571281bdac1bb655f60c4455a34e49f415d371660b30735dd4169af9b9", "size_in_bytes": 27607}, {"_path": "site-packages/pip/_vendor/requests/api.py", "path_type": "hardlink", "sha256": "fd96fd39aeedcd5222cd32b016b3e30c463d7a3b66fce9d2444467003c46b10b", "sha256_in_prefix": "fd96fd39aeedcd5222cd32b016b3e30c463d7a3b66fce9d2444467003c46b10b", "size_in_bytes": 6449}, {"_path": "site-packages/pip/_vendor/requests/auth.py", "path_type": "hardlink", "sha256": "905ef9b6a9cb72d67d31ffe19bd4d9223e1c4169cde6ec51cfca16b31e70991d", "sha256_in_prefix": "905ef9b6a9cb72d67d31ffe19bd4d9223e1c4169cde6ec51cfca16b31e70991d", "size_in_bytes": 10186}, {"_path": "site-packages/pip/_vendor/requests/certs.py", "path_type": "hardlink", "sha256": "9070e590afdb7ae1d778c3dce63b5adb0825f2074a7945ade5fda74c356bbedf", "sha256_in_prefix": "9070e590afdb7ae1d778c3dce63b5adb0825f2074a7945ade5fda74c356bbedf", "size_in_bytes": 441}, {"_path": "site-packages/pip/_vendor/requests/compat.py", "path_type": "hardlink", "sha256": "328f5ff7166979fa1df199be9fdfd2b497154e6c12ba45d1da9dc8432c955ef5", "sha256_in_prefix": "328f5ff7166979fa1df199be9fdfd2b497154e6c12ba45d1da9dc8432c955ef5", "size_in_bytes": 1485}, {"_path": "site-packages/pip/_vendor/requests/cookies.py", "path_type": "hardlink", "sha256": "6cd8be8aa123e0d3d9d34fa86feac7bf392f39bccdde5129830de0ea9692dd7c", "sha256_in_prefix": "6cd8be8aa123e0d3d9d34fa86feac7bf392f39bccdde5129830de0ea9692dd7c", "size_in_bytes": 18590}, {"_path": "site-packages/pip/_vendor/requests/exceptions.py", "path_type": "hardlink", "sha256": "0f5c2acd85a77b5992dab538ded3fd09e3751bb400cbb7aa2fda3582877a123c", "sha256_in_prefix": "0f5c2acd85a77b5992dab538ded3fd09e3751bb400cbb7aa2fda3582877a123c", "size_in_bytes": 4272}, {"_path": "site-packages/pip/_vendor/requests/help.py", "path_type": "hardlink", "sha256": "85129a7fdbb41bb7ddc2ba8c1ed177a06d7a44a92d45fe8a8b0b52ab6168d7fd", "sha256_in_prefix": "85129a7fdbb41bb7ddc2ba8c1ed177a06d7a44a92d45fe8a8b0b52ab6168d7fd", "size_in_bytes": 3813}, {"_path": "site-packages/pip/_vendor/requests/hooks.py", "path_type": "hardlink", "sha256": "0a2bb2b221c0dfd57951f702057148c7cdc8ac3a6ec1f37d45c4d482fdbc7ed4", "sha256_in_prefix": "0a2bb2b221c0dfd57951f702057148c7cdc8ac3a6ec1f37d45c4d482fdbc7ed4", "size_in_bytes": 733}, {"_path": "site-packages/pip/_vendor/requests/models.py", "path_type": "hardlink", "sha256": "c782b80a61fe942d25d8a6fe88f7cc3787515f11c471b39a11604bfe2d3d0302", "sha256_in_prefix": "c782b80a61fe942d25d8a6fe88f7cc3787515f11c471b39a11604bfe2d3d0302", "size_in_bytes": 35483}, {"_path": "site-packages/pip/_vendor/requests/packages.py", "path_type": "hardlink", "sha256": "fd94030894c9f123f79155ae9d2a81b1164d3f38f673558556a6ddaf4f29cf75", "sha256_in_prefix": "fd94030894c9f123f79155ae9d2a81b1164d3f38f673558556a6ddaf4f29cf75", "size_in_bytes": 1057}, {"_path": "site-packages/pip/_vendor/requests/sessions.py", "path_type": "hardlink", "sha256": "ca44c8f145864a5b4e7c7d3b1caa25947ee44c11b0e168620556901a67244f0e", "sha256_in_prefix": "ca44c8f145864a5b4e7c7d3b1caa25947ee44c11b0e168620556901a67244f0e", "size_in_bytes": 30495}, {"_path": "site-packages/pip/_vendor/requests/status_codes.py", "path_type": "hardlink", "sha256": "889500780db96da4ddc3ee8f7c3d1e178aa1a48343251248fb268cab1b382c42", "sha256_in_prefix": "889500780db96da4ddc3ee8f7c3d1e178aa1a48343251248fb268cab1b382c42", "size_in_bytes": 4322}, {"_path": "site-packages/pip/_vendor/requests/structures.py", "path_type": "hardlink", "sha256": "f886e6855cf4e92fb968f499b94b6167afba0fd5ce8d1b935c739a6d8d38d573", "sha256_in_prefix": "f886e6855cf4e92fb968f499b94b6167afba0fd5ce8d1b935c739a6d8d38d573", "size_in_bytes": 2912}, {"_path": "site-packages/pip/_vendor/requests/utils.py", "path_type": "hardlink", "sha256": "2fbf6f9c56f32774852cab49c29a167b8d53a338b746566ff78a58d53148ca8c", "sha256_in_prefix": "2fbf6f9c56f32774852cab49c29a167b8d53a338b746566ff78a58d53148ca8c", "size_in_bytes": 33631}, {"_path": "site-packages/pip/_vendor/resolvelib/__init__.py", "path_type": "hardlink", "sha256": "e0b701587307df5ec42849295795cb567aa253596b9828b2823b000ee0a0cf8b", "sha256_in_prefix": "e0b701587307df5ec42849295795cb567aa253596b9828b2823b000ee0a0cf8b", "size_in_bytes": 541}, {"_path": "site-packages/pip/_vendor/resolvelib/providers.py", "path_type": "hardlink", "sha256": "a485896c874927d185b4d6edc131f421ae37563ea16021098f60ce2ee7b5e453", "sha256_in_prefix": "a485896c874927d185b4d6edc131f421ae37563ea16021098f60ce2ee7b5e453", "size_in_bytes": 8914}, {"_path": "site-packages/pip/_vendor/resolvelib/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/resolvelib/reporters.py", "path_type": "hardlink", "sha256": "f0135aec6f5c296e0b89ee0184385ded9e7b27f5656f50983c64a054dd9eacc0", "sha256_in_prefix": "f0135aec6f5c296e0b89ee0184385ded9e7b27f5656f50983c64a054dd9eacc0", "size_in_bytes": 2038}, {"_path": "site-packages/pip/_vendor/resolvelib/resolvers/__init__.py", "path_type": "hardlink", "sha256": "18c62e86b6d2b184c88ce8a3d2db8928bbe593a5179a9de75d07ad9f6b0ebe94", "sha256_in_prefix": "18c62e86b6d2b184c88ce8a3d2db8928bbe593a5179a9de75d07ad9f6b0ebe94", "size_in_bytes": 640}, {"_path": "site-packages/pip/_vendor/resolvelib/resolvers/abstract.py", "path_type": "hardlink", "sha256": "8d9381562804e0f52e6fd8b717e6d3bc1c1a2175fc4bd114dc2180481bc5a845", "sha256_in_prefix": "8d9381562804e0f52e6fd8b717e6d3bc1c1a2175fc4bd114dc2180481bc5a845", "size_in_bytes": 1558}, {"_path": "site-packages/pip/_vendor/resolvelib/resolvers/criterion.py", "path_type": "hardlink", "sha256": "95c9991afe6c2873a7143fd1cd9c2f9464a3d7d31e03ee6b08ca5d7f64a0c3b6", "sha256_in_prefix": "95c9991afe6c2873a7143fd1cd9c2f9464a3d7d31e03ee6b08ca5d7f64a0c3b6", "size_in_bytes": 1768}, {"_path": "site-packages/pip/_vendor/resolvelib/resolvers/exceptions.py", "path_type": "hardlink", "sha256": "967fe3690b602e545448563adbbca21c6da00fb020697cd129a12515587b7c34", "sha256_in_prefix": "967fe3690b602e545448563adbbca21c6da00fb020697cd129a12515587b7c34", "size_in_bytes": 1768}, {"_path": "site-packages/pip/_vendor/resolvelib/resolvers/resolution.py", "path_type": "hardlink", "sha256": "c907a032e3a697300494b2e9803fa4e8d6cf0cc0907f6f6b5a18850b6e8e7643", "sha256_in_prefix": "c907a032e3a697300494b2e9803fa4e8d6cf0cc0907f6f6b5a18850b6e8e7643", "size_in_bytes": 20671}, {"_path": "site-packages/pip/_vendor/resolvelib/structs.py", "path_type": "hardlink", "sha256": "a6ef84262476201213af649078d3d16b4ad7863952b5f98efc6120021af7d34e", "sha256_in_prefix": "a6ef84262476201213af649078d3d16b4ad7863952b5f98efc6120021af7d34e", "size_in_bytes": 6420}, {"_path": "site-packages/pip/_vendor/rich/__init__.py", "path_type": "hardlink", "sha256": "751c6320bf926c5558d2adc88d232b7e00531eb9b52d90e02ceca0541c226197", "sha256_in_prefix": "751c6320bf926c5558d2adc88d232b7e00531eb9b52d90e02ceca0541c226197", "size_in_bytes": 6090}, {"_path": "site-packages/pip/_vendor/rich/__main__.py", "path_type": "hardlink", "sha256": "78eec2abc267ae01bccd5a1e226880b3ddaade15cd3087e9d30e6532c3bb4366", "sha256_in_prefix": "78eec2abc267ae01bccd5a1e226880b3ddaade15cd3087e9d30e6532c3bb4366", "size_in_bytes": 8477}, {"_path": "site-packages/pip/_vendor/rich/_cell_widths.py", "path_type": "hardlink", "sha256": "7db99ec9eb447478f313f571da5d6e2bbb673ce84cb365f59497cedefb0a0e90", "sha256_in_prefix": "7db99ec9eb447478f313f571da5d6e2bbb673ce84cb365f59497cedefb0a0e90", "size_in_bytes": 10209}, {"_path": "site-packages/pip/_vendor/rich/_emoji_codes.py", "path_type": "hardlink", "sha256": "86ed552fd9db55da6926b5688a356c85195c4517bfbf7763bb7326776b0a65d6", "sha256_in_prefix": "86ed552fd9db55da6926b5688a356c85195c4517bfbf7763bb7326776b0a65d6", "size_in_bytes": 140235}, {"_path": "site-packages/pip/_vendor/rich/_emoji_replace.py", "path_type": "hardlink", "sha256": "9fe91c7adb04531d99526850adf78c35cfad79e1a1a6e490e45f153c1b32bc3a", "sha256_in_prefix": "9fe91c7adb04531d99526850adf78c35cfad79e1a1a6e490e45f153c1b32bc3a", "size_in_bytes": 1064}, {"_path": "site-packages/pip/_vendor/rich/_export_format.py", "path_type": "hardlink", "sha256": "448d3ca52ae6e6d052ccf32f9db4ea6c3f5621a95a3a837977833545398bab56", "sha256_in_prefix": "448d3ca52ae6e6d052ccf32f9db4ea6c3f5621a95a3a837977833545398bab56", "size_in_bytes": 2128}, {"_path": "site-packages/pip/_vendor/rich/_extension.py", "path_type": "hardlink", "sha256": "5ede3b41a7022b062bbb38c38be80e06aef6e0945e0e3f429bdc548b97ebfb7e", "sha256_in_prefix": "5ede3b41a7022b062bbb38c38be80e06aef6e0945e0e3f429bdc548b97ebfb7e", "size_in_bytes": 265}, {"_path": "site-packages/pip/_vendor/rich/_fileno.py", "path_type": "hardlink", "sha256": "1d66713f90b66a331b1ebcaf01066c79f9557d0a06cec28e1f3286b0b0fcca74", "sha256_in_prefix": "1d66713f90b66a331b1ebcaf01066c79f9557d0a06cec28e1f3286b0b0fcca74", "size_in_bytes": 799}, {"_path": "site-packages/pip/_vendor/rich/_inspect.py", "path_type": "hardlink", "sha256": "40cd399441671684da16aa676f1fb304423a93ca082ab0f772f8c43903612a28", "sha256_in_prefix": "40cd399441671684da16aa676f1fb304423a93ca082ab0f772f8c43903612a28", "size_in_bytes": 9655}, {"_path": "site-packages/pip/_vendor/rich/_log_render.py", "path_type": "hardlink", "sha256": "d41c88d0f035669c5963708624e2b9e218e5ab85fe073fdba088c8a8277c2a7b", "sha256_in_prefix": "d41c88d0f035669c5963708624e2b9e218e5ab85fe073fdba088c8a8277c2a7b", "size_in_bytes": 3225}, {"_path": "site-packages/pip/_vendor/rich/_loop.py", "path_type": "hardlink", "sha256": "855ffa08b7683e6d2f6b6d96a70e332aa334458b33dd36715e3d0fa12fbd7834", "sha256_in_prefix": "855ffa08b7683e6d2f6b6d96a70e332aa334458b33dd36715e3d0fa12fbd7834", "size_in_bytes": 1236}, {"_path": "site-packages/pip/_vendor/rich/_null_file.py", "path_type": "hardlink", "sha256": "00318aa75cadfa4ef414c295ead9ea0aa79c07ead2273a7e590b03ecb3cbfa48", "sha256_in_prefix": "00318aa75cadfa4ef414c295ead9ea0aa79c07ead2273a7e590b03ecb3cbfa48", "size_in_bytes": 1394}, {"_path": "site-packages/pip/_vendor/rich/_palettes.py", "path_type": "hardlink", "sha256": "71d7afd4940a67426f960b95f62a478339d3767be52335050c16f422dd8fce32", "sha256_in_prefix": "71d7afd4940a67426f960b95f62a478339d3767be52335050c16f422dd8fce32", "size_in_bytes": 7063}, {"_path": "site-packages/pip/_vendor/rich/_pick.py", "path_type": "hardlink", "sha256": "7af0edf10378945e428b0ad421794e2429ed8ad0423ac23764b3c42005512c95", "sha256_in_prefix": "7af0edf10378945e428b0ad421794e2429ed8ad0423ac23764b3c42005512c95", "size_in_bytes": 423}, {"_path": "site-packages/pip/_vendor/rich/_ratio.py", "path_type": "hardlink", "sha256": "66de7c6a9b3323a84001c5cfa607562a9bb7737d5405679b39e47899bca9b6f5", "sha256_in_prefix": "66de7c6a9b3323a84001c5cfa607562a9bb7737d5405679b39e47899bca9b6f5", "size_in_bytes": 5471}, {"_path": "site-packages/pip/_vendor/rich/_spinners.py", "path_type": "hardlink", "sha256": "536af5fe0ff5cd28ec8e251d00449cda200c7378b8ae2fd2f0f60fea4439cf52", "sha256_in_prefix": "536af5fe0ff5cd28ec8e251d00449cda200c7378b8ae2fd2f0f60fea4439cf52", "size_in_bytes": 19919}, {"_path": "site-packages/pip/_vendor/rich/_stack.py", "path_type": "hardlink", "sha256": "f82f0e2bbaf19f7b0851d570c59041a5e1e12335f4788f9533731e9987da5e6d", "sha256_in_prefix": "f82f0e2bbaf19f7b0851d570c59041a5e1e12335f4788f9533731e9987da5e6d", "size_in_bytes": 351}, {"_path": "site-packages/pip/_vendor/rich/_timer.py", "path_type": "hardlink", "sha256": "cde9716d3ea83c566736bc163e973592d51e013f957387ee15c4592d018bb4c2", "sha256_in_prefix": "cde9716d3ea83c566736bc163e973592d51e013f957387ee15c4592d018bb4c2", "size_in_bytes": 417}, {"_path": "site-packages/pip/_vendor/rich/_win32_console.py", "path_type": "hardlink", "sha256": "05268344833004b2139ff9b499344b3ea304e6afaab8675232e60ca587982707", "sha256_in_prefix": "05268344833004b2139ff9b499344b3ea304e6afaab8675232e60ca587982707", "size_in_bytes": 22755}, {"_path": "site-packages/pip/_vendor/rich/_windows.py", "path_type": "hardlink", "sha256": "681c1a0ff4b9e926e0a2922f6b2566a64d18dbcbb06360b905a6f5c25dc1a7e2", "sha256_in_prefix": "681c1a0ff4b9e926e0a2922f6b2566a64d18dbcbb06360b905a6f5c25dc1a7e2", "size_in_bytes": 1925}, {"_path": "site-packages/pip/_vendor/rich/_windows_renderer.py", "path_type": "hardlink", "sha256": "b7be192f7c6e0c23f79e64e9f691f52f92e223671a909b9045095e1c225eae59", "sha256_in_prefix": "b7be192f7c6e0c23f79e64e9f691f52f92e223671a909b9045095e1c225eae59", "size_in_bytes": 2783}, {"_path": "site-packages/pip/_vendor/rich/_wrap.py", "path_type": "hardlink", "sha256": "1654aca26e445f42d5900dca5b2df8c879c27cbb6a5fe6487a95ca87eef4ae97", "sha256_in_prefix": "1654aca26e445f42d5900dca5b2df8c879c27cbb6a5fe6487a95ca87eef4ae97", "size_in_bytes": 3404}, {"_path": "site-packages/pip/_vendor/rich/abc.py", "path_type": "hardlink", "sha256": "38df84f99a924a1799f3c56b297d8cdcf5e915b18451464f31afc07f497ee1fd", "sha256_in_prefix": "38df84f99a924a1799f3c56b297d8cdcf5e915b18451464f31afc07f497ee1fd", "size_in_bytes": 890}, {"_path": "site-packages/pip/_vendor/rich/align.py", "path_type": "hardlink", "sha256": "461fb769d9c368dd40a34ec48d1d8f86013ad8f18b3e03bc48cc09064bb5bab4", "sha256_in_prefix": "461fb769d9c368dd40a34ec48d1d8f86013ad8f18b3e03bc48cc09064bb5bab4", "size_in_bytes": 10469}, {"_path": "site-packages/pip/_vendor/rich/ansi.py", "path_type": "hardlink", "sha256": "02fb352c76d275cc8ebc339da442d952850b7018987b063be9e341a7ab85061b", "sha256_in_prefix": "02fb352c76d275cc8ebc339da442d952850b7018987b063be9e341a7ab85061b", "size_in_bytes": 6921}, {"_path": "site-packages/pip/_vendor/rich/bar.py", "path_type": "hardlink", "sha256": "95d6d51cecca24e9df95536ebf5c52ee0e9d2d7d84df03275e474f6e9cc94dcb", "sha256_in_prefix": "95d6d51cecca24e9df95536ebf5c52ee0e9d2d7d84df03275e474f6e9cc94dcb", "size_in_bytes": 3263}, {"_path": "site-packages/pip/_vendor/rich/box.py", "path_type": "hardlink", "sha256": "9ebe5f608520841fe250212aeb2d19dcb9424fc8053c3af337dbb6927eed265e", "sha256_in_prefix": "9ebe5f608520841fe250212aeb2d19dcb9424fc8053c3af337dbb6927eed265e", "size_in_bytes": 10831}, {"_path": "site-packages/pip/_vendor/rich/cells.py", "path_type": "hardlink", "sha256": "2ab4248f9f8b821082a492d23502320198e775ce1b9c4a8e1268b962e67d5026", "sha256_in_prefix": "2ab4248f9f8b821082a492d23502320198e775ce1b9c4a8e1268b962e67d5026", "size_in_bytes": 5130}, {"_path": "site-packages/pip/_vendor/rich/color.py", "path_type": "hardlink", "sha256": "dc74942d50e3eea4245d47455afefc24e8926737f2e72d6791c6219dadbde95d", "sha256_in_prefix": "dc74942d50e3eea4245d47455afefc24e8926737f2e72d6791c6219dadbde95d", "size_in_bytes": 18211}, {"_path": "site-packages/pip/_vendor/rich/color_triplet.py", "path_type": "hardlink", "sha256": "de585091d25bbd63e82c33be0276089805a626f579765818342559f7b39168de", "sha256_in_prefix": "de585091d25bbd63e82c33be0276089805a626f579765818342559f7b39168de", "size_in_bytes": 1054}, {"_path": "site-packages/pip/_vendor/rich/columns.py", "path_type": "hardlink", "sha256": "1d45f429c326f5db0a362d757d36e233f876883b65f3248269573195a944ceaf", "sha256_in_prefix": "1d45f429c326f5db0a362d757d36e233f876883b65f3248269573195a944ceaf", "size_in_bytes": 7131}, {"_path": "site-packages/pip/_vendor/rich/console.py", "path_type": "hardlink", "sha256": "fd1262cc14089fdab1af82e7ed0fd20b937d7a43d63c0c47d0a195c6c820ebd6", "sha256_in_prefix": "fd1262cc14089fdab1af82e7ed0fd20b937d7a43d63c0c47d0a195c6c820ebd6", "size_in_bytes": 100565}, {"_path": "site-packages/pip/_vendor/rich/constrain.py", "path_type": "hardlink", "sha256": "d5520fb82f0082d296adc9dc42b8c1758a80dc9556cacbba8d9a35aeb87b73b4", "sha256_in_prefix": "d5520fb82f0082d296adc9dc42b8c1758a80dc9556cacbba8d9a35aeb87b73b4", "size_in_bytes": 1288}, {"_path": "site-packages/pip/_vendor/rich/containers.py", "path_type": "hardlink", "sha256": "73fe7a4f171e74662a0dea4704c4ee65d5088a38ad010827a31f9075ed19d6aa", "sha256_in_prefix": "73fe7a4f171e74662a0dea4704c4ee65d5088a38ad010827a31f9075ed19d6aa", "size_in_bytes": 5502}, {"_path": "site-packages/pip/_vendor/rich/control.py", "path_type": "hardlink", "sha256": "0d29074d440ba2b7d211100a13fa1300450579f667669e1b41be2af2b1db2b0b", "sha256_in_prefix": "0d29074d440ba2b7d211100a13fa1300450579f667669e1b41be2af2b1db2b0b", "size_in_bytes": 6630}, {"_path": "site-packages/pip/_vendor/rich/default_styles.py", "path_type": "hardlink", "sha256": "921405aaa6a80ecddba6b32a5a91f0f273b95291b60cde90b6e4dde8bcd9c187", "sha256_in_prefix": "921405aaa6a80ecddba6b32a5a91f0f273b95291b60cde90b6e4dde8bcd9c187", "size_in_bytes": 8257}, {"_path": "site-packages/pip/_vendor/rich/diagnose.py", "path_type": "hardlink", "sha256": "58d3e3536a4476b3c8089db828e6930ff8733fcdfda80ae9985d4920ce71fc44", "sha256_in_prefix": "58d3e3536a4476b3c8089db828e6930ff8733fcdfda80ae9985d4920ce71fc44", "size_in_bytes": 998}, {"_path": "site-packages/pip/_vendor/rich/emoji.py", "path_type": "hardlink", "sha256": "a264c5f5ab1a027b0ce322d8f78791ffd7604514a6d651d4b335f6d03d726024", "sha256_in_prefix": "a264c5f5ab1a027b0ce322d8f78791ffd7604514a6d651d4b335f6d03d726024", "size_in_bytes": 2501}, {"_path": "site-packages/pip/_vendor/rich/errors.py", "path_type": "hardlink", "sha256": "e693f729ce5de1027f734285b31adfca18e23d57bb275ccea9215b140cdc57e6", "sha256_in_prefix": "e693f729ce5de1027f734285b31adfca18e23d57bb275ccea9215b140cdc57e6", "size_in_bytes": 642}, {"_path": "site-packages/pip/_vendor/rich/file_proxy.py", "path_type": "hardlink", "sha256": "4e5f531cc0d9f8f9395a6f2c23580683f5390e1bac9b10fe159d1f51b714d16d", "sha256_in_prefix": "4e5f531cc0d9f8f9395a6f2c23580683f5390e1bac9b10fe159d1f51b714d16d", "size_in_bytes": 1683}, {"_path": "site-packages/pip/_vendor/rich/filesize.py", "path_type": "hardlink", "sha256": "fe2cfd948a5182f5bb30d49e0999cb83e1f0cdb3f81844e0e78dd6a83f1216cd", "sha256_in_prefix": "fe2cfd948a5182f5bb30d49e0999cb83e1f0cdb3f81844e0e78dd6a83f1216cd", "size_in_bytes": 2484}, {"_path": "site-packages/pip/_vendor/rich/highlighter.py", "path_type": "hardlink", "sha256": "1bfb27fbc0ca8ccd6c1232c6fe8738a2f9169a25295af8fc6d78b4c9e7762e76", "sha256_in_prefix": "1bfb27fbc0ca8ccd6c1232c6fe8738a2f9169a25295af8fc6d78b4c9e7762e76", "size_in_bytes": 9586}, {"_path": "site-packages/pip/_vendor/rich/json.py", "path_type": "hardlink", "sha256": "bd512829d6b0a094630056b23f05e43013cbcbb4524ecf9fe38c124034769c9d", "sha256_in_prefix": "bd512829d6b0a094630056b23f05e43013cbcbb4524ecf9fe38c124034769c9d", "size_in_bytes": 5031}, {"_path": "site-packages/pip/_vendor/rich/jupyter.py", "path_type": "hardlink", "sha256": "432a0aa04ffc21d09baed8921e9f53b1348dc931d8d053b9c2113b8ce4ddf541", "sha256_in_prefix": "432a0aa04ffc21d09baed8921e9f53b1348dc931d8d053b9c2113b8ce4ddf541", "size_in_bytes": 3252}, {"_path": "site-packages/pip/_vendor/rich/layout.py", "path_type": "hardlink", "sha256": "6a3912140b4456ff44153705b3ec38b997dfb7b9c45e13732fb655760ad3e6b2", "sha256_in_prefix": "6a3912140b4456ff44153705b3ec38b997dfb7b9c45e13732fb655760ad3e6b2", "size_in_bytes": 14004}, {"_path": "site-packages/pip/_vendor/rich/live.py", "path_type": "hardlink", "sha256": "0e1cc03c49e34f142eabdff4636c61d8c53041c3ff6863e47a72df2844ec9703", "sha256_in_prefix": "0e1cc03c49e34f142eabdff4636c61d8c53041c3ff6863e47a72df2844ec9703", "size_in_bytes": 14270}, {"_path": "site-packages/pip/_vendor/rich/live_render.py", "path_type": "hardlink", "sha256": "cc9b41e3bd631b3881b44c31739e31d76c0442d1f806e42bd5203cbfd914f36c", "sha256_in_prefix": "cc9b41e3bd631b3881b44c31739e31d76c0442d1f806e42bd5203cbfd914f36c", "size_in_bytes": 3666}, {"_path": "site-packages/pip/_vendor/rich/logging.py", "path_type": "hardlink", "sha256": "660a4a30c058fc1b8c008fc1633a3e52d5da93ab79a07f552bc9bd4362e6d1fe", "sha256_in_prefix": "660a4a30c058fc1b8c008fc1633a3e52d5da93ab79a07f552bc9bd4362e6d1fe", "size_in_bytes": 12458}, {"_path": "site-packages/pip/_vendor/rich/markup.py", "path_type": "hardlink", "sha256": "ddeb8628fe6ce353424306928d39c9c6eb398993078f1a483345ba7c2c6b6b7f", "sha256_in_prefix": "ddeb8628fe6ce353424306928d39c9c6eb398993078f1a483345ba7c2c6b6b7f", "size_in_bytes": 8451}, {"_path": "site-packages/pip/_vendor/rich/measure.py", "path_type": "hardlink", "sha256": "1e6ac8257f2c5914c76e087c33111acbff37564a8d5bfef4b3c68a3f965c608f", "sha256_in_prefix": "1e6ac8257f2c5914c76e087c33111acbff37564a8d5bfef4b3c68a3f965c608f", "size_in_bytes": 5305}, {"_path": "site-packages/pip/_vendor/rich/padding.py", "path_type": "hardlink", "sha256": "295108ded3b0a3db202b560d4ae1fffccd7f8d45a62d9c11555fca98eb55cf23", "sha256_in_prefix": "295108ded3b0a3db202b560d4ae1fffccd7f8d45a62d9c11555fca98eb55cf23", "size_in_bytes": 4908}, {"_path": "site-packages/pip/_vendor/rich/pager.py", "path_type": "hardlink", "sha256": "48efc44c114a6e0de7fc080ecd79b8d52bf7e98c57032237fd1f8a398dbfb927", "sha256_in_prefix": "48efc44c114a6e0de7fc080ecd79b8d52bf7e98c57032237fd1f8a398dbfb927", "size_in_bytes": 828}, {"_path": "site-packages/pip/_vendor/rich/palette.py", "path_type": "hardlink", "sha256": "9489ef4753830d3d9fdd464c7cbd60aeaedd63fa4374a1f0e1b75480e19a3386", "sha256_in_prefix": "9489ef4753830d3d9fdd464c7cbd60aeaedd63fa4374a1f0e1b75480e19a3386", "size_in_bytes": 3396}, {"_path": "site-packages/pip/_vendor/rich/panel.py", "path_type": "hardlink", "sha256": "4940da6b7cf8314eef223cef6e2d125ee73a06c943cc00f0758d405f84db4dd6", "sha256_in_prefix": "4940da6b7cf8314eef223cef6e2d125ee73a06c943cc00f0758d405f84db4dd6", "size_in_bytes": 11225}, {"_path": "site-packages/pip/_vendor/rich/pretty.py", "path_type": "hardlink", "sha256": "832dd2ef6bb8151836cada28ecdd590d60c8bc1e2e9dbcdde625067609bef1f7", "sha256_in_prefix": "832dd2ef6bb8151836cada28ecdd590d60c8bc1e2e9dbcdde625067609bef1f7", "size_in_bytes": 36391}, {"_path": "site-packages/pip/_vendor/rich/progress.py", "path_type": "hardlink", "sha256": "32d9828d3939cd853f5ed447c511d30041c6ea117d3de17b10c59b10f95e202d", "sha256_in_prefix": "32d9828d3939cd853f5ed447c511d30041c6ea117d3de17b10c59b10f95e202d", "size_in_bytes": 60357}, {"_path": "site-packages/pip/_vendor/rich/progress_bar.py", "path_type": "hardlink", "sha256": "9994cfa4953071f71d8100934f3de4c98f9f73bf5d74bc2dc7a1a18717e8d3ae", "sha256_in_prefix": "9994cfa4953071f71d8100934f3de4c98f9f73bf5d74bc2dc7a1a18717e8d3ae", "size_in_bytes": 8162}, {"_path": "site-packages/pip/_vendor/rich/prompt.py", "path_type": "hardlink", "sha256": "974461414fb45154d5f5ed3cc56d416c88f426ad885f20a15f8942d2514dcede", "sha256_in_prefix": "974461414fb45154d5f5ed3cc56d416c88f426ad885f20a15f8942d2514dcede", "size_in_bytes": 12447}, {"_path": "site-packages/pip/_vendor/rich/protocol.py", "path_type": "hardlink", "sha256": "e611c70c3347724764f22587e7311b8becee215485e616d4da3228e3b47b9531", "sha256_in_prefix": "e611c70c3347724764f22587e7311b8becee215485e616d4da3228e3b47b9531", "size_in_bytes": 1391}, {"_path": "site-packages/pip/_vendor/rich/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/rich/region.py", "path_type": "hardlink", "sha256": "acd4fdc59ad56536085d90b43589f8d42250c1835b47e29e70f3b14e042f07c6", "sha256_in_prefix": "acd4fdc59ad56536085d90b43589f8d42250c1835b47e29e70f3b14e042f07c6", "size_in_bytes": 166}, {"_path": "site-packages/pip/_vendor/rich/repr.py", "path_type": "hardlink", "sha256": "e4c64966638d802ea4b9df905befe6d68917c0bd9a47abbacbea54714089cf6f", "sha256_in_prefix": "e4c64966638d802ea4b9df905befe6d68917c0bd9a47abbacbea54714089cf6f", "size_in_bytes": 4431}, {"_path": "site-packages/pip/_vendor/rich/rule.py", "path_type": "hardlink", "sha256": "d1f35a4bf68445add43117374f958ca4dfecba6b43c5f6a8af6cb7a1fd5fb419", "sha256_in_prefix": "d1f35a4bf68445add43117374f958ca4dfecba6b43c5f6a8af6cb7a1fd5fb419", "size_in_bytes": 4602}, {"_path": "site-packages/pip/_vendor/rich/scope.py", "path_type": "hardlink", "sha256": "4cc514f2aa35eed872a9008faa30cb62983f514d64e6a55df96c2226f9c955ab", "sha256_in_prefix": "4cc514f2aa35eed872a9008faa30cb62983f514d64e6a55df96c2226f9c955ab", "size_in_bytes": 2843}, {"_path": "site-packages/pip/_vendor/rich/screen.py", "path_type": "hardlink", "sha256": "628791784494871ef882ba9bd264926fd960861cac5a6147621b1b3154235cef", "sha256_in_prefix": "628791784494871ef882ba9bd264926fd960861cac5a6147621b1b3154235cef", "size_in_bytes": 1591}, {"_path": "site-packages/pip/_vendor/rich/segment.py", "path_type": "hardlink", "sha256": "a2d9ca78a18457e591950568b1f2557850dc0f100a1e9bc9fe12f34aee65ba63", "sha256_in_prefix": "a2d9ca78a18457e591950568b1f2557850dc0f100a1e9bc9fe12f34aee65ba63", "size_in_bytes": 24743}, {"_path": "site-packages/pip/_vendor/rich/spinner.py", "path_type": "hardlink", "sha256": "3d3e6a8173c6dd9a6a463ee7dc4650e8d5b9ea6c7795d66a64253b804332664e", "sha256_in_prefix": "3d3e6a8173c6dd9a6a463ee7dc4650e8d5b9ea6c7795d66a64253b804332664e", "size_in_bytes": 4364}, {"_path": "site-packages/pip/_vendor/rich/status.py", "path_type": "hardlink", "sha256": "9243e987761e019068f97fb8c0fa7c813a99c94e3ae8d2f06410383d94d37b0a", "sha256_in_prefix": "9243e987761e019068f97fb8c0fa7c813a99c94e3ae8d2f06410383d94d37b0a", "size_in_bytes": 4424}, {"_path": "site-packages/pip/_vendor/rich/style.py", "path_type": "hardlink", "sha256": "c698f8b8c05932db8db8da267d489a9a2825de9d6c0ef4c2670ac6d6d71355e8", "sha256_in_prefix": "c698f8b8c05932db8db8da267d489a9a2825de9d6c0ef4c2670ac6d6d71355e8", "size_in_bytes": 27059}, {"_path": "site-packages/pip/_vendor/rich/styled.py", "path_type": "hardlink", "sha256": "799367cc6ac8e248bfe78a606373a3d13fb1de5c5d5d3621e3faf20c1db8c015", "sha256_in_prefix": "799367cc6ac8e248bfe78a606373a3d13fb1de5c5d5d3621e3faf20c1db8c015", "size_in_bytes": 1258}, {"_path": "site-packages/pip/_vendor/rich/syntax.py", "path_type": "hardlink", "sha256": "aaa0271146782b9ecfa3cd7fe510719ecb94e0a47349dbc33c084ac3c99aff71", "sha256_in_prefix": "aaa0271146782b9ecfa3cd7fe510719ecb94e0a47349dbc33c084ac3c99aff71", "size_in_bytes": 35763}, {"_path": "site-packages/pip/_vendor/rich/table.py", "path_type": "hardlink", "sha256": "6664fb57b30c08e60ac3b4c663d4992f26037fa25d33e5957f4ec5755b958532", "sha256_in_prefix": "6664fb57b30c08e60ac3b4c663d4992f26037fa25d33e5957f4ec5755b958532", "size_in_bytes": 40049}, {"_path": "site-packages/pip/_vendor/rich/terminal_theme.py", "path_type": "hardlink", "sha256": "d63e7eb9f25f9ef940a3942c8bf0026625c39b0317cea826141c8e6d3f7ec896", "sha256_in_prefix": "d63e7eb9f25f9ef940a3942c8bf0026625c39b0317cea826141c8e6d3f7ec896", "size_in_bytes": 3370}, {"_path": "site-packages/pip/_vendor/rich/text.py", "path_type": "hardlink", "sha256": "00eec93c2cfafa068dd6d8552d73019ed1260cf55816014d1b5a0ceb5fec6a75", "sha256_in_prefix": "00eec93c2cfafa068dd6d8552d73019ed1260cf55816014d1b5a0ceb5fec6a75", "size_in_bytes": 47552}, {"_path": "site-packages/pip/_vendor/rich/theme.py", "path_type": "hardlink", "sha256": "a0dca15e119a82d0e56c3c9eded56eddeb16396934bcd92ec45c3efee9e568ad", "sha256_in_prefix": "a0dca15e119a82d0e56c3c9eded56eddeb16396934bcd92ec45c3efee9e568ad", "size_in_bytes": 3771}, {"_path": "site-packages/pip/_vendor/rich/themes.py", "path_type": "hardlink", "sha256": "d318132e8cdf69b79b62d709b43742e50917e4855411abe2a83509261e185459", "sha256_in_prefix": "d318132e8cdf69b79b62d709b43742e50917e4855411abe2a83509261e185459", "size_in_bytes": 102}, {"_path": "site-packages/pip/_vendor/rich/traceback.py", "path_type": "hardlink", "sha256": "640f10ebb0f23f96bfb2da4887a18f7fd2225e3fecddd0210c8afa65b7e46a19", "sha256_in_prefix": "640f10ebb0f23f96bfb2da4887a18f7fd2225e3fecddd0210c8afa65b7e46a19", "size_in_bytes": 35170}, {"_path": "site-packages/pip/_vendor/rich/tree.py", "path_type": "hardlink", "sha256": "c969d0eab02f446277a991aa06bc52d925b64ca05336b3f449d63c4313853eec", "sha256_in_prefix": "c969d0eab02f446277a991aa06bc52d925b64ca05336b3f449d63c4313853eec", "size_in_bytes": 9451}, {"_path": "site-packages/pip/_vendor/tomli/__init__.py", "path_type": "hardlink", "sha256": "3e1370fdec8b81d9fb31c27a9eb00df32226ddd5c2ef9bebd6c546555c034a90", "sha256_in_prefix": "3e1370fdec8b81d9fb31c27a9eb00df32226ddd5c2ef9bebd6c546555c034a90", "size_in_bytes": 314}, {"_path": "site-packages/pip/_vendor/tomli/_parser.py", "path_type": "hardlink", "sha256": "f70f0b1b48c1edfc26659581d2f5576de7a30c7725e00348271076b1c1270e50", "sha256_in_prefix": "f70f0b1b48c1edfc26659581d2f5576de7a30c7725e00348271076b1c1270e50", "size_in_bytes": 25591}, {"_path": "site-packages/pip/_vendor/tomli/_re.py", "path_type": "hardlink", "sha256": "b21e2c0434603bde0a259c0d22b81d73257fa906acb79d18bf3380506a510ca0", "sha256_in_prefix": "b21e2c0434603bde0a259c0d22b81d73257fa906acb79d18bf3380506a510ca0", "size_in_bytes": 3171}, {"_path": "site-packages/pip/_vendor/tomli/_types.py", "path_type": "hardlink", "sha256": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "sha256_in_prefix": "f864c6d9552a929c7032ace654ee05ef26ca75d21b027b801d77e65907138b74", "size_in_bytes": 254}, {"_path": "site-packages/pip/_vendor/tomli/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "sha256_in_prefix": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}, {"_path": "site-packages/pip/_vendor/tomli_w/__init__.py", "path_type": "hardlink", "sha256": "d05f320ed5f1dd4ba7866f3be0aac070fefab2bb0cf7ccbb5b21d0c02ba565ba", "sha256_in_prefix": "d05f320ed5f1dd4ba7866f3be0aac070fefab2bb0cf7ccbb5b21d0c02ba565ba", "size_in_bytes": 169}, {"_path": "site-packages/pip/_vendor/tomli_w/_writer.py", "path_type": "hardlink", "sha256": "76c89f152db161fd62efa9a64727f3f72d76e710bb67f1d0f38e592a126c617b", "sha256_in_prefix": "76c89f152db161fd62efa9a64727f3f72d76e710bb67f1d0f38e592a126c617b", "size_in_bytes": 6961}, {"_path": "site-packages/pip/_vendor/tomli_w/py.typed", "path_type": "hardlink", "sha256": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "sha256_in_prefix": "f0f8f2675695a10a5156fb7bd66bafbaae6a13e8d315990af862c792175e6e67", "size_in_bytes": 26}, {"_path": "site-packages/pip/_vendor/truststore/__init__.py", "path_type": "hardlink", "sha256": "db04525628e34733cb5575335aabdd64b36c10e8437e8a4a2ddd8428060bc0a5", "sha256_in_prefix": "db04525628e34733cb5575335aabdd64b36c10e8437e8a4a2ddd8428060bc0a5", "size_in_bytes": 1320}, {"_path": "site-packages/pip/_vendor/truststore/_api.py", "path_type": "hardlink", "sha256": "e34234a233b60e7213887bce9d462f2756df40c04a1ce91c8b5e27a0dc449c2b", "sha256_in_prefix": "e34234a233b60e7213887bce9d462f2756df40c04a1ce91c8b5e27a0dc449c2b", "size_in_bytes": 11246}, {"_path": "site-packages/pip/_vendor/truststore/_macos.py", "path_type": "hardlink", "sha256": "9d994b90e9accd413483aaf2470055198e423b33f2b9d72c889b4359aacce4b4", "sha256_in_prefix": "9d994b90e9accd413483aaf2470055198e423b33f2b9d72c889b4359aacce4b4", "size_in_bytes": 20503}, {"_path": "site-packages/pip/_vendor/truststore/_openssl.py", "path_type": "hardlink", "sha256": "2cb519ed919a8a8fa2e5da4a2a328249e4ae7e69fa4fca62f650dc167bd2caad", "sha256_in_prefix": "2cb519ed919a8a8fa2e5da4a2a328249e4ae7e69fa4fca62f650dc167bd2caad", "size_in_bytes": 2324}, {"_path": "site-packages/pip/_vendor/truststore/_ssl_constants.py", "path_type": "hardlink", "sha256": "3540f87d529d483d36ae2efe75bd2d9ced15a8b3fd687bb3992b5c5bbb40974f", "sha256_in_prefix": "3540f87d529d483d36ae2efe75bd2d9ced15a8b3fd687bb3992b5c5bbb40974f", "size_in_bytes": 1130}, {"_path": "site-packages/pip/_vendor/truststore/_windows.py", "path_type": "hardlink", "sha256": "ac01f22980fc33bb7e6d77c6f1580e55add3a5f85585bb78ad94253b8e58b8ff", "sha256_in_prefix": "ac01f22980fc33bb7e6d77c6f1580e55add3a5f85585bb78ad94253b8e58b8ff", "size_in_bytes": 17993}, {"_path": "site-packages/pip/_vendor/truststore/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/typing_extensions.py", "path_type": "hardlink", "sha256": "8a928750fb590aa2fa73e1dfbc697ef6dd28a6c5dc48bef6cb81988f2257b3f8", "sha256_in_prefix": "8a928750fb590aa2fa73e1dfbc697ef6dd28a6c5dc48bef6cb81988f2257b3f8", "size_in_bytes": 172702}, {"_path": "site-packages/pip/_vendor/urllib3/__init__.py", "path_type": "hardlink", "sha256": "8972dc6222724a7d0635b58e3990c30298012f52603f8e0467c8b5efad12f0c7", "sha256_in_prefix": "8972dc6222724a7d0635b58e3990c30298012f52603f8e0467c8b5efad12f0c7", "size_in_bytes": 3333}, {"_path": "site-packages/pip/_vendor/urllib3/_collections.py", "path_type": "hardlink", "sha256": "a72012249856ef074ea6a263f50240f05c8645fafc13cb94521a94be1174ef6f", "sha256_in_prefix": "a72012249856ef074ea6a263f50240f05c8645fafc13cb94521a94be1174ef6f", "size_in_bytes": 11372}, {"_path": "site-packages/pip/_vendor/urllib3/_version.py", "path_type": "hardlink", "sha256": "b7dc0607aa283935d782263ae8ad66e81652d422725c7014f04a160d37ba4a19", "sha256_in_prefix": "b7dc0607aa283935d782263ae8ad66e81652d422725c7014f04a160d37ba4a19", "size_in_bytes": 64}, {"_path": "site-packages/pip/_vendor/urllib3/connection.py", "path_type": "hardlink", "sha256": "b6d200f74f41adb4d4cf092a11efd3cd9561e0938e8fb83ad58b1e8b69abc068", "sha256_in_prefix": "b6d200f74f41adb4d4cf092a11efd3cd9561e0938e8fb83ad58b1e8b69abc068", "size_in_bytes": 20314}, {"_path": "site-packages/pip/_vendor/urllib3/connectionpool.py", "path_type": "hardlink", "sha256": "7b67a203035b14d08ac63e1bc0328d2bec3b1c8752cf73a633153f4c8b7e7af4", "sha256_in_prefix": "7b67a203035b14d08ac63e1bc0328d2bec3b1c8752cf73a633153f4c8b7e7af4", "size_in_bytes": 40408}, {"_path": "site-packages/pip/_vendor/urllib3/contrib/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/urllib3/contrib/_appengine_environ.py", "path_type": "hardlink", "sha256": "6c36f2384856d8228b25c42a00a032ac41cdf9a925b321c52aaeaf17c645b269", "sha256_in_prefix": "6c36f2384856d8228b25c42a00a032ac41cdf9a925b321c52aaeaf17c645b269", "size_in_bytes": 957}, {"_path": "site-packages/pip/_vendor/urllib3/contrib/_securetransport/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/urllib3/contrib/_securetransport/bindings.py", "path_type": "hardlink", "sha256": "e1793ae2a2243c1b74f40e6af9120552e0e135cf665e29556a99bb5a7627cd1c", "sha256_in_prefix": "e1793ae2a2243c1b74f40e6af9120552e0e135cf665e29556a99bb5a7627cd1c", "size_in_bytes": 17632}, {"_path": "site-packages/pip/_vendor/urllib3/contrib/_securetransport/low_level.py", "path_type": "hardlink", "sha256": "076241076fcd44fd36c4ae8309ad4f6bd22ec6b3f0c730f365b8b14246fb53d3", "sha256_in_prefix": "076241076fcd44fd36c4ae8309ad4f6bd22ec6b3f0c730f365b8b14246fb53d3", "size_in_bytes": 13922}, {"_path": "site-packages/pip/_vendor/urllib3/contrib/appengine.py", "path_type": "hardlink", "sha256": "551ebc780544d77ee5c53823043c029dae5488165338a6b4d408fffb905a0b3e", "sha256_in_prefix": "551ebc780544d77ee5c53823043c029dae5488165338a6b4d408fffb905a0b3e", "size_in_bytes": 11036}, {"_path": "site-packages/pip/_vendor/urllib3/contrib/ntlmpool.py", "path_type": "hardlink", "sha256": "3657e45bb58c756f338aab9da298c7a16dbdf688350535a2d0878889baae1709", "sha256_in_prefix": "3657e45bb58c756f338aab9da298c7a16dbdf688350535a2d0878889baae1709", "size_in_bytes": 4528}, {"_path": "site-packages/pip/_vendor/urllib3/contrib/pyopenssl.py", "path_type": "hardlink", "sha256": "843261e0c87263fa7ea0a9457187106954110efe86326046b96f728f1c9e7a33", "sha256_in_prefix": "843261e0c87263fa7ea0a9457187106954110efe86326046b96f728f1c9e7a33", "size_in_bytes": 17081}, {"_path": "site-packages/pip/_vendor/urllib3/contrib/securetransport.py", "path_type": "hardlink", "sha256": "15e7f5208514147aa97afcd78833db20690329c858d8554a79578b191d50ab78", "sha256_in_prefix": "15e7f5208514147aa97afcd78833db20690329c858d8554a79578b191d50ab78", "size_in_bytes": 34446}, {"_path": "site-packages/pip/_vendor/urllib3/contrib/socks.py", "path_type": "hardlink", "sha256": "6918bd7965e8f5911bf795d4c5e7f8676d421659e78db122028f473ac7a832de", "sha256_in_prefix": "6918bd7965e8f5911bf795d4c5e7f8676d421659e78db122028f473ac7a832de", "size_in_bytes": 7097}, {"_path": "site-packages/pip/_vendor/urllib3/exceptions.py", "path_type": "hardlink", "sha256": "d0c9e7a372874cd7d745f63beb7f0db9f38f9146fa9973a6f8baa3fb8c76c3c0", "sha256_in_prefix": "d0c9e7a372874cd7d745f63beb7f0db9f38f9146fa9973a6f8baa3fb8c76c3c0", "size_in_bytes": 8217}, {"_path": "site-packages/pip/_vendor/urllib3/fields.py", "path_type": "hardlink", "sha256": "92f2c30a0fc9987d652e3514118fc52d2f14858ee106f0cfb951136d8f2676b3", "sha256_in_prefix": "92f2c30a0fc9987d652e3514118fc52d2f14858ee106f0cfb951136d8f2676b3", "size_in_bytes": 8579}, {"_path": "site-packages/pip/_vendor/urllib3/filepost.py", "path_type": "hardlink", "sha256": "e5bfeaaa04475652fbb8bb5d018073061f861e653901f255b7fd8dd174b73de6", "sha256_in_prefix": "e5bfeaaa04475652fbb8bb5d018073061f861e653901f255b7fd8dd174b73de6", "size_in_bytes": 2440}, {"_path": "site-packages/pip/_vendor/urllib3/packages/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/urllib3/packages/backports/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pip/_vendor/urllib3/packages/backports/makefile.py", "path_type": "hardlink", "sha256": "9dbcedde2d1a80f54fd3b8eaaa08e16988cc9ae022fd6e44d04cb0662bd53bc1", "sha256_in_prefix": "9dbcedde2d1a80f54fd3b8eaaa08e16988cc9ae022fd6e44d04cb0662bd53bc1", "size_in_bytes": 1417}, {"_path": "site-packages/pip/_vendor/urllib3/packages/backports/weakref_finalize.py", "path_type": "hardlink", "sha256": "b5109a97938084d491c9bd03847a7edfc02d2250ac44ff01c45dcd5feeaba880", "sha256_in_prefix": "b5109a97938084d491c9bd03847a7edfc02d2250ac44ff01c45dcd5feeaba880", "size_in_bytes": 5343}, {"_path": "site-packages/pip/_vendor/urllib3/packages/six.py", "path_type": "hardlink", "sha256": "6fd2ccd30057bfb13b4ab6c28c09b8c3037e86b1fe88dc6fd7c2e058d30c28fa", "sha256_in_prefix": "6fd2ccd30057bfb13b4ab6c28c09b8c3037e86b1fe88dc6fd7c2e058d30c28fa", "size_in_bytes": 34665}, {"_path": "site-packages/pip/_vendor/urllib3/poolmanager.py", "path_type": "hardlink", "sha256": "696ca15d1b4d3b82549c249556a29329077c1174ef526d5537da60b366dc38da", "sha256_in_prefix": "696ca15d1b4d3b82549c249556a29329077c1174ef526d5537da60b366dc38da", "size_in_bytes": 19990}, {"_path": "site-packages/pip/_vendor/urllib3/request.py", "path_type": "hardlink", "sha256": "61358536bed023087b1355bd75d7bd2ccefbbf65564c9e55efc5ee4d3c3b0f50", "sha256_in_prefix": "61358536bed023087b1355bd75d7bd2ccefbbf65564c9e55efc5ee4d3c3b0f50", "size_in_bytes": 6691}, {"_path": "site-packages/pip/_vendor/urllib3/response.py", "path_type": "hardlink", "sha256": "7e60c9005906ef5b854e7fac5524e1d88c345a6717418aa46d18e286fc018d4f", "sha256_in_prefix": "7e60c9005906ef5b854e7fac5524e1d88c345a6717418aa46d18e286fc018d4f", "size_in_bytes": 30641}, {"_path": "site-packages/pip/_vendor/urllib3/util/__init__.py", "path_type": "hardlink", "sha256": "2449929a6aaa2f26b0f0fe75814226661f06c20f62d7349ef83a2a022b67da77", "sha256_in_prefix": "2449929a6aaa2f26b0f0fe75814226661f06c20f62d7349ef83a2a022b67da77", "size_in_bytes": 1155}, {"_path": "site-packages/pip/_vendor/urllib3/util/connection.py", "path_type": "hardlink", "sha256": "e4bc760753d6dbd2b1067d93d3190dd420604416b780654904aa10a11a201159", "sha256_in_prefix": "e4bc760753d6dbd2b1067d93d3190dd420604416b780654904aa10a11a201159", "size_in_bytes": 4901}, {"_path": "site-packages/pip/_vendor/urllib3/util/proxy.py", "path_type": "hardlink", "sha256": "cd4bcf3c226ba7a74e17437818055b39c97aa3ee2e5ca4ab1a24e492be6f512e", "sha256_in_prefix": "cd4bcf3c226ba7a74e17437818055b39c97aa3ee2e5ca4ab1a24e492be6f512e", "size_in_bytes": 1605}, {"_path": "site-packages/pip/_vendor/urllib3/util/queue.py", "path_type": "hardlink", "sha256": "9d1817f3f797fbf564bf1a17d3de905a8cfc3ecd101d4004c482c263fecf9dc3", "sha256_in_prefix": "9d1817f3f797fbf564bf1a17d3de905a8cfc3ecd101d4004c482c263fecf9dc3", "size_in_bytes": 498}, {"_path": "site-packages/pip/_vendor/urllib3/util/request.py", "path_type": "hardlink", "sha256": "0b4394b76b5c53a2d189027b61834ff46bcfad2be5ef388805e910fb99e50599", "sha256_in_prefix": "0b4394b76b5c53a2d189027b61834ff46bcfad2be5ef388805e910fb99e50599", "size_in_bytes": 3997}, {"_path": "site-packages/pip/_vendor/urllib3/util/response.py", "path_type": "hardlink", "sha256": "189a60dc4822f6a6895d1c01879c2ff8c36e4566a7e4122ee34a117a8c563f6f", "sha256_in_prefix": "189a60dc4822f6a6895d1c01879c2ff8c36e4566a7e4122ee34a117a8c563f6f", "size_in_bytes": 3510}, {"_path": "site-packages/pip/_vendor/urllib3/util/retry.py", "path_type": "hardlink", "sha256": "e8436f399f0f043ce1f24822c69aa5f6522b6f67711fe93b66605a9c9176360e", "sha256_in_prefix": "e8436f399f0f043ce1f24822c69aa5f6522b6f67711fe93b66605a9c9176360e", "size_in_bytes": 22050}, {"_path": "site-packages/pip/_vendor/urllib3/util/ssl_.py", "path_type": "hardlink", "sha256": "403bae4f13d20a3d6b62d678c690fb531fabdb44c3e74687caa2b2850ec1ab80", "sha256_in_prefix": "403bae4f13d20a3d6b62d678c690fb531fabdb44c3e74687caa2b2850ec1ab80", "size_in_bytes": 17460}, {"_path": "site-packages/pip/_vendor/urllib3/util/ssl_match_hostname.py", "path_type": "hardlink", "sha256": "22be1c65512398093c8140081d64a2ef0b4e3bcdd4098001636c450f5425fd60", "sha256_in_prefix": "22be1c65512398093c8140081d64a2ef0b4e3bcdd4098001636c450f5425fd60", "size_in_bytes": 5758}, {"_path": "site-packages/pip/_vendor/urllib3/util/ssltransport.py", "path_type": "hardlink", "sha256": "340faee6b313ac3143142f10cd129410a306d39eb584e0f8a814ebdd9e29bfa1", "sha256_in_prefix": "340faee6b313ac3143142f10cd129410a306d39eb584e0f8a814ebdd9e29bfa1", "size_in_bytes": 6895}, {"_path": "site-packages/pip/_vendor/urllib3/util/timeout.py", "path_type": "hardlink", "sha256": "730ab874c93cee624748192d2b59a2609fbce46fb74f74664f6d2fed2142a67a", "sha256_in_prefix": "730ab874c93cee624748192d2b59a2609fbce46fb74f74664f6d2fed2142a67a", "size_in_bytes": 10168}, {"_path": "site-packages/pip/_vendor/urllib3/util/url.py", "path_type": "hardlink", "sha256": "942004ecce66c80f040dd5b4b09bb2c9985507d2bf8f7f258d684702715a5a81", "sha256_in_prefix": "942004ecce66c80f040dd5b4b09bb2c9985507d2bf8f7f258d684702715a5a81", "size_in_bytes": 14296}, {"_path": "site-packages/pip/_vendor/urllib3/util/wait.py", "path_type": "hardlink", "sha256": "7ce5f4fdf6a8cc6d8fee25688d0a04d666f277078dc93726fa15c47c5ad3b4b2", "sha256_in_prefix": "7ce5f4fdf6a8cc6d8fee25688d0a04d666f277078dc93726fa15c47c5ad3b4b2", "size_in_bytes": 5403}, {"_path": "site-packages/pip/_vendor/vendor.txt", "path_type": "hardlink", "sha256": "ce640e3e1b237f1534728bce06bd9921c48e1670700b50eef2a89046b7ccaf0f", "sha256_in_prefix": "ce640e3e1b237f1534728bce06bd9921c48e1670700b50eef2a89046b7ccaf0f", "size_in_bytes": 373}, {"_path": "site-packages/pip/py.typed", "path_type": "hardlink", "sha256": "10156fbcf4539ff788a73e5ee50ced48276b317ed0c1ded53fddd14a82256762", "sha256_in_prefix": "10156fbcf4539ff788a73e5ee50ced48276b317ed0c1ded53fddd14a82256762", "size_in_bytes": 286}, {"_path": "Lib/site-packages/pip/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/__pycache__/__pip-runner__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/__pycache__/build_env.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/__pycache__/cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/cli/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/cli/__pycache__/autocompletion.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/cli/__pycache__/base_command.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/cli/__pycache__/cmdoptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/cli/__pycache__/command_context.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/cli/__pycache__/index_command.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/cli/__pycache__/main.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/cli/__pycache__/main_parser.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/cli/__pycache__/parser.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/cli/__pycache__/progress_bars.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/cli/__pycache__/req_command.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/cli/__pycache__/spinners.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/cli/__pycache__/status_codes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/check.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/completion.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/configuration.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/debug.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/download.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/freeze.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/hash.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/help.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/index.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/inspect.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/install.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/list.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/lock.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/search.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/show.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/uninstall.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/commands/__pycache__/wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/__pycache__/configuration.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/distributions/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/distributions/__pycache__/base.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/distributions/__pycache__/installed.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/distributions/__pycache__/sdist.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/distributions/__pycache__/wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/__pycache__/exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/index/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/index/__pycache__/collector.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/index/__pycache__/package_finder.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/index/__pycache__/sources.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/locations/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/locations/__pycache__/_distutils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/locations/__pycache__/_sysconfig.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/locations/__pycache__/base.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/__pycache__/main.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/metadata/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/metadata/__pycache__/_json.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/metadata/__pycache__/base.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/metadata/importlib/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/metadata/importlib/__pycache__/_compat.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/metadata/importlib/__pycache__/_dists.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/metadata/importlib/__pycache__/_envs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/metadata/__pycache__/pkg_resources.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/models/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/models/__pycache__/candidate.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/models/__pycache__/direct_url.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/models/__pycache__/format_control.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/models/__pycache__/index.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/models/__pycache__/installation_report.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/models/__pycache__/link.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/models/__pycache__/pylock.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/models/__pycache__/scheme.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/models/__pycache__/search_scope.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/models/__pycache__/selection_prefs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/models/__pycache__/target_python.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/models/__pycache__/wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/network/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/network/__pycache__/auth.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/network/__pycache__/cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/network/__pycache__/download.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/network/__pycache__/lazy_wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/network/__pycache__/session.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/network/__pycache__/utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/network/__pycache__/xmlrpc.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/build/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/build/__pycache__/build_tracker.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/build/__pycache__/metadata.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/build/__pycache__/metadata_editable.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/build/__pycache__/metadata_legacy.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/build/__pycache__/wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/build/__pycache__/wheel_editable.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/build/__pycache__/wheel_legacy.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/__pycache__/check.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/__pycache__/freeze.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/install/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/install/__pycache__/editable_legacy.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/install/__pycache__/wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/operations/__pycache__/prepare.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/__pycache__/pyproject.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/req/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/req/__pycache__/constructors.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/req/__pycache__/req_dependency_group.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/req/__pycache__/req_file.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/req/__pycache__/req_install.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/req/__pycache__/req_set.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/req/__pycache__/req_uninstall.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/resolution/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/resolution/__pycache__/base.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/resolution/legacy/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/resolution/legacy/__pycache__/resolver.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/base.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/candidates.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/factory.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/found_candidates.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/provider.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/reporter.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/requirements.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/resolution/resolvelib/__pycache__/resolver.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/__pycache__/self_outdated_check.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/_jaraco_text.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/_log.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/appdirs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/compat.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/compatibility_tags.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/datetime.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/deprecation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/direct_url_helpers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/egg_link.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/entrypoints.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/filesystem.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/filetypes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/glibc.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/hashes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/logging.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/misc.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/packaging.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/retry.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/setuptools_build.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/subprocess.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/temp_dir.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/unpacking.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/urls.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/virtualenv.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/utils/__pycache__/wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/vcs/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/vcs/__pycache__/bazaar.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/vcs/__pycache__/git.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/vcs/__pycache__/mercurial.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/vcs/__pycache__/subversion.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/vcs/__pycache__/versioncontrol.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_internal/__pycache__/wheel_builder.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/_cmd.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/adapter.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/cachecontrol/caches/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/cachecontrol/caches/__pycache__/file_cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/cachecontrol/caches/__pycache__/redis_cache.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/controller.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/filewrapper.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/heuristics.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/serialize.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/cachecontrol/__pycache__/wrapper.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/certifi/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/certifi/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/certifi/__pycache__/core.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/dependency_groups/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/dependency_groups/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/dependency_groups/__pycache__/_implementation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/dependency_groups/__pycache__/_lint_dependency_groups.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/dependency_groups/__pycache__/_pip_wrapper.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/dependency_groups/__pycache__/_toml_compat.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distlib/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distlib/__pycache__/compat.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distlib/__pycache__/database.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distlib/__pycache__/index.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distlib/__pycache__/locators.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distlib/__pycache__/manifest.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distlib/__pycache__/markers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distlib/__pycache__/metadata.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distlib/__pycache__/resources.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distlib/__pycache__/scripts.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distlib/__pycache__/util.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distlib/__pycache__/version.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distlib/__pycache__/wheel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distro/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distro/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/distro/__pycache__/distro.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/idna/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/idna/__pycache__/codec.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/idna/__pycache__/compat.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/idna/__pycache__/core.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/idna/__pycache__/idnadata.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/idna/__pycache__/intranges.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/idna/__pycache__/package_data.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/idna/__pycache__/uts46data.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/msgpack/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/msgpack/__pycache__/exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/msgpack/__pycache__/ext.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/msgpack/__pycache__/fallback.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/_elffile.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/_manylinux.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/_musllinux.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/_parser.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/_structures.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/_tokenizer.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/licenses/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/licenses/__pycache__/_spdx.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/markers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/metadata.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/requirements.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/specifiers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/tags.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/packaging/__pycache__/version.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pkg_resources/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/android.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/api.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/macos.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/unix.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/version.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/platformdirs/__pycache__/windows.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/console.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/filter.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/filters/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/formatter.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/formatters/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/formatters/__pycache__/_mapping.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/lexer.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/lexers/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/lexers/__pycache__/_mapping.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/lexers/__pycache__/python.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/modeline.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/plugin.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/regexopt.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/scanner.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/sphinxext.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/style.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/styles/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/styles/__pycache__/_mapping.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/token.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/unistring.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pygments/__pycache__/util.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pyproject_hooks/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pyproject_hooks/__pycache__/_impl.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pyproject_hooks/_in_process/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/pyproject_hooks/_in_process/__pycache__/_in_process.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/__version__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/_internal_utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/adapters.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/api.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/auth.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/certs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/compat.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/cookies.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/help.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/hooks.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/models.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/packages.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/sessions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/status_codes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/structures.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/requests/__pycache__/utils.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/resolvelib/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/resolvelib/__pycache__/providers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/resolvelib/__pycache__/reporters.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/abstract.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/criterion.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/resolvelib/resolvers/__pycache__/resolution.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/resolvelib/__pycache__/structs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_cell_widths.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_emoji_codes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_emoji_replace.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_export_format.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_extension.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_fileno.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_inspect.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_log_render.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_loop.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_null_file.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_palettes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_pick.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_ratio.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_spinners.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_stack.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_timer.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_win32_console.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_windows.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_windows_renderer.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/_wrap.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/abc.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/align.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/ansi.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/bar.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/box.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/cells.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/color.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/color_triplet.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/columns.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/console.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/constrain.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/containers.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/control.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/default_styles.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/diagnose.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/emoji.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/errors.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/file_proxy.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/filesize.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/highlighter.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/json.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/jupyter.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/layout.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/live.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/live_render.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/logging.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/markup.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/measure.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/padding.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/pager.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/palette.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/panel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/pretty.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/progress.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/progress_bar.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/prompt.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/protocol.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/region.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/repr.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/rule.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/scope.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/screen.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/segment.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/spinner.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/status.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/style.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/styled.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/syntax.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/table.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/terminal_theme.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/text.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/theme.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/themes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/traceback.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/rich/__pycache__/tree.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/tomli/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/tomli/__pycache__/_parser.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/tomli/__pycache__/_re.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/tomli/__pycache__/_types.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/tomli_w/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/tomli_w/__pycache__/_writer.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/truststore/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/truststore/__pycache__/_api.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/truststore/__pycache__/_macos.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/truststore/__pycache__/_openssl.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/truststore/__pycache__/_ssl_constants.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/truststore/__pycache__/_windows.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/__pycache__/typing_extensions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/__pycache__/_collections.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/__pycache__/_version.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/__pycache__/connection.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/__pycache__/connectionpool.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/_appengine_environ.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__pycache__/bindings.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/contrib/_securetransport/__pycache__/low_level.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/appengine.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/ntlmpool.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/pyopenssl.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/securetransport.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/contrib/__pycache__/socks.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/__pycache__/exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/__pycache__/fields.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/__pycache__/filepost.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/packages/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/makefile.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/packages/backports/__pycache__/weakref_finalize.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/packages/__pycache__/six.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/__pycache__/poolmanager.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/__pycache__/request.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/__pycache__/response.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/connection.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/proxy.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/queue.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/request.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/response.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/retry.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/ssl_.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/ssl_match_hostname.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/ssltransport.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/timeout.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/url.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/pip/_vendor/urllib3/util/__pycache__/wait.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Scripts/pip-script.py", "path_type": "windows_python_entry_point_script"}, {"_path": "Scripts/pip3-script.py", "path_type": "windows_python_entry_point_script"}, {"_path": "Scripts/pip.exe", "path_type": "windows_python_entry_point_exe"}, {"_path": "Scripts/pip3.exe", "path_type": "windows_python_entry_point_exe"}], "paths_version": 1}, "requested_spec": "None", "sha256": "dc705c852ed94395f0b75cf65aa0f230adfb74cda46c9daca6df94fca0b90bb7", "size": 1332980, "subdir": "noarch", "timestamp": 1746204037151, "track_features": "", "url": "https://repo.anaconda.com/pkgs/main/noarch/pip-25.1-pyhc872135_2.conda", "version": "25.1"}