# 迷宫密封-转子系统分析工具使用指南

## 快速开始

### 环境要求
- Python 3.7+
- NumPy
- SciPy
- Matplotlib

### 安装依赖
```bash
pip install numpy scipy matplotlib
```

### 基本使用

#### 1. 运行测试程序
```bash
python test_system.py
```
验证程序是否正常工作，输出基本功能测试结果。

#### 2. 运行演示分析
```bash
python demo_analysis.py
```
执行单个案例的详细分析，生成多种分析图表。

#### 3. 运行完整分析
```bash
python labyrinth_seal_rotor_system.py
```
复现论文中的所有分析，生成完整的图表集合。

#### 4. 查看项目总结
```bash
python summary.py
```
显示项目总结信息并生成对比分析图。

## 详细使用说明

### 1. 创建系统实例

```python
from labyrinth_seal_rotor_system import LabyrinthSealRotorSystem

# 创建系统实例
system = LabyrinthSealRotorSystem()

# 查看默认参数
print(f"密封半径: {system.Rs1*1000:.1f} mm")
print(f"径向间隙: {system.Cr*1000:.2f} mm")
print(f"轴向压降: {system.delta_p/1e6:.1f} MPa")
```

### 2. 修改系统参数

```python
# 修改轴向压降
system.delta_p = 0.4e6  # 0.4 MPa

# 修改密封间隙
system.Cr = 0.15e-3     # 0.15 mm

# 修改密封长度
system.L = 0.06         # 60 mm

# 修改转子参数
system.m = 120          # 转子质量 120 kg
system.Ke = 1.2e6       # 转子刚度 1.2 MN/m
```

### 3. 基本分析功能

#### 动态特性系数分析
```python
import numpy as np

# 定义转速范围
omega_range = np.linspace(100, 600, 50)

# 计算动态特性系数
K1, K2, D1, D2 = system.calculate_dynamic_coefficients(omega_range)

# 绘制系数随转速变化图
system.plot_dynamic_coefficients_vs_speed(omega_range)
```

#### 单个案例分析
```python
# 设置分析参数
omega = 340  # 转速 (rad/s)
t_span = 2.0  # 时间跨度 (s)
initial_conditions = [0.0001, 0.0001, 0, 0]  # 初始条件 [x, y, vx, vy]

# 求解系统
t, solution = system.solve_system(omega, t_span, initial_conditions)
x, y, vx, vy = solution.T

# 计算关键指标
displacement = np.sqrt(x**2 + y**2)
max_displacement = np.max(displacement)
print(f"最大位移: {max_displacement*1000:.4f} mm")
```

#### 密封力分析
```python
# 计算密封力
Fsx_list, Fsy_list = [], []
for i in range(len(t)):
    Fsx, Fsy = system.calculate_seal_force(x[i], y[i], vx[i], vy[i], 0, 0, omega, t[i])
    Fsx_list.append(Fsx)
    Fsy_list.append(Fsy)

print(f"最大水平密封力: {np.max(np.abs(Fsx_list)):.2f} N")
print(f"最大垂直密封力: {np.max(np.abs(Fsy_list)):.2f} N")
```

### 4. 参数影响分析

#### 轴向压降影响
```python
# 定义压降范围
pressure_drops = np.linspace(0.2e6, 0.8e6, 7)

# 分析压降影响
system.analyze_pressure_drop_effect(pressure_drops, omega=340)
```

#### 密封间隙影响
```python
# 定义间隙范围 (0.6-1.2 mm)
clearance_values = np.linspace(0.6e-3, 1.2e-3, 7)

# 分析间隙影响
system.analyze_clearance_effect(clearance_values, omega=340)
```

#### 密封长度影响
```python
# 定义长度范围 (30-70 mm)
length_values = np.linspace(0.03, 0.07, 5)

# 分析长度影响
system.analyze_length_effect(length_values, omega=340)
```

### 5. 非线性动力学分析

#### 分岔图分析
```python
# 定义转速范围
omega_range_bifurcation = np.linspace(200, 500, 30)

# 绘制分岔图
system.plot_bifurcation_diagram(omega_range_bifurcation, delta_p_values=[0.2e6, 0.6e6])
```

#### 频谱分析
```python
# 进行频谱分析
system.frequency_analysis(omega=340, delta_p_values=[0.2e6, 0.6e6], t_span=5.0)
```

#### 轨道图和时间历程
```python
# 定义多个转速
omega_values = [340, 580, 760, 1100]

# 绘制轨道图和时间历程
system.plot_orbit_and_time_history(omega_values, delta_p=0.6e6, t_span=2.0)
```

### 6. 自定义分析

#### 创建自定义分析函数
```python
def custom_analysis(system, omega_range, parameter_name, parameter_values):
    """自定义参数分析函数"""
    results = []
    
    # 保存原始参数值
    original_value = getattr(system, parameter_name)
    
    for param_value in parameter_values:
        # 设置新参数值
        setattr(system, parameter_name, param_value)
        
        # 进行分析
        max_displacements = []
        for omega in omega_range:
            t, solution = system.solve_system(omega, 1.0, [0.0001, 0.0001, 0, 0])
            x, y = solution.T[:2]
            displacement = np.sqrt(x**2 + y**2)
            max_displacements.append(np.max(displacement))
        
        results.append(max_displacements)
    
    # 恢复原始参数值
    setattr(system, parameter_name, original_value)
    
    return np.array(results)

# 使用示例
omega_range = np.linspace(200, 500, 20)
pressure_values = np.linspace(0.2e6, 0.8e6, 5)
results = custom_analysis(system, omega_range, 'delta_p', pressure_values)
```

## 高级功能

### 1. 批量分析
```python
def batch_analysis(parameter_sets):
    """批量分析多组参数"""
    results = {}
    
    for i, params in enumerate(parameter_sets):
        system = LabyrinthSealRotorSystem()
        
        # 设置参数
        for key, value in params.items():
            setattr(system, key, value)
        
        # 进行分析
        omega_range = np.linspace(100, 600, 50)
        K1, K2, D1, D2 = system.calculate_dynamic_coefficients(omega_range)
        
        results[f'case_{i}'] = {
            'params': params,
            'K1': K1,
            'K2': K2,
            'D1': D1,
            'D2': D2
        }
    
    return results

# 使用示例
parameter_sets = [
    {'delta_p': 0.2e6, 'Cr': 0.1e-3},
    {'delta_p': 0.4e6, 'Cr': 0.1e-3},
    {'delta_p': 0.6e6, 'Cr': 0.1e-3},
]

batch_results = batch_analysis(parameter_sets)
```

### 2. 结果导出
```python
import pandas as pd

def export_results(t, solution, filename):
    """导出结果到CSV文件"""
    x, y, vx, vy = solution.T
    
    data = {
        'time': t,
        'x': x,
        'y': y,
        'vx': vx,
        'vy': vy,
        'displacement': np.sqrt(x**2 + y**2)
    }
    
    df = pd.DataFrame(data)
    df.to_csv(filename, index=False)
    print(f"结果已导出到 {filename}")

# 使用示例
t, solution = system.solve_system(340, 2.0, [0.0001, 0.0001, 0, 0])
export_results(t, solution, 'analysis_results.csv')
```

## 故障排除

### 常见问题

#### 1. 中文字体显示问题
```python
# 解决方案1: 使用英文标签
plt.xlabel('Speed (rad/s)')
plt.ylabel('Displacement (mm)')

# 解决方案2: 配置中文字体
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
```

#### 2. 计算时间过长
```python
# 减少采样点数
omega_range = np.linspace(100, 600, 20)  # 从50减少到20

# 减少时间跨度
t_span = 1.0  # 从2.0减少到1.0

# 降低采样频率
def solve_system_fast(self, omega, t_span, initial_conditions):
    t = np.linspace(0, t_span, int(t_span * 500))  # 从1000Hz降到500Hz
    # ... 其余代码相同
```

#### 3. 数值不稳定
```python
# 检查参数合理性
if system.Cr <= 0:
    raise ValueError("径向间隙必须大于0")

if system.delta_p <= 0:
    raise ValueError("轴向压降必须大于0")

# 限制偏心率范围
eM = min(eM, 0.9)  # 避免接近1导致的数值问题
```

## 最佳实践

1. **参数设置**: 确保物理参数在合理范围内
2. **计算效率**: 根据需要调整采样频率和时间跨度
3. **结果验证**: 检查结果的物理合理性
4. **图表保存**: 使用高DPI设置保存高质量图片
5. **代码复用**: 将常用分析封装成函数

通过本指南，用户可以充分利用该工具进行迷宫密封-转子系统的各种分析研究。
