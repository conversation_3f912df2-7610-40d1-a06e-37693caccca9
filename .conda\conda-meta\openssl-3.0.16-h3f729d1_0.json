{"build": "h3f729d1_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["ca-certificates", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0"], "extracted_package_dir": "C:\\Users\\<USER>\\anaconda3\\pkgs\\openssl-3.0.16-h3f729d1_0", "features": "", "files": ["Library/bin/c_rehash.pl", "Library/bin/libcrypto-3-x64.dll", "Library/bin/libcrypto-3-x64.pdb", "Library/bin/libssl-3-x64.dll", "Library/bin/libssl-3-x64.pdb", "Library/bin/openssl.exe", "Library/bin/openssl.pdb", "Library/include/openssl/__DECC_INCLUDE_EPILOGUE.H", "Library/include/openssl/__DECC_INCLUDE_PROLOGUE.H", "Library/include/openssl/aes.h", "Library/include/openssl/applink.c", "Library/include/openssl/asn1.h", "Library/include/openssl/asn1_mac.h", "Library/include/openssl/asn1err.h", "Library/include/openssl/asn1t.h", "Library/include/openssl/async.h", "Library/include/openssl/asyncerr.h", "Library/include/openssl/bio.h", "Library/include/openssl/bioerr.h", "Library/include/openssl/blowfish.h", "Library/include/openssl/bn.h", "Library/include/openssl/bnerr.h", "Library/include/openssl/buffer.h", "Library/include/openssl/buffererr.h", "Library/include/openssl/camellia.h", "Library/include/openssl/cast.h", "Library/include/openssl/cmac.h", "Library/include/openssl/cmp.h", "Library/include/openssl/cmp_util.h", "Library/include/openssl/cmperr.h", "Library/include/openssl/cms.h", "Library/include/openssl/cmserr.h", "Library/include/openssl/comp.h", "Library/include/openssl/comperr.h", "Library/include/openssl/conf.h", "Library/include/openssl/conf_api.h", "Library/include/openssl/conferr.h", "Library/include/openssl/configuration.h", "Library/include/openssl/conftypes.h", "Library/include/openssl/core.h", "Library/include/openssl/core_dispatch.h", "Library/include/openssl/core_names.h", "Library/include/openssl/core_object.h", "Library/include/openssl/crmf.h", "Library/include/openssl/crmferr.h", "Library/include/openssl/crypto.h", "Library/include/openssl/cryptoerr.h", "Library/include/openssl/cryptoerr_legacy.h", "Library/include/openssl/ct.h", "Library/include/openssl/cterr.h", "Library/include/openssl/decoder.h", "Library/include/openssl/decodererr.h", "Library/include/openssl/des.h", "Library/include/openssl/dh.h", "Library/include/openssl/dherr.h", "Library/include/openssl/dsa.h", "Library/include/openssl/dsaerr.h", "Library/include/openssl/dtls1.h", "Library/include/openssl/e_os2.h", "Library/include/openssl/ebcdic.h", "Library/include/openssl/ec.h", "Library/include/openssl/ecdh.h", "Library/include/openssl/ecdsa.h", "Library/include/openssl/ecerr.h", "Library/include/openssl/encoder.h", "Library/include/openssl/encodererr.h", "Library/include/openssl/engine.h", "Library/include/openssl/engineerr.h", "Library/include/openssl/err.h", "Library/include/openssl/ess.h", "Library/include/openssl/esserr.h", "Library/include/openssl/evp.h", "Library/include/openssl/evperr.h", "Library/include/openssl/fips_names.h", "Library/include/openssl/fipskey.h", "Library/include/openssl/hmac.h", "Library/include/openssl/http.h", "Library/include/openssl/httperr.h", "Library/include/openssl/idea.h", "Library/include/openssl/kdf.h", "Library/include/openssl/kdferr.h", "Library/include/openssl/lhash.h", "Library/include/openssl/macros.h", "Library/include/openssl/md2.h", "Library/include/openssl/md4.h", "Library/include/openssl/md5.h", "Library/include/openssl/mdc2.h", "Library/include/openssl/modes.h", "Library/include/openssl/obj_mac.h", "Library/include/openssl/objects.h", "Library/include/openssl/objectserr.h", "Library/include/openssl/ocsp.h", "Library/include/openssl/ocsperr.h", "Library/include/openssl/opensslconf.h", "Library/include/openssl/opensslv.h", "Library/include/openssl/ossl_typ.h", "Library/include/openssl/param_build.h", "Library/include/openssl/params.h", "Library/include/openssl/pem.h", "Library/include/openssl/pem2.h", "Library/include/openssl/pemerr.h", "Library/include/openssl/pkcs12.h", "Library/include/openssl/pkcs12err.h", "Library/include/openssl/pkcs7.h", "Library/include/openssl/pkcs7err.h", "Library/include/openssl/prov_ssl.h", "Library/include/openssl/proverr.h", "Library/include/openssl/provider.h", "Library/include/openssl/rand.h", "Library/include/openssl/randerr.h", "Library/include/openssl/rc2.h", "Library/include/openssl/rc4.h", "Library/include/openssl/rc5.h", "Library/include/openssl/ripemd.h", "Library/include/openssl/rsa.h", "Library/include/openssl/rsaerr.h", "Library/include/openssl/safestack.h", "Library/include/openssl/seed.h", "Library/include/openssl/self_test.h", "Library/include/openssl/sha.h", "Library/include/openssl/srp.h", "Library/include/openssl/srtp.h", "Library/include/openssl/ssl.h", "Library/include/openssl/ssl2.h", "Library/include/openssl/ssl3.h", "Library/include/openssl/sslerr.h", "Library/include/openssl/sslerr_legacy.h", "Library/include/openssl/stack.h", "Library/include/openssl/store.h", "Library/include/openssl/storeerr.h", "Library/include/openssl/symhacks.h", "Library/include/openssl/tls1.h", "Library/include/openssl/trace.h", "Library/include/openssl/ts.h", "Library/include/openssl/tserr.h", "Library/include/openssl/txt_db.h", "Library/include/openssl/types.h", "Library/include/openssl/ui.h", "Library/include/openssl/uierr.h", "Library/include/openssl/whrlpool.h", "Library/include/openssl/x509.h", "Library/include/openssl/x509_vfy.h", "Library/include/openssl/x509err.h", "Library/include/openssl/x509v3.h", "Library/include/openssl/x509v3err.h", "Library/lib/libcrypto.lib", "Library/lib/libssl.lib", "Library/lib/pkgconfig/libcrypto.pc", "Library/lib/pkgconfig/libssl.pc", "Library/lib/pkgconfig/openssl.pc", "Library/ssl/ct_log_list.cnf", "Library/ssl/ct_log_list.cnf.dist", "Library/ssl/misc/CA.pl", "Library/ssl/misc/tsget.pl", "Library/ssl/openssl.cnf", "Library/ssl/openssl.cnf.dist", "etc/conda/activate.d/openssl_activate.bat", "etc/conda/activate.d/openssl_activate.ps1", "etc/conda/activate.d/openssl_activate.sh", "etc/conda/deactivate.d/openssl_deactivate.bat", "etc/conda/deactivate.d/openssl_deactivate.ps1", "etc/conda/deactivate.d/openssl_deactivate.sh", ".nonadmin"], "fn": "openssl-3.0.16-h3f729d1_0.conda", "legacy_bz2_md5": "e071cd2b74e888dd3aef739ee4060bc0", "license": "Apache-2.0", "license_family": "Apache", "link": {"source": "C:\\Users\\<USER>\\anaconda3\\pkgs\\openssl-3.0.16-h3f729d1_0", "type": 1}, "md5": "5ceb57e424ffe9918ba8f8af4fcc936a", "name": "openssl", "package_tarball_full_path": "C:\\Users\\<USER>\\anaconda3\\pkgs\\openssl-3.0.16-h3f729d1_0.conda", "paths_data": {"paths": [{"_path": "Library/bin/c_rehash.pl", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:\\\\b\\\\abs_04f5aioy9i\\\\croot\\\\openssl_1740989503961\\\\_h_env", "sha256": "3b8fa93ef1ce2bac0fc65f2e1c8d8bb5cbaee2751848ac619f33ae95d1ca7c95", "sha256_in_prefix": "0a1677572287c8c7adc4578287fa24097b72031b803d2275f7b62676a644c42c", "size_in_bytes": 7268}, {"_path": "Library/bin/libcrypto-3-x64.dll", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "C:\\b\\abs_04f5aioy9i\\croot\\openssl_1740989503961\\_h_env", "sha256": "97ebc121e9478d09358acf8d8c68b465eaa99f8ee3cd515b3d440525b6a9ce90", "sha256_in_prefix": "97ebc121e9478d09358acf8d8c68b465eaa99f8ee3cd515b3d440525b6a9ce90", "size_in_bytes": 5297424}, {"_path": "Library/bin/libcrypto-3-x64.pdb", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "C:\\\\b\\\\abs_04f5aioy9i\\\\croot\\\\openssl_1740989503961\\\\_h_env", "sha256": "09b5836c5d53c4b002679267f11be07782cc2074101c0caa5d8e8d902d5004b5", "sha256_in_prefix": "09b5836c5d53c4b002679267f11be07782cc2074101c0caa5d8e8d902d5004b5", "size_in_bytes": 16076800}, {"_path": "Library/bin/libssl-3-x64.dll", "path_type": "hardlink", "sha256": "d6011e2c3ed87a37dbd7454ef84cd4679e79644a103b74087ca01eccf52c54a8", "sha256_in_prefix": "d6011e2c3ed87a37dbd7454ef84cd4679e79644a103b74087ca01eccf52c54a8", "size_in_bytes": 790288}, {"_path": "Library/bin/libssl-3-x64.pdb", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "C:\\\\b\\\\abs_04f5aioy9i\\\\croot\\\\openssl_1740989503961\\\\_h_env", "sha256": "f9739c5f68dea779c99b8248c37a311eb57477e5786389f7e2b9574b5a4c5d09", "sha256_in_prefix": "f9739c5f68dea779c99b8248c37a311eb57477e5786389f7e2b9574b5a4c5d09", "size_in_bytes": 2895872}, {"_path": "Library/bin/openssl.exe", "path_type": "hardlink", "sha256": "4b3f7f194445efc4bac827860842106aed656d6507527482bffd4faa321a2931", "sha256_in_prefix": "4b3f7f194445efc4bac827860842106aed656d6507527482bffd4faa321a2931", "size_in_bytes": 735504}, {"_path": "Library/bin/openssl.pdb", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "C:\\\\b\\\\abs_04f5aioy9i\\\\croot\\\\openssl_1740989503961\\\\_h_env", "sha256": "f9978c3a12de8ae47b34c565dd4a6d99e886dba69251810e1581f2ac4155842c", "sha256_in_prefix": "f9978c3a12de8ae47b34c565dd4a6d99e886dba69251810e1581f2ac4155842c", "size_in_bytes": 3084288}, {"_path": "Library/include/openssl/__DECC_INCLUDE_EPILOGUE.H", "path_type": "hardlink", "sha256": "3d837d015f23ad248d7e0c74b5b8ca102d81525f166a0a4b7c19900eea982644", "sha256_in_prefix": "3d837d015f23ad248d7e0c74b5b8ca102d81525f166a0a4b7c19900eea982644", "size_in_bytes": 729}, {"_path": "Library/include/openssl/__DECC_INCLUDE_PROLOGUE.H", "path_type": "hardlink", "sha256": "e66be3418a7b707f09fa011c85b0b3fdfcfa1740c46da11385abf23fe9983529", "sha256_in_prefix": "e66be3418a7b707f09fa011c85b0b3fdfcfa1740c46da11385abf23fe9983529", "size_in_bytes": 801}, {"_path": "Library/include/openssl/aes.h", "path_type": "hardlink", "sha256": "27aaa89367b022b12b66cf52c3c2d68f6761965ac36f3f1153202fa44692ad0e", "sha256_in_prefix": "27aaa89367b022b12b66cf52c3c2d68f6761965ac36f3f1153202fa44692ad0e", "size_in_bytes": 3752}, {"_path": "Library/include/openssl/applink.c", "path_type": "hardlink", "sha256": "64f2bbf22447c293a26c309459ac6ed5a77912c2d8133cccf5cbe5dc75360ce8", "sha256_in_prefix": "64f2bbf22447c293a26c309459ac6ed5a77912c2d8133cccf5cbe5dc75360ce8", "size_in_bytes": 3660}, {"_path": "Library/include/openssl/asn1.h", "path_type": "hardlink", "sha256": "21c06736d3f32822f41c5d9e2db3f84c2f97299f2feac252d724c3e5b6ab3aec", "sha256_in_prefix": "21c06736d3f32822f41c5d9e2db3f84c2f97299f2feac252d724c3e5b6ab3aec", "size_in_bytes": 62042}, {"_path": "Library/include/openssl/asn1_mac.h", "path_type": "hardlink", "sha256": "5a0d1d59316bc398bc63af0f1dcf377fb66c3e3132d4c45400c9dbc2003e24b5", "sha256_in_prefix": "5a0d1d59316bc398bc63af0f1dcf377fb66c3e3132d4c45400c9dbc2003e24b5", "size_in_bytes": 398}, {"_path": "Library/include/openssl/asn1err.h", "path_type": "hardlink", "sha256": "75c4b045fef75587c0df5c658b7466b74ad42755368a56cf6ff43581aa5768c6", "sha256_in_prefix": "75c4b045fef75587c0df5c658b7466b74ad42755368a56cf6ff43581aa5768c6", "size_in_bytes": 7731}, {"_path": "Library/include/openssl/asn1t.h", "path_type": "hardlink", "sha256": "03fcf37af6248fad3421306aa87d1bb2365a4b29f4f7be035d87651e42ed012c", "sha256_in_prefix": "03fcf37af6248fad3421306aa87d1bb2365a4b29f4f7be035d87651e42ed012c", "size_in_bytes": 36883}, {"_path": "Library/include/openssl/async.h", "path_type": "hardlink", "sha256": "49369e1569d424f56f016865a34d59b676984e7f67f459e6514241afcd818252", "sha256_in_prefix": "49369e1569d424f56f016865a34d59b676984e7f67f459e6514241afcd818252", "size_in_bytes": 3163}, {"_path": "Library/include/openssl/asyncerr.h", "path_type": "hardlink", "sha256": "154f003cfbf49040a04d9aac459cf5009a5a1d76298b222d66ba5b5a4e3721af", "sha256_in_prefix": "154f003cfbf49040a04d9aac459cf5009a5a1d76298b222d66ba5b5a4e3721af", "size_in_bytes": 842}, {"_path": "Library/include/openssl/bio.h", "path_type": "hardlink", "sha256": "a679845c227343294780919c2fc7c94a58cade652753ae979b6b9560752a3cbe", "sha256_in_prefix": "a679845c227343294780919c2fc7c94a58cade652753ae979b6b9560752a3cbe", "size_in_bytes": 40731}, {"_path": "Library/include/openssl/bioerr.h", "path_type": "hardlink", "sha256": "348571893bca9600b9f790af5c6a02b40bffd83a718450a54a8022c70fef1a14", "sha256_in_prefix": "348571893bca9600b9f790af5c6a02b40bffd83a718450a54a8022c70fef1a14", "size_in_bytes": 3081}, {"_path": "Library/include/openssl/blowfish.h", "path_type": "hardlink", "sha256": "fb4b19b7730d1cc7ff2b9da1435a506ad0ef50263bd168c5ff24214a06580282", "sha256_in_prefix": "fb4b19b7730d1cc7ff2b9da1435a506ad0ef50263bd168c5ff24214a06580282", "size_in_bytes": 2693}, {"_path": "Library/include/openssl/bn.h", "path_type": "hardlink", "sha256": "8141d04fc871e83308921c290fea380ce92db4a1e2647c985d2f9dcc2bedb08d", "sha256_in_prefix": "8141d04fc871e83308921c290fea380ce92db4a1e2647c985d2f9dcc2bedb08d", "size_in_bytes": 23689}, {"_path": "Library/include/openssl/bnerr.h", "path_type": "hardlink", "sha256": "f0dfac26985a7ae40174e90173df9f95b15bba4d3768290746d7258ff1b0ae64", "sha256_in_prefix": "f0dfac26985a7ae40174e90173df9f95b15bba4d3768290746d7258ff1b0ae64", "size_in_bytes": 1949}, {"_path": "Library/include/openssl/buffer.h", "path_type": "hardlink", "sha256": "c87b52702746e224e6242f4a2a2070b007502ea92063b41df2c4f6bec11c37ca", "sha256_in_prefix": "c87b52702746e224e6242f4a2a2070b007502ea92063b41df2c4f6bec11c37ca", "size_in_bytes": 1658}, {"_path": "Library/include/openssl/buffererr.h", "path_type": "hardlink", "sha256": "73f33a7b4406477a0eaf9d0ec42f43b2594167b1d6b84175f378cf5b0de07c12", "sha256_in_prefix": "73f33a7b4406477a0eaf9d0ec42f43b2594167b1d6b84175f378cf5b0de07c12", "size_in_bytes": 594}, {"_path": "Library/include/openssl/camellia.h", "path_type": "hardlink", "sha256": "d1cee6e44668fba0e46c38db7394aa094c6cd2a25b97dbcfcc6f0ff4414f8ebf", "sha256_in_prefix": "d1cee6e44668fba0e46c38db7394aa094c6cd2a25b97dbcfcc6f0ff4414f8ebf", "size_in_bytes": 5069}, {"_path": "Library/include/openssl/cast.h", "path_type": "hardlink", "sha256": "654ac650ae74ca5e9a87ab46c1205157a7489097d005fdccc4c52912cfcefa55", "sha256_in_prefix": "654ac650ae74ca5e9a87ab46c1205157a7489097d005fdccc4c52912cfcefa55", "size_in_bytes": 2066}, {"_path": "Library/include/openssl/cmac.h", "path_type": "hardlink", "sha256": "b26f8ddb9f60eef2601a84a5455c11060e028d8ce700cae682c4a02ffe2f2ca2", "sha256_in_prefix": "b26f8ddb9f60eef2601a84a5455c11060e028d8ce700cae682c4a02ffe2f2ca2", "size_in_bytes": 1608}, {"_path": "Library/include/openssl/cmp.h", "path_type": "hardlink", "sha256": "0f04b773fea93ee353b53274c56c43e7f969df2eaae697a6399faa43fb9f59c0", "sha256_in_prefix": "0f04b773fea93ee353b53274c56c43e7f969df2eaae697a6399faa43fb9f59c0", "size_in_bytes": 41720}, {"_path": "Library/include/openssl/cmp_util.h", "path_type": "hardlink", "sha256": "7a982bac5840812b486176102b1fe8b48dda8cce0fe94f2d35aff5057a99004e", "sha256_in_prefix": "7a982bac5840812b486176102b1fe8b48dda8cce0fe94f2d35aff5057a99004e", "size_in_bytes": 1742}, {"_path": "Library/include/openssl/cmperr.h", "path_type": "hardlink", "sha256": "a15841cd934edf4f79c2f6bde6b08aad01046179774e765295c57efebdb66527", "sha256_in_prefix": "a15841cd934edf4f79c2f6bde6b08aad01046179774e765295c57efebdb66527", "size_in_bytes": 6165}, {"_path": "Library/include/openssl/cms.h", "path_type": "hardlink", "sha256": "ce2a0571ff7ad4317ff2fe359e67ea3b1ed0a393a6ff533e37bbe164da7eb53a", "sha256_in_prefix": "ce2a0571ff7ad4317ff2fe359e67ea3b1ed0a393a6ff533e37bbe164da7eb53a", "size_in_bytes": 34574}, {"_path": "Library/include/openssl/cmserr.h", "path_type": "hardlink", "sha256": "250953529ec294424fd84e58ebb6c65d1047ff9c78a19c9ba1a2a948bcbbed64", "sha256_in_prefix": "250953529ec294424fd84e58ebb6c65d1047ff9c78a19c9ba1a2a948bcbbed64", "size_in_bytes": 6731}, {"_path": "Library/include/openssl/comp.h", "path_type": "hardlink", "sha256": "44ad0613758e8cf84d9ec4f40cf50cbb735b16e659f7e9fd30c2155585d94199", "sha256_in_prefix": "44ad0613758e8cf84d9ec4f40cf50cbb735b16e659f7e9fd30c2155585d94199", "size_in_bytes": 1445}, {"_path": "Library/include/openssl/comperr.h", "path_type": "hardlink", "sha256": "656851389d8f21bc80b566248d7849c6b4ecbd5b178592b8e099c6457b37d87c", "sha256_in_prefix": "656851389d8f21bc80b566248d7849c6b4ecbd5b178592b8e099c6457b37d87c", "size_in_bytes": 813}, {"_path": "Library/include/openssl/conf.h", "path_type": "hardlink", "sha256": "1c0b5f21e976a113657a915ef396b7e187ce5bdf25235292193743bcab418918", "sha256_in_prefix": "1c0b5f21e976a113657a915ef396b7e187ce5bdf25235292193743bcab418918", "size_in_bytes": 10696}, {"_path": "Library/include/openssl/conf_api.h", "path_type": "hardlink", "sha256": "a66bcc69464235679980efc4a687a4fe036388da91173809ca45c0a3cfe47a5b", "sha256_in_prefix": "a66bcc69464235679980efc4a687a4fe036388da91173809ca45c0a3cfe47a5b", "size_in_bytes": 1420}, {"_path": "Library/include/openssl/conferr.h", "path_type": "hardlink", "sha256": "a37a9bb4578d1b2b1b373c820eb005dfe022c596f5cc5b7ab80de56a07d62c9b", "sha256_in_prefix": "a37a9bb4578d1b2b1b373c820eb005dfe022c596f5cc5b7ab80de56a07d62c9b", "size_in_bytes": 2265}, {"_path": "Library/include/openssl/configuration.h", "path_type": "hardlink", "sha256": "38e00579e78e084386e6bea07b70ae3b2f54c6fc71f40cff717c86c8221fe921", "sha256_in_prefix": "38e00579e78e084386e6bea07b70ae3b2f54c6fc71f40cff717c86c8221fe921", "size_in_bytes": 3327}, {"_path": "Library/include/openssl/conftypes.h", "path_type": "hardlink", "sha256": "e8f6697076d2464eaecfe2cdae8d2045388c53da2372fd52df5f6cfdc4d63375", "sha256_in_prefix": "e8f6697076d2464eaecfe2cdae8d2045388c53da2372fd52df5f6cfdc4d63375", "size_in_bytes": 1190}, {"_path": "Library/include/openssl/core.h", "path_type": "hardlink", "sha256": "2981b182ac8930f17b136665b61f1c34c0cfdb4e122f19bd75d7ff552ff5e736", "sha256_in_prefix": "2981b182ac8930f17b136665b61f1c34c0cfdb4e122f19bd75d7ff552ff5e736", "size_in_bytes": 8131}, {"_path": "Library/include/openssl/core_dispatch.h", "path_type": "hardlink", "sha256": "c736175338055b9ba8811d16db30aa5d3e5f9e2f09000706348a5ba06df44c30", "sha256_in_prefix": "c736175338055b9ba8811d16db30aa5d3e5f9e2f09000706348a5ba06df44c30", "size_in_bytes": 47570}, {"_path": "Library/include/openssl/core_names.h", "path_type": "hardlink", "sha256": "2b1676b5ed4d0e418ee4f143f40d5349cccfa5c46e08d34298f2881182da25a0", "sha256_in_prefix": "2b1676b5ed4d0e418ee4f143f40d5349cccfa5c46e08d34298f2881182da25a0", "size_in_bytes": 29014}, {"_path": "Library/include/openssl/core_object.h", "path_type": "hardlink", "sha256": "7a7172d30597e3a3e06c4e67a049d1335aa6f7d5b49641abba8fd4d5a1c07563", "sha256_in_prefix": "7a7172d30597e3a3e06c4e67a049d1335aa6f7d5b49641abba8fd4d5a1c07563", "size_in_bytes": 1126}, {"_path": "Library/include/openssl/crmf.h", "path_type": "hardlink", "sha256": "269055b929140313b60c007a191d2b80be45edf36c7a66fae300d4e78c92ec9d", "sha256_in_prefix": "269055b929140313b60c007a191d2b80be45edf36c7a66fae300d4e78c92ec9d", "size_in_bytes": 14871}, {"_path": "Library/include/openssl/crmferr.h", "path_type": "hardlink", "sha256": "c08a40103c0c6d0d7d9ad0e2781db1f19829d29193d115d38b4d0271d13fecf9", "sha256_in_prefix": "c08a40103c0c6d0d7d9ad0e2781db1f19829d29193d115d38b4d0271d13fecf9", "size_in_bytes": 2011}, {"_path": "Library/include/openssl/crypto.h", "path_type": "hardlink", "sha256": "51a8c99312d5c36ecbed1c96a1786b26c7949dadc0c8494c67523643e4db9c5c", "sha256_in_prefix": "51a8c99312d5c36ecbed1c96a1786b26c7949dadc0c8494c67523643e4db9c5c", "size_in_bytes": 24503}, {"_path": "Library/include/openssl/cryptoerr.h", "path_type": "hardlink", "sha256": "4e7759de28d9f389122c9f5adc93fc20eef7b6619594b1c96c7904b421450d4b", "sha256_in_prefix": "4e7759de28d9f389122c9f5adc93fc20eef7b6619594b1c96c7904b421450d4b", "size_in_bytes": 1899}, {"_path": "Library/include/openssl/cryptoerr_legacy.h", "path_type": "hardlink", "sha256": "870042252331e89723d31079469104cafd676f0fedcbe0d99f56f3e8862fff8d", "sha256_in_prefix": "870042252331e89723d31079469104cafd676f0fedcbe0d99f56f3e8862fff8d", "size_in_bytes": 80396}, {"_path": "Library/include/openssl/ct.h", "path_type": "hardlink", "sha256": "9b9c6df74d9d7ecee8dc17e68a561f9bae901797525f9b28e88c24a7866cbe07", "sha256_in_prefix": "9b9c6df74d9d7ecee8dc17e68a561f9bae901797525f9b28e88c24a7866cbe07", "size_in_bytes": 23338}, {"_path": "Library/include/openssl/cterr.h", "path_type": "hardlink", "sha256": "562bfe4ac984ebfef4fb91bdbe0a649d157f5057ab61ffee3a844d23f7c72c0a", "sha256_in_prefix": "562bfe4ac984ebfef4fb91bdbe0a649d157f5057ab61ffee3a844d23f7c72c0a", "size_in_bytes": 1688}, {"_path": "Library/include/openssl/decoder.h", "path_type": "hardlink", "sha256": "8419fd9e4e333fd477238bbad4ff875d5657b02cc39635c3a5c15f3a5bc7f0f2", "sha256_in_prefix": "8419fd9e4e333fd477238bbad4ff875d5657b02cc39635c3a5c15f3a5bc7f0f2", "size_in_bytes": 5760}, {"_path": "Library/include/openssl/decodererr.h", "path_type": "hardlink", "sha256": "a785fb95930e8b4a18054f77b7d5143d44673f4ca57682899bc2bf3464cafccf", "sha256_in_prefix": "a785fb95930e8b4a18054f77b7d5143d44673f4ca57682899bc2bf3464cafccf", "size_in_bytes": 791}, {"_path": "Library/include/openssl/des.h", "path_type": "hardlink", "sha256": "bb13c7c5e13f3402d674fa88994b92ed72d6cdc1116707765d28bd7e0de31285", "sha256_in_prefix": "bb13c7c5e13f3402d674fa88994b92ed72d6cdc1116707765d28bd7e0de31285", "size_in_bytes": 8525}, {"_path": "Library/include/openssl/dh.h", "path_type": "hardlink", "sha256": "a007f31faa72a53ad0b85688287a231898d78048e49dd17f8e75f0b4338b151c", "sha256_in_prefix": "a007f31faa72a53ad0b85688287a231898d78048e49dd17f8e75f0b4338b151c", "size_in_bytes": 15297}, {"_path": "Library/include/openssl/dherr.h", "path_type": "hardlink", "sha256": "1fdb17fb97cdfb1a5db6a29fb34f77e625a4592614d31b6bd7efb334492f5cf3", "sha256_in_prefix": "1fdb17fb97cdfb1a5db6a29fb34f77e625a4592614d31b6bd7efb334492f5cf3", "size_in_bytes": 2507}, {"_path": "Library/include/openssl/dsa.h", "path_type": "hardlink", "sha256": "28e92a797490e0fd3ba888803fec0104c242149bf922b1b447325efbde0c12d8", "sha256_in_prefix": "28e92a797490e0fd3ba888803fec0104c242149bf922b1b447325efbde0c12d8", "size_in_bytes": 12442}, {"_path": "Library/include/openssl/dsaerr.h", "path_type": "hardlink", "sha256": "69c2ecff5f62898461bc521ea918abd2a673206dd5e8d43288ad25d2c012f163", "sha256_in_prefix": "69c2ecff5f62898461bc521ea918abd2a673206dd5e8d43288ad25d2c012f163", "size_in_bytes": 1629}, {"_path": "Library/include/openssl/dtls1.h", "path_type": "hardlink", "sha256": "1d1f404032a9eb31408c1f10bdff554d5740fb345b64b86fb74da8df95fbd901", "sha256_in_prefix": "1d1f404032a9eb31408c1f10bdff554d5740fb345b64b86fb74da8df95fbd901", "size_in_bytes": 1465}, {"_path": "Library/include/openssl/e_os2.h", "path_type": "hardlink", "sha256": "a5c404e815f8ea17c46ed1b78118f32c2e31fd3ce42f761af2bf8fb5a5864550", "sha256_in_prefix": "a5c404e815f8ea17c46ed1b78118f32c2e31fd3ce42f761af2bf8fb5a5864550", "size_in_bytes": 8718}, {"_path": "Library/include/openssl/ebcdic.h", "path_type": "hardlink", "sha256": "75a668c25c97853d5ba37ebce060a15152573242e3729d42830eba1daa642404", "sha256_in_prefix": "75a668c25c97853d5ba37ebce060a15152573242e3729d42830eba1daa642404", "size_in_bytes": 1042}, {"_path": "Library/include/openssl/ec.h", "path_type": "hardlink", "sha256": "b863eb4d76d0ac3a6465e859de128549e169eec280f029a22404321a6ebb1b80", "sha256_in_prefix": "b863eb4d76d0ac3a6465e859de128549e169eec280f029a22404321a6ebb1b80", "size_in_bytes": 67683}, {"_path": "Library/include/openssl/ecdh.h", "path_type": "hardlink", "sha256": "5b99fdd1dfea38640ed8a506fb9b66db381cc26a1254448a81cc6b161e41850f", "sha256_in_prefix": "5b99fdd1dfea38640ed8a506fb9b66db381cc26a1254448a81cc6b161e41850f", "size_in_bytes": 361}, {"_path": "Library/include/openssl/ecdsa.h", "path_type": "hardlink", "sha256": "5b99fdd1dfea38640ed8a506fb9b66db381cc26a1254448a81cc6b161e41850f", "sha256_in_prefix": "5b99fdd1dfea38640ed8a506fb9b66db381cc26a1254448a81cc6b161e41850f", "size_in_bytes": 361}, {"_path": "Library/include/openssl/ecerr.h", "path_type": "hardlink", "sha256": "ce4fec7ee41de25a20abb7a9f00fe93305793a7bd2023d434b9aa6f64f91058a", "sha256_in_prefix": "ce4fec7ee41de25a20abb7a9f00fe93305793a7bd2023d434b9aa6f64f91058a", "size_in_bytes": 5405}, {"_path": "Library/include/openssl/encoder.h", "path_type": "hardlink", "sha256": "907d2f061c2972447d3f0c1cfc149c78791b1e4bdc131ad5a3eed1d084c76b41", "sha256_in_prefix": "907d2f061c2972447d3f0c1cfc149c78791b1e4bdc131ad5a3eed1d084c76b41", "size_in_bytes": 5450}, {"_path": "Library/include/openssl/encodererr.h", "path_type": "hardlink", "sha256": "63504766e9fcf36fe1527d95fe21460574896da187c60707bfa68254a35693b7", "sha256_in_prefix": "63504766e9fcf36fe1527d95fe21460574896da187c60707bfa68254a35693b7", "size_in_bytes": 791}, {"_path": "Library/include/openssl/engine.h", "path_type": "hardlink", "sha256": "11530c79754e3d241cb277d6bc9c9a3f6eb382db53513877b40488908e243556", "sha256_in_prefix": "11530c79754e3d241cb277d6bc9c9a3f6eb382db53513877b40488908e243556", "size_in_bytes": 38821}, {"_path": "Library/include/openssl/engineerr.h", "path_type": "hardlink", "sha256": "8616a93b1b1bd8d1221844834817c28b7da78be1649a5b1780d9ea65fba8807c", "sha256_in_prefix": "8616a93b1b1bd8d1221844834817c28b7da78be1649a5b1780d9ea65fba8807c", "size_in_bytes": 2838}, {"_path": "Library/include/openssl/err.h", "path_type": "hardlink", "sha256": "67f9f3f83f70524dee7166010dbb59bf7bf1bed385b625b0993e67bf440a7084", "sha256_in_prefix": "67f9f3f83f70524dee7166010dbb59bf7bf1bed385b625b0993e67bf440a7084", "size_in_bytes": 22482}, {"_path": "Library/include/openssl/ess.h", "path_type": "hardlink", "sha256": "494f87fe22195a9756db7e603b7e53f2c26145da37ab6e274400929e7bf3cc50", "sha256_in_prefix": "494f87fe22195a9756db7e603b7e53f2c26145da37ab6e274400929e7bf3cc50", "size_in_bytes": 9096}, {"_path": "Library/include/openssl/esserr.h", "path_type": "hardlink", "sha256": "e791193e891b0784670d5410539aeea9d2a8591de71495b4add6e7dbf9dc22cd", "sha256_in_prefix": "e791193e891b0784670d5410539aeea9d2a8591de71495b4add6e7dbf9dc22cd", "size_in_bytes": 1144}, {"_path": "Library/include/openssl/evp.h", "path_type": "hardlink", "sha256": "21f9783955cd806e69b09ad6fbc956e7f2dc18f8804a4132e2ea0f37492d0c59", "sha256_in_prefix": "21f9783955cd806e69b09ad6fbc956e7f2dc18f8804a4132e2ea0f37492d0c59", "size_in_bytes": 103745}, {"_path": "Library/include/openssl/evperr.h", "path_type": "hardlink", "sha256": "7fab5bade4441300fa7ffe721ca2eb361835998db7d386f8f1be7db5b7596c3f", "sha256_in_prefix": "7fab5bade4441300fa7ffe721ca2eb361835998db7d386f8f1be7db5b7596c3f", "size_in_bytes": 7351}, {"_path": "Library/include/openssl/fips_names.h", "path_type": "hardlink", "sha256": "2d9f27ed8c44edc185101da548f533d0dbee1435fd8cdb7ad8f02690d31cd20b", "sha256_in_prefix": "2d9f27ed8c44edc185101da548f533d0dbee1435fd8cdb7ad8f02690d31cd20b", "size_in_bytes": 1679}, {"_path": "Library/include/openssl/fipskey.h", "path_type": "hardlink", "sha256": "be2cbfd5e3a82d97566c390cb881cded2136edad5d12783c8419da623b18ac66", "sha256_in_prefix": "be2cbfd5e3a82d97566c390cb881cded2136edad5d12783c8419da623b18ac66", "size_in_bytes": 1046}, {"_path": "Library/include/openssl/hmac.h", "path_type": "hardlink", "sha256": "e49fbe0086f8fbefa5648eef70bc84e8090a9226a1e3c6e856499373004aed0a", "sha256_in_prefix": "e49fbe0086f8fbefa5648eef70bc84e8090a9226a1e3c6e856499373004aed0a", "size_in_bytes": 2141}, {"_path": "Library/include/openssl/http.h", "path_type": "hardlink", "sha256": "dd409efeca44be216a7af99b9f39653a5129bfc05f415d6dfaec17758641e1ca", "sha256_in_prefix": "dd409efeca44be216a7af99b9f39653a5129bfc05f415d6dfaec17758641e1ca", "size_in_bytes": 5346}, {"_path": "Library/include/openssl/httperr.h", "path_type": "hardlink", "sha256": "b50562e98d92c08e47e2b1b0bcf5652820b2a774652968a1188f9f2d87f2fe87", "sha256_in_prefix": "b50562e98d92c08e47e2b1b0bcf5652820b2a774652968a1188f9f2d87f2fe87", "size_in_bytes": 2451}, {"_path": "Library/include/openssl/idea.h", "path_type": "hardlink", "sha256": "239122df15e738d7552dd76850c55a9ffe0136f33506c23d9058215a1255af66", "sha256_in_prefix": "239122df15e738d7552dd76850c55a9ffe0136f33506c23d9058215a1255af66", "size_in_bytes": 3010}, {"_path": "Library/include/openssl/kdf.h", "path_type": "hardlink", "sha256": "41756fe038443d1d270458d53d6e42ea78d12d980728b6a9284fa259958ea00a", "sha256_in_prefix": "41756fe038443d1d270458d53d6e42ea78d12d980728b6a9284fa259958ea00a", "size_in_bytes": 5619}, {"_path": "Library/include/openssl/kdferr.h", "path_type": "hardlink", "sha256": "3d9f27fffdb49e0ece9d5a62adbb9cc42c56262b00cc8ce7f956b2cb05a2a22d", "sha256_in_prefix": "3d9f27fffdb49e0ece9d5a62adbb9cc42c56262b00cc8ce7f956b2cb05a2a22d", "size_in_bytes": 482}, {"_path": "Library/include/openssl/lhash.h", "path_type": "hardlink", "sha256": "324cd5a7b872adaca791a58cb0f58177397667360326c215f2aba17650806a32", "sha256_in_prefix": "324cd5a7b872adaca791a58cb0f58177397667360326c215f2aba17650806a32", "size_in_bytes": 14349}, {"_path": "Library/include/openssl/macros.h", "path_type": "hardlink", "sha256": "e480df214285bb3225da7549c0468590f7156ca399591167ee41360936264c46", "sha256_in_prefix": "e480df214285bb3225da7549c0468590f7156ca399591167ee41360936264c46", "size_in_bytes": 10110}, {"_path": "Library/include/openssl/md2.h", "path_type": "hardlink", "sha256": "4add77ed047736979dc442a49d42921cce21e654a2dceef058d0191aa2d3c941", "sha256_in_prefix": "4add77ed047736979dc442a49d42921cce21e654a2dceef058d0191aa2d3c941", "size_in_bytes": 1461}, {"_path": "Library/include/openssl/md4.h", "path_type": "hardlink", "sha256": "0472e597d139b44dd7d78d9093a5d8109417d18e9955fc940f1ea3e2e892ab44", "sha256_in_prefix": "0472e597d139b44dd7d78d9093a5d8109417d18e9955fc940f1ea3e2e892ab44", "size_in_bytes": 1699}, {"_path": "Library/include/openssl/md5.h", "path_type": "hardlink", "sha256": "308c901ec1a28f9b0098717f689ca63e104ce805050802d38b8f122d85ab2c78", "sha256_in_prefix": "308c901ec1a28f9b0098717f689ca63e104ce805050802d38b8f122d85ab2c78", "size_in_bytes": 1696}, {"_path": "Library/include/openssl/mdc2.h", "path_type": "hardlink", "sha256": "42b844c9ae9e00e7c0b0e28858b8b3db7b8abf7e514e5e63f43456371ed3384b", "sha256_in_prefix": "42b844c9ae9e00e7c0b0e28858b8b3db7b8abf7e514e5e63f43456371ed3384b", "size_in_bytes": 1441}, {"_path": "Library/include/openssl/modes.h", "path_type": "hardlink", "sha256": "4a8b3b1dafc15798a3b2bef0e3885275746e7fae73a0d96e55da55261554ba52", "sha256_in_prefix": "4a8b3b1dafc15798a3b2bef0e3885275746e7fae73a0d96e55da55261554ba52", "size_in_bytes": 10786}, {"_path": "Library/include/openssl/obj_mac.h", "path_type": "hardlink", "sha256": "c1d31f32a3dbc9dea1db10f322b4b46a24c3d4411fe54630df59fa46fc2b583a", "sha256_in_prefix": "c1d31f32a3dbc9dea1db10f322b4b46a24c3d4411fe54630df59fa46fc2b583a", "size_in_bytes": 228668}, {"_path": "Library/include/openssl/objects.h", "path_type": "hardlink", "sha256": "5fc6f3f0dd5e46fd409cb51ae1b331fec799fb6ef4b5efdc8ffbe264e5e83997", "sha256_in_prefix": "5fc6f3f0dd5e46fd409cb51ae1b331fec799fb6ef4b5efdc8ffbe264e5e83997", "size_in_bytes": 6848}, {"_path": "Library/include/openssl/objectserr.h", "path_type": "hardlink", "sha256": "e17a8d7f62a1ef257fd90e604d4293bf02d5f81ae8198efe1e197c5b27baeb8c", "sha256_in_prefix": "e17a8d7f62a1ef257fd90e604d4293bf02d5f81ae8198efe1e197c5b27baeb8c", "size_in_bytes": 782}, {"_path": "Library/include/openssl/ocsp.h", "path_type": "hardlink", "sha256": "01aa2aa17ccad22ebc1a1701ad27b67a165a0c23f9e50fe5ad86b4e90ef190b9", "sha256_in_prefix": "01aa2aa17ccad22ebc1a1701ad27b67a165a0c23f9e50fe5ad86b4e90ef190b9", "size_in_bytes": 29835}, {"_path": "Library/include/openssl/ocsperr.h", "path_type": "hardlink", "sha256": "178329cfc042d3f1eb6e179206d844de41ba05ee4ac0ed9e3e6c861fb49d68ea", "sha256_in_prefix": "178329cfc042d3f1eb6e179206d844de41ba05ee4ac0ed9e3e6c861fb49d68ea", "size_in_bytes": 2200}, {"_path": "Library/include/openssl/opensslconf.h", "path_type": "hardlink", "sha256": "890184233890bacd52fd420fef07befad411b9a318b97efbf36f46673d3e7841", "sha256_in_prefix": "890184233890bacd52fd420fef07befad411b9a318b97efbf36f46673d3e7841", "size_in_bytes": 515}, {"_path": "Library/include/openssl/opensslv.h", "path_type": "hardlink", "sha256": "a391f18a4c6ec350719dbdb80c4edd4b8d7ea5cf0e58ae72f3ace47f4271f68b", "sha256_in_prefix": "a391f18a4c6ec350719dbdb80c4edd4b8d7ea5cf0e58ae72f3ace47f4271f68b", "size_in_bytes": 3304}, {"_path": "Library/include/openssl/ossl_typ.h", "path_type": "hardlink", "sha256": "76cb203ef3bcd305f4171e1d33f3f3319dee6354c2433493e5e9068aa79672fd", "sha256_in_prefix": "76cb203ef3bcd305f4171e1d33f3f3319dee6354c2433493e5e9068aa79672fd", "size_in_bytes": 562}, {"_path": "Library/include/openssl/param_build.h", "path_type": "hardlink", "sha256": "3bf39b1037256466f1a89868621b2b62f1d05e63064159e60727041b170d55e3", "sha256_in_prefix": "3bf39b1037256466f1a89868621b2b62f1d05e63064159e60727041b170d55e3", "size_in_bytes": 2809}, {"_path": "Library/include/openssl/params.h", "path_type": "hardlink", "sha256": "10d8e0157e339ee01f3b9c60c4b5bc60e6d4edce1084f0c9589ff75bf3a9f693", "sha256_in_prefix": "10d8e0157e339ee01f3b9c60c4b5bc60e6d4edce1084f0c9589ff75bf3a9f693", "size_in_bytes": 7328}, {"_path": "Library/include/openssl/pem.h", "path_type": "hardlink", "sha256": "9ae49f961842fa3e2e76ea796e48b2a984e2a66dc0c266a52d01ac7bab5bd9f1", "sha256_in_prefix": "9ae49f961842fa3e2e76ea796e48b2a984e2a66dc0c266a52d01ac7bab5bd9f1", "size_in_bytes": 25764}, {"_path": "Library/include/openssl/pem2.h", "path_type": "hardlink", "sha256": "a34a1607983b5f32be8ca49e75c3b41f1c9413b4eb777af144958283ecbd3922", "sha256_in_prefix": "a34a1607983b5f32be8ca49e75c3b41f1c9413b4eb777af144958283ecbd3922", "size_in_bytes": 531}, {"_path": "Library/include/openssl/pemerr.h", "path_type": "hardlink", "sha256": "843df90b1b434eed626bb6b8bccd5f6ed530e592d706584f56a725d254d8a5d2", "sha256_in_prefix": "843df90b1b434eed626bb6b8bccd5f6ed530e592d706584f56a725d254d8a5d2", "size_in_bytes": 2634}, {"_path": "Library/include/openssl/pkcs12.h", "path_type": "hardlink", "sha256": "72724303808766cc0a5a72f48ad5b1eb219ca4685a5fc5574d038214614d1a83", "sha256_in_prefix": "72724303808766cc0a5a72f48ad5b1eb219ca4685a5fc5574d038214614d1a83", "size_in_bytes": 19666}, {"_path": "Library/include/openssl/pkcs12err.h", "path_type": "hardlink", "sha256": "b692b1a2c7fc06002dee07a868f0ec394e9b7f20b5e151f78e0941e143c2d2d4", "sha256_in_prefix": "b692b1a2c7fc06002dee07a868f0ec394e9b7f20b5e151f78e0941e143c2d2d4", "size_in_bytes": 1837}, {"_path": "Library/include/openssl/pkcs7.h", "path_type": "hardlink", "sha256": "dc51d4166e9450f1c9c4a80bda105a953fe7db8a48a6e5d6c1266ef40f4807f2", "sha256_in_prefix": "dc51d4166e9450f1c9c4a80bda105a953fe7db8a48a6e5d6c1266ef40f4807f2", "size_in_bytes": 22849}, {"_path": "Library/include/openssl/pkcs7err.h", "path_type": "hardlink", "sha256": "9fe7a51f3de13b1fd03b319c64b8bd287164eb6ce7d3481994141c0be51396d5", "sha256_in_prefix": "9fe7a51f3de13b1fd03b319c64b8bd287164eb6ce7d3481994141c0be51396d5", "size_in_bytes": 2952}, {"_path": "Library/include/openssl/prov_ssl.h", "path_type": "hardlink", "sha256": "1f5c121c02d31f695bff708396e0512286fa04dee67f12ab895c0c558ba33f20", "sha256_in_prefix": "1f5c121c02d31f695bff708396e0512286fa04dee67f12ab895c0c558ba33f20", "size_in_bytes": 981}, {"_path": "Library/include/openssl/proverr.h", "path_type": "hardlink", "sha256": "adf058748c58f5e0e3446a1af743ea70e4387a95ddc0d08d9ceddf79a87ac517", "sha256_in_prefix": "adf058748c58f5e0e3446a1af743ea70e4387a95ddc0d08d9ceddf79a87ac517", "size_in_bytes": 8217}, {"_path": "Library/include/openssl/provider.h", "path_type": "hardlink", "sha256": "b9e5b46a26f7e7ec383fe540404092e4d76ae54b5822744e4ba0750ef8d2cac0", "sha256_in_prefix": "b9e5b46a26f7e7ec383fe540404092e4d76ae54b5822744e4ba0750ef8d2cac0", "size_in_bytes": 2325}, {"_path": "Library/include/openssl/rand.h", "path_type": "hardlink", "sha256": "85bda3b0a72aedb08cb8b75bb49366e4f8e07599f9dae5df8d688ce57073033f", "sha256_in_prefix": "85bda3b0a72aedb08cb8b75bb49366e4f8e07599f9dae5df8d688ce57073033f", "size_in_bytes": 3860}, {"_path": "Library/include/openssl/randerr.h", "path_type": "hardlink", "sha256": "80260d41625b9ed9f727e8553a65a111645b3c013df8cc8fa6a718d32b643c88", "sha256_in_prefix": "80260d41625b9ed9f727e8553a65a111645b3c013df8cc8fa6a718d32b643c88", "size_in_bytes": 3257}, {"_path": "Library/include/openssl/rc2.h", "path_type": "hardlink", "sha256": "08c6865d169a300e8bc818bd810f80ffb8a21d69e97dad88e400b586d0f3e965", "sha256_in_prefix": "08c6865d169a300e8bc818bd810f80ffb8a21d69e97dad88e400b586d0f3e965", "size_in_bytes": 2382}, {"_path": "Library/include/openssl/rc4.h", "path_type": "hardlink", "sha256": "ea45836c253246c1d6f1b16b360dbb59322e26e28bfc54881d698e7cd5057666", "sha256_in_prefix": "ea45836c253246c1d6f1b16b360dbb59322e26e28bfc54881d698e7cd5057666", "size_in_bytes": 1194}, {"_path": "Library/include/openssl/rc5.h", "path_type": "hardlink", "sha256": "968c96ead08204edb8148981094700cbc3338ed0613c4469da5ab4675fa1ce29", "sha256_in_prefix": "968c96ead08204edb8148981094700cbc3338ed0613c4469da5ab4675fa1ce29", "size_in_bytes": 2861}, {"_path": "Library/include/openssl/ripemd.h", "path_type": "hardlink", "sha256": "2e28edeb6613516db89e28c9d962301f4fe7b38366ebdd1d35933f3491d57b9d", "sha256_in_prefix": "2e28edeb6613516db89e28c9d962301f4fe7b38366ebdd1d35933f3491d57b9d", "size_in_bytes": 1717}, {"_path": "Library/include/openssl/rsa.h", "path_type": "hardlink", "sha256": "55aa4b44d21ebb0d7cbc54273f0cf6032c449f5055cfae6793c5b68a682d6692", "sha256_in_prefix": "55aa4b44d21ebb0d7cbc54273f0cf6032c449f5055cfae6793c5b68a682d6692", "size_in_bytes": 28136}, {"_path": "Library/include/openssl/rsaerr.h", "path_type": "hardlink", "sha256": "a745e6b2835af7bb933e78870a270d51ab33778fe10a5cd377422d4b9587dcf0", "sha256_in_prefix": "a745e6b2835af7bb933e78870a270d51ab33778fe10a5cd377422d4b9587dcf0", "size_in_bytes": 5681}, {"_path": "Library/include/openssl/safestack.h", "path_type": "hardlink", "sha256": "1089ec732df2ababf7185ecf93660a5a8e2cf6d84eee3097afa514086cde7cb5", "sha256_in_prefix": "1089ec732df2ababf7185ecf93660a5a8e2cf6d84eee3097afa514086cde7cb5", "size_in_bytes": 18736}, {"_path": "Library/include/openssl/seed.h", "path_type": "hardlink", "sha256": "0d6d206f240f7bd6fa28cd4ec66b2b878f199af3ce6eda172af9fe31ebb71586", "sha256_in_prefix": "0d6d206f240f7bd6fa28cd4ec66b2b878f199af3ce6eda172af9fe31ebb71586", "size_in_bytes": 3964}, {"_path": "Library/include/openssl/self_test.h", "path_type": "hardlink", "sha256": "5a77b263ac9a41190c15fc0c6932b0d573d9034d55b401ccdc52858b5ee9c5fc", "sha256_in_prefix": "5a77b263ac9a41190c15fc0c6932b0d573d9034d55b401ccdc52858b5ee9c5fc", "size_in_bytes": 4015}, {"_path": "Library/include/openssl/sha.h", "path_type": "hardlink", "sha256": "06500535b9b3d9742e745558dc02e52d0df6d75b038457d4f6c374ed68d39eaf", "sha256_in_prefix": "06500535b9b3d9742e745558dc02e52d0df6d75b038457d4f6c374ed68d39eaf", "size_in_bytes": 4658}, {"_path": "Library/include/openssl/srp.h", "path_type": "hardlink", "sha256": "8b4982b2f881ef4234279e1fe31634848a64db40d66762c2e396a4f8beafb296", "sha256_in_prefix": "8b4982b2f881ef4234279e1fe31634848a64db40d66762c2e396a4f8beafb296", "size_in_bytes": 15772}, {"_path": "Library/include/openssl/srtp.h", "path_type": "hardlink", "sha256": "d2b97e90531bf9cdb086d9943a518bc474aebaa0aef02f1d41e8113fe944c9d9", "sha256_in_prefix": "d2b97e90531bf9cdb086d9943a518bc474aebaa0aef02f1d41e8113fe944c9d9", "size_in_bytes": 1429}, {"_path": "Library/include/openssl/ssl.h", "path_type": "hardlink", "sha256": "4cc6ef3fd3be5fcd8707cd01a0b972a9ec4bb13e01c0a858c88170fd77d3a484", "sha256_in_prefix": "4cc6ef3fd3be5fcd8707cd01a0b972a9ec4bb13e01c0a858c88170fd77d3a484", "size_in_bytes": 127537}, {"_path": "Library/include/openssl/ssl2.h", "path_type": "hardlink", "sha256": "92e3330e2867bf17d3b305ba0f6fe6b073ad4bdb9db519e4224bbd993f1e9cb7", "sha256_in_prefix": "92e3330e2867bf17d3b305ba0f6fe6b073ad4bdb9db519e4224bbd993f1e9cb7", "size_in_bytes": 658}, {"_path": "Library/include/openssl/ssl3.h", "path_type": "hardlink", "sha256": "5ce26c99d8a0fffe062a4293f01f6d55619b4e1b8f75bf0065fb3faa2ac512e9", "sha256_in_prefix": "5ce26c99d8a0fffe062a4293f01f6d55619b4e1b8f75bf0065fb3faa2ac512e9", "size_in_bytes": 14773}, {"_path": "Library/include/openssl/sslerr.h", "path_type": "hardlink", "sha256": "f81905743cb44b6a82f79a6edba7a879740da8cfc69b20d5a51a0e27f325f54a", "sha256_in_prefix": "f81905743cb44b6a82f79a6edba7a879740da8cfc69b20d5a51a0e27f325f54a", "size_in_bytes": 20527}, {"_path": "Library/include/openssl/sslerr_legacy.h", "path_type": "hardlink", "sha256": "98401ca29f46694fff11304801d995015a7e4a81afe0db0a9a79a0bdde9e03d8", "sha256_in_prefix": "98401ca29f46694fff11304801d995015a7e4a81afe0db0a9a79a0bdde9e03d8", "size_in_bytes": 27005}, {"_path": "Library/include/openssl/stack.h", "path_type": "hardlink", "sha256": "69f94382a15a3c4cfd1dda32108db5234727b36ed0e25f1fb12e0993c7b5ac95", "sha256_in_prefix": "69f94382a15a3c4cfd1dda32108db5234727b36ed0e25f1fb12e0993c7b5ac95", "size_in_bytes": 3284}, {"_path": "Library/include/openssl/store.h", "path_type": "hardlink", "sha256": "cfd4ee1777782d642da53a045d253ede58f0f0463647e6d4f352953b26e2e058", "sha256_in_prefix": "cfd4ee1777782d642da53a045d253ede58f0f0463647e6d4f352953b26e2e058", "size_in_bytes": 15178}, {"_path": "Library/include/openssl/storeerr.h", "path_type": "hardlink", "sha256": "370277e107a1b979ff5e0bd28f5adb92e066d41831ac37ce7108d2a1b84376f6", "sha256_in_prefix": "370277e107a1b979ff5e0bd28f5adb92e066d41831ac37ce7108d2a1b84376f6", "size_in_bytes": 2092}, {"_path": "Library/include/openssl/symhacks.h", "path_type": "hardlink", "sha256": "68b54776fa15943f3f018be6c7dc7a8847c9f512fb5eeec4f093804197dc2dfa", "sha256_in_prefix": "68b54776fa15943f3f018be6c7dc7a8847c9f512fb5eeec4f093804197dc2dfa", "size_in_bytes": 1290}, {"_path": "Library/include/openssl/tls1.h", "path_type": "hardlink", "sha256": "3b00ace186f249ab037b165847a1e100705ce23464d1e12bb40d55dd421de33c", "sha256_in_prefix": "3b00ace186f249ab037b165847a1e100705ce23464d1e12bb40d55dd421de33c", "size_in_bytes": 71796}, {"_path": "Library/include/openssl/trace.h", "path_type": "hardlink", "sha256": "ece8835757afceedac1ab80b4081db1a5b9758bd1eab887c00834dd91d4d5339", "sha256_in_prefix": "ece8835757afceedac1ab80b4081db1a5b9758bd1eab887c00834dd91d4d5339", "size_in_bytes": 10277}, {"_path": "Library/include/openssl/ts.h", "path_type": "hardlink", "sha256": "eca8f795f977a1f52bd84c8c01d2e90686887fc151a9309efdeb95f42d1cd327", "sha256_in_prefix": "eca8f795f977a1f52bd84c8c01d2e90686887fc151a9309efdeb95f42d1cd327", "size_in_bytes": 19706}, {"_path": "Library/include/openssl/tserr.h", "path_type": "hardlink", "sha256": "0d851cb9db84c48bb8a9871a988950fd0b62ecc854b11641e3e9a07fa191a6f6", "sha256_in_prefix": "0d851cb9db84c48bb8a9871a988950fd0b62ecc854b11641e3e9a07fa191a6f6", "size_in_bytes": 3074}, {"_path": "Library/include/openssl/txt_db.h", "path_type": "hardlink", "sha256": "1a6a6b331ef3cc6c632f782e8da2fa81aaeeac56e4d0b2fb3016f936805be257", "sha256_in_prefix": "1a6a6b331ef3cc6c632f782e8da2fa81aaeeac56e4d0b2fb3016f936805be257", "size_in_bytes": 1784}, {"_path": "Library/include/openssl/types.h", "path_type": "hardlink", "sha256": "4986b31300621b35ddd0e2220fd9943eabc264003d73364282869fbb5c1d4a84", "sha256_in_prefix": "4986b31300621b35ddd0e2220fd9943eabc264003d73364282869fbb5c1d4a84", "size_in_bytes": 7206}, {"_path": "Library/include/openssl/ui.h", "path_type": "hardlink", "sha256": "1ec7da15b464387449827771eb3884b3a0f2a66001703809ba4d519e0ba4636a", "sha256_in_prefix": "1ec7da15b464387449827771eb3884b3a0f2a66001703809ba4d519e0ba4636a", "size_in_bytes": 19658}, {"_path": "Library/include/openssl/uierr.h", "path_type": "hardlink", "sha256": "6f46dc9509b4d10802aaa1ad3c84763a2843312fdc8dd8add5c7b24e7f0c877f", "sha256_in_prefix": "6f46dc9509b4d10802aaa1ad3c84763a2843312fdc8dd8add5c7b24e7f0c877f", "size_in_bytes": 1391}, {"_path": "Library/include/openssl/whrlpool.h", "path_type": "hardlink", "sha256": "bb8f9f6ad1960e87f78363793130a0c1bee89b64a12eb32e939791fb0ca61016", "sha256_in_prefix": "bb8f9f6ad1960e87f78363793130a0c1bee89b64a12eb32e939791fb0ca61016", "size_in_bytes": 1853}, {"_path": "Library/include/openssl/x509.h", "path_type": "hardlink", "sha256": "b92a7b83799d075816a2a30fb18c16b1c5ec258bb8debd8056034fc23b33beca", "sha256_in_prefix": "b92a7b83799d075816a2a30fb18c16b1c5ec258bb8debd8056034fc23b33beca", "size_in_bytes": 72824}, {"_path": "Library/include/openssl/x509_vfy.h", "path_type": "hardlink", "sha256": "d66e75c6d3914f1115ab98831a1302669787f766cb9a92cda2480a937c766aa0", "sha256_in_prefix": "d66e75c6d3914f1115ab98831a1302669787f766cb9a92cda2480a937c766aa0", "size_in_bytes": 52921}, {"_path": "Library/include/openssl/x509err.h", "path_type": "hardlink", "sha256": "a9f2e315eb068c81dd1711a4b2cdc65af0cdd976912704b86f9cd33b341fdd2b", "sha256_in_prefix": "a9f2e315eb068c81dd1711a4b2cdc65af0cdd976912704b86f9cd33b341fdd2b", "size_in_bytes": 3319}, {"_path": "Library/include/openssl/x509v3.h", "path_type": "hardlink", "sha256": "461bd457ee55f1c6799620dbe0c136f2ce71d272a79e8c5ea907de58cce6fde6", "sha256_in_prefix": "461bd457ee55f1c6799620dbe0c136f2ce71d272a79e8c5ea907de58cce6fde6", "size_in_bytes": 95418}, {"_path": "Library/include/openssl/x509v3err.h", "path_type": "hardlink", "sha256": "25ce00779ee00002830ede3e302a8b4bf03dbc505243d2b87a86a62c31a52d6f", "sha256_in_prefix": "25ce00779ee00002830ede3e302a8b4bf03dbc505243d2b87a86a62c31a52d6f", "size_in_bytes": 4819}, {"_path": "Library/lib/libcrypto.lib", "path_type": "hardlink", "sha256": "a27f00999547710cb077d884a6b2d84b516e9cac58117337e4c98a9afd04929d", "sha256_in_prefix": "a27f00999547710cb077d884a6b2d84b516e9cac58117337e4c98a9afd04929d", "size_in_bytes": 1251364}, {"_path": "Library/lib/libssl.lib", "path_type": "hardlink", "sha256": "e13eeaef26f2ece820283734db1673eefe8bccf4285785d0459c24cd0488d246", "sha256_in_prefix": "e13eeaef26f2ece820283734db1673eefe8bccf4285785d0459c24cd0488d246", "size_in_bytes": 126434}, {"_path": "Library/lib/pkgconfig/libcrypto.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_04f5aioy9i/croot/openssl_1740989503961/_h_env", "sha256": "db5368187590d6411a25e2d17a0b18d6d7ddd7710072265c2fb7586201043129", "sha256_in_prefix": "bc000fca541677873983e799be7f8cf78b51e165aa418aaa37d1af796f5b752e", "size_in_bytes": 325}, {"_path": "Library/lib/pkgconfig/libssl.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_04f5aioy9i/croot/openssl_1740989503961/_h_env", "sha256": "b81642046e735754e996eefcbec141b669a9e33b4ba1facd7bfdc8a4efcdbb19", "sha256_in_prefix": "1434f4ead8506095ff9a6d0af27e4f75fe049cf7c6f4acf59ba0242999d23017", "size_in_bytes": 325}, {"_path": "Library/lib/pkgconfig/openssl.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_04f5aioy9i/croot/openssl_1740989503961/_h_env", "sha256": "c7932fcc3aa19e36f58cdbb5c8d00ba56b86d301e355aaa0d9e6f138f1373784", "sha256_in_prefix": "718e4770ac864f9c437e99b0efa4077c3a5e718bcfbbe787ea195a2dd669ed00", "size_in_bytes": 280}, {"_path": "Library/ssl/ct_log_list.cnf", "path_type": "hardlink", "sha256": "f1c1803d13d1d0b755b13b23c28bd4e20e07baf9f2b744c9337ba5866aa0ec3b", "sha256_in_prefix": "f1c1803d13d1d0b755b13b23c28bd4e20e07baf9f2b744c9337ba5866aa0ec3b", "size_in_bytes": 412}, {"_path": "Library/ssl/ct_log_list.cnf.dist", "path_type": "hardlink", "sha256": "f1c1803d13d1d0b755b13b23c28bd4e20e07baf9f2b744c9337ba5866aa0ec3b", "sha256_in_prefix": "f1c1803d13d1d0b755b13b23c28bd4e20e07baf9f2b744c9337ba5866aa0ec3b", "size_in_bytes": 412}, {"_path": "Library/ssl/misc/CA.pl", "path_type": "hardlink", "sha256": "2d5ee9f64e008a31b9e67fc140ccb3fe3a40f263e3323747b26c566f17e3488e", "sha256_in_prefix": "2d5ee9f64e008a31b9e67fc140ccb3fe3a40f263e3323747b26c566f17e3488e", "size_in_bytes": 8357}, {"_path": "Library/ssl/misc/tsget.pl", "path_type": "hardlink", "sha256": "ed060e36e213b85d7e632450ba2eda5cb9e0917274cf5ea76006022327bb5a87", "sha256_in_prefix": "ed060e36e213b85d7e632450ba2eda5cb9e0917274cf5ea76006022327bb5a87", "size_in_bytes": 7005}, {"_path": "Library/ssl/openssl.cnf", "path_type": "hardlink", "sha256": "529815b0dd4bd6608bafeeb3d410b0683374e61aef792b3e3f38b3767d26f747", "sha256_in_prefix": "529815b0dd4bd6608bafeeb3d410b0683374e61aef792b3e3f38b3767d26f747", "size_in_bytes": 12324}, {"_path": "Library/ssl/openssl.cnf.dist", "path_type": "hardlink", "sha256": "529815b0dd4bd6608bafeeb3d410b0683374e61aef792b3e3f38b3767d26f747", "sha256_in_prefix": "529815b0dd4bd6608bafeeb3d410b0683374e61aef792b3e3f38b3767d26f747", "size_in_bytes": 12324}, {"_path": "etc/conda/activate.d/openssl_activate.bat", "path_type": "hardlink", "sha256": "b5ec69ff3a2abead2e4141058c056fe89696ec6ff265db3a6e12d54a7b0cf968", "sha256_in_prefix": "b5ec69ff3a2abead2e4141058c056fe89696ec6ff265db3a6e12d54a7b0cf968", "size_in_bytes": 141}, {"_path": "etc/conda/activate.d/openssl_activate.ps1", "path_type": "hardlink", "sha256": "4faf38998848de897f3d1bf343b9ed4f3357f9be924ae3f39fa878e6eac4d990", "sha256_in_prefix": "4faf38998848de897f3d1bf343b9ed4f3357f9be924ae3f39fa878e6eac4d990", "size_in_bytes": 146}, {"_path": "etc/conda/activate.d/openssl_activate.sh", "path_type": "hardlink", "sha256": "0491ca20c6a5232c054965f317e9e867306315e777b611a94a02e8cfdfb66dbe", "sha256_in_prefix": "0491ca20c6a5232c054965f317e9e867306315e777b611a94a02e8cfdfb66dbe", "size_in_bytes": 158}, {"_path": "etc/conda/deactivate.d/openssl_deactivate.bat", "path_type": "hardlink", "sha256": "66ccd7108ea8c1718f60ae81f07794f48a8c4cb3f5ab1dd2a8a17484991afdcb", "sha256_in_prefix": "66ccd7108ea8c1718f60ae81f07794f48a8c4cb3f5ab1dd2a8a17484991afdcb", "size_in_bytes": 120}, {"_path": "etc/conda/deactivate.d/openssl_deactivate.ps1", "path_type": "hardlink", "sha256": "d3c530cac4c25c2cdd70a1db5eb2078ee66c1a7e9af0b7d972fec7d22bcdc112", "sha256_in_prefix": "d3c530cac4c25c2cdd70a1db5eb2078ee66c1a7e9af0b7d972fec7d22bcdc112", "size_in_bytes": 150}, {"_path": "etc/conda/deactivate.d/openssl_deactivate.sh", "path_type": "hardlink", "sha256": "1a597cfb46c521b4dfc99ace069b32045c516421f3e83f76587871f85f29c272", "sha256_in_prefix": "1a597cfb46c521b4dfc99ace069b32045c516421f3e83f76587871f85f29c272", "size_in_bytes": 126}], "paths_version": 1}, "requested_spec": "None", "sha256": "27941fbec3fab999105c4a26cd999318b92da39031b1b4c15872a251e436a970", "size": 8195174, "subdir": "win-64", "timestamp": 1740991970485, "track_features": "", "url": "https://repo.anaconda.com/pkgs/main/win-64/openssl-3.0.16-h3f729d1_0.conda", "version": "3.0.16"}