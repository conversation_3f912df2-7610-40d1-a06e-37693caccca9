{"build": "hbfb602d_5", "build_number": 5, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": [], "extracted_package_dir": "C:\\Users\\<USER>\\anaconda3\\pkgs\\vs2015_runtime-14.42.34433-hbfb602d_5", "features": "", "files": ["Library/bin/api-ms-win-core-console-l1-1-0.dll", "Library/bin/api-ms-win-core-console-l1-2-0.dll", "Library/bin/api-ms-win-core-datetime-l1-1-0.dll", "Library/bin/api-ms-win-core-debug-l1-1-0.dll", "Library/bin/api-ms-win-core-errorhandling-l1-1-0.dll", "Library/bin/api-ms-win-core-fibers-l1-1-0.dll", "Library/bin/api-ms-win-core-file-l1-1-0.dll", "Library/bin/api-ms-win-core-file-l1-2-0.dll", "Library/bin/api-ms-win-core-file-l2-1-0.dll", "Library/bin/api-ms-win-core-handle-l1-1-0.dll", "Library/bin/api-ms-win-core-heap-l1-1-0.dll", "Library/bin/api-ms-win-core-interlocked-l1-1-0.dll", "Library/bin/api-ms-win-core-libraryloader-l1-1-0.dll", "Library/bin/api-ms-win-core-localization-l1-2-0.dll", "Library/bin/api-ms-win-core-memory-l1-1-0.dll", "Library/bin/api-ms-win-core-namedpipe-l1-1-0.dll", "Library/bin/api-ms-win-core-processenvironment-l1-1-0.dll", "Library/bin/api-ms-win-core-processthreads-l1-1-0.dll", "Library/bin/api-ms-win-core-processthreads-l1-1-1.dll", "Library/bin/api-ms-win-core-profile-l1-1-0.dll", "Library/bin/api-ms-win-core-rtlsupport-l1-1-0.dll", "Library/bin/api-ms-win-core-string-l1-1-0.dll", "Library/bin/api-ms-win-core-synch-l1-1-0.dll", "Library/bin/api-ms-win-core-synch-l1-2-0.dll", "Library/bin/api-ms-win-core-sysinfo-l1-1-0.dll", "Library/bin/api-ms-win-core-timezone-l1-1-0.dll", "Library/bin/api-ms-win-core-util-l1-1-0.dll", "Library/bin/api-ms-win-crt-conio-l1-1-0.dll", "Library/bin/api-ms-win-crt-convert-l1-1-0.dll", "Library/bin/api-ms-win-crt-environment-l1-1-0.dll", "Library/bin/api-ms-win-crt-filesystem-l1-1-0.dll", "Library/bin/api-ms-win-crt-heap-l1-1-0.dll", "Library/bin/api-ms-win-crt-locale-l1-1-0.dll", "Library/bin/api-ms-win-crt-math-l1-1-0.dll", "Library/bin/api-ms-win-crt-multibyte-l1-1-0.dll", "Library/bin/api-ms-win-crt-private-l1-1-0.dll", "Library/bin/api-ms-win-crt-process-l1-1-0.dll", "Library/bin/api-ms-win-crt-runtime-l1-1-0.dll", "Library/bin/api-ms-win-crt-stdio-l1-1-0.dll", "Library/bin/api-ms-win-crt-string-l1-1-0.dll", "Library/bin/api-ms-win-crt-time-l1-1-0.dll", "Library/bin/api-ms-win-crt-utility-l1-1-0.dll", "Library/bin/concrt140.dll", "Library/bin/msvcp140.dll", "Library/bin/msvcp140_1.dll", "Library/bin/msvcp140_2.dll", "Library/bin/msvcp140_atomic_wait.dll", "Library/bin/msvcp140_codecvt_ids.dll", "Library/bin/ucrtbase.dll", "Library/bin/vccorlib140.dll", "Library/bin/vcomp140.dll", "Library/bin/vcruntime140.dll", "Library/bin/vcruntime140_1.dll", "Library/bin/vcruntime140_threads.dll", "api-ms-win-core-console-l1-1-0.dll", "api-ms-win-core-console-l1-2-0.dll", "api-ms-win-core-datetime-l1-1-0.dll", "api-ms-win-core-debug-l1-1-0.dll", "api-ms-win-core-errorhandling-l1-1-0.dll", "api-ms-win-core-fibers-l1-1-0.dll", "api-ms-win-core-file-l1-1-0.dll", "api-ms-win-core-file-l1-2-0.dll", "api-ms-win-core-file-l2-1-0.dll", "api-ms-win-core-handle-l1-1-0.dll", "api-ms-win-core-heap-l1-1-0.dll", "api-ms-win-core-interlocked-l1-1-0.dll", "api-ms-win-core-libraryloader-l1-1-0.dll", "api-ms-win-core-localization-l1-2-0.dll", "api-ms-win-core-memory-l1-1-0.dll", "api-ms-win-core-namedpipe-l1-1-0.dll", "api-ms-win-core-processenvironment-l1-1-0.dll", "api-ms-win-core-processthreads-l1-1-0.dll", "api-ms-win-core-processthreads-l1-1-1.dll", "api-ms-win-core-profile-l1-1-0.dll", "api-ms-win-core-rtlsupport-l1-1-0.dll", "api-ms-win-core-string-l1-1-0.dll", "api-ms-win-core-synch-l1-1-0.dll", "api-ms-win-core-synch-l1-2-0.dll", "api-ms-win-core-sysinfo-l1-1-0.dll", "api-ms-win-core-timezone-l1-1-0.dll", "api-ms-win-core-util-l1-1-0.dll", "api-ms-win-crt-conio-l1-1-0.dll", "api-ms-win-crt-convert-l1-1-0.dll", "api-ms-win-crt-environment-l1-1-0.dll", "api-ms-win-crt-filesystem-l1-1-0.dll", "api-ms-win-crt-heap-l1-1-0.dll", "api-ms-win-crt-locale-l1-1-0.dll", "api-ms-win-crt-math-l1-1-0.dll", "api-ms-win-crt-multibyte-l1-1-0.dll", "api-ms-win-crt-private-l1-1-0.dll", "api-ms-win-crt-process-l1-1-0.dll", "api-ms-win-crt-runtime-l1-1-0.dll", "api-ms-win-crt-stdio-l1-1-0.dll", "api-ms-win-crt-string-l1-1-0.dll", "api-ms-win-crt-time-l1-1-0.dll", "api-ms-win-crt-utility-l1-1-0.dll", "concrt140.dll", "msvcp140.dll", "msvcp140_1.dll", "msvcp140_2.dll", "msvcp140_atomic_wait.dll", "msvcp140_codecvt_ids.dll", "ucrtbase.dll", "vccorlib140.dll", "vcomp140.dll", "vcruntime140.dll", "vcruntime140_1.dll", "vcruntime140_threads.dll", ".nonadmin"], "fn": "vs2015_runtime-14.42.34433-hbfb602d_5.conda", "legacy_bz2_md5": "8d1e14224a61c6344bb626745ae6f457", "link": {"source": "C:\\Users\\<USER>\\anaconda3\\pkgs\\vs2015_runtime-14.42.34433-hbfb602d_5", "type": 1}, "md5": "9f125b013ca3a3b47b4909a79cd3bcb9", "name": "vs2015_runtime", "package_tarball_full_path": "C:\\Users\\<USER>\\anaconda3\\pkgs\\vs2015_runtime-14.42.34433-hbfb602d_5.conda", "paths_data": {"paths": [{"_path": "Library/bin/api-ms-win-core-console-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "22b0d0412c274a04d11d7fd3f6545eff245e6f032e21b86d920c2844dd1007c6", "sha256_in_prefix": "22b0d0412c274a04d11d7fd3f6545eff245e6f032e21b86d920c2844dd1007c6", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-console-l1-2-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "0c60ddcfddc6e11d77354d5695b822881f37fb537192e61e62f2ebd703fb2119", "sha256_in_prefix": "0c60ddcfddc6e11d77354d5695b822881f37fb537192e61e62f2ebd703fb2119", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-datetime-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "631a106755f13a78032d7c17cd19c5185fe89d93fda2ac108c4f53e27dcdecdb", "sha256_in_prefix": "631a106755f13a78032d7c17cd19c5185fe89d93fda2ac108c4f53e27dcdecdb", "size_in_bytes": 22120}, {"_path": "Library/bin/api-ms-win-core-debug-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "1ce185afeea0a30a12b496d95df395c18bd0e99570c0ac3126758476d4b6aea0", "sha256_in_prefix": "1ce185afeea0a30a12b496d95df395c18bd0e99570c0ac3126758476d4b6aea0", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-errorhandling-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "cdbc9ee30658188fd9af68ad52b5d8e7f59111191b0681ec2ed9095d9c85ebee", "sha256_in_prefix": "cdbc9ee30658188fd9af68ad52b5d8e7f59111191b0681ec2ed9095d9c85ebee", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-fibers-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "938a52984e7e9ffeef350a794907639d453e346d5bdc0aec8c1360d040cc672a", "sha256_in_prefix": "938a52984e7e9ffeef350a794907639d453e346d5bdc0aec8c1360d040cc672a", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-file-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "f36025fd0715ec893c112f06472072c565385b8c5fa675cce5b4a9158bfb87e9", "sha256_in_prefix": "f36025fd0715ec893c112f06472072c565385b8c5fa675cce5b4a9158bfb87e9", "size_in_bytes": 26232}, {"_path": "Library/bin/api-ms-win-core-file-l1-2-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "3d95961590fe6da5c569bcb0a54651488e70dd7b15c257e1b9faf8a3cc0e63e4", "sha256_in_prefix": "3d95961590fe6da5c569bcb0a54651488e70dd7b15c257e1b9faf8a3cc0e63e4", "size_in_bytes": 22120}, {"_path": "Library/bin/api-ms-win-core-file-l2-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "c6515fb573cd8190ebc401aab4646069066205ee9eeca548ae5ddbec3633336b", "sha256_in_prefix": "c6515fb573cd8190ebc401aab4646069066205ee9eeca548ae5ddbec3633336b", "size_in_bytes": 22120}, {"_path": "Library/bin/api-ms-win-core-handle-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "523b4c1528aab62c5f8622e4e2c4a4ba0df43114098a05f0c58c69c716c42626", "sha256_in_prefix": "523b4c1528aab62c5f8622e4e2c4a4ba0df43114098a05f0c58c69c716c42626", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-heap-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "4fff338c18ab8a1a37d1190e3b9edcca55afa86b0ba0f97d87c4c841e4e29678", "sha256_in_prefix": "4fff338c18ab8a1a37d1190e3b9edcca55afa86b0ba0f97d87c4c841e4e29678", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-interlocked-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "841aa4632552c47b43d453968da2c8d0861b1eb776d530a4e985d0290516d6c8", "sha256_in_prefix": "841aa4632552c47b43d453968da2c8d0861b1eb776d530a4e985d0290516d6c8", "size_in_bytes": 22120}, {"_path": "Library/bin/api-ms-win-core-libraryloader-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "e989c62edade6b3333d798e0481f4c2ec08f7d2a0c47acfcca2a869cd4b68a1f", "sha256_in_prefix": "e989c62edade6b3333d798e0481f4c2ec08f7d2a0c47acfcca2a869cd4b68a1f", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-localization-l1-2-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "945dacfe53f62d83acd0537a6712658558faafb18f68b76b88127db78482fd8f", "sha256_in_prefix": "945dacfe53f62d83acd0537a6712658558faafb18f68b76b88127db78482fd8f", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-memory-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "a505bdf2e4dfd5120de230fd9d159ef75aa00fb3f98e24d259f5c0a456713c74", "sha256_in_prefix": "a505bdf2e4dfd5120de230fd9d159ef75aa00fb3f98e24d259f5c0a456713c74", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-namedpipe-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "b26de5e517b05e10ee34fdf4996f82c465668670329e7f19d21f39a7e39011e7", "sha256_in_prefix": "b26de5e517b05e10ee34fdf4996f82c465668670329e7f19d21f39a7e39011e7", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-processenvironment-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "32d11f07156248c7906027e0f17e93e51de848f136e6d3fd0d4f9d1ffb2c70da", "sha256_in_prefix": "32d11f07156248c7906027e0f17e93e51de848f136e6d3fd0d4f9d1ffb2c70da", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-processthreads-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "562e2619e1e685080faf2122c12ae3c35202ce34ce8330d1ff0a3b566095fd38", "sha256_in_prefix": "562e2619e1e685080faf2122c12ae3c35202ce34ce8330d1ff0a3b566095fd38", "size_in_bytes": 22120}, {"_path": "Library/bin/api-ms-win-core-processthreads-l1-1-1.dll", "no_link": true, "path_type": "hardlink", "sha256": "73fabc60a9b24c1eb65ec886a59a190046af5853800572df1d48634417a15729", "sha256_in_prefix": "73fabc60a9b24c1eb65ec886a59a190046af5853800572df1d48634417a15729", "size_in_bytes": 22120}, {"_path": "Library/bin/api-ms-win-core-profile-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "2401cc9407ebb1fa60ddf520d422ec1eefec050dd9871554756c869c9b730558", "sha256_in_prefix": "2401cc9407ebb1fa60ddf520d422ec1eefec050dd9871554756c869c9b730558", "size_in_bytes": 22120}, {"_path": "Library/bin/api-ms-win-core-rtlsupport-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "b5a61cd60ec9088ee27bf61d37c55abc9d6db3f722616d74fc191cf671a4902a", "sha256_in_prefix": "b5a61cd60ec9088ee27bf61d37c55abc9d6db3f722616d74fc191cf671a4902a", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-string-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "7e97fbf5cee26ab01227d564f023337736310868c1cf23920e4dceeeb1c11701", "sha256_in_prefix": "7e97fbf5cee26ab01227d564f023337736310868c1cf23920e4dceeeb1c11701", "size_in_bytes": 22112}, {"_path": "Library/bin/api-ms-win-core-synch-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "4f47aad2664ed21dd80d30ffd954a34503ffe2493bebb39da058d452212e75af", "sha256_in_prefix": "4f47aad2664ed21dd80d30ffd954a34503ffe2493bebb39da058d452212e75af", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-synch-l1-2-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "a885774d4a5419db2e9f7fbd0ac06f7244e046aa614cd6585ab22fc428f2c7ee", "sha256_in_prefix": "a885774d4a5419db2e9f7fbd0ac06f7244e046aa614cd6585ab22fc428f2c7ee", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-core-sysinfo-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "ac3b86a3e66c5ae2cb30d8a386b0574e6b59fe0f549120c16b7790c3489bc593", "sha256_in_prefix": "ac3b86a3e66c5ae2cb30d8a386b0574e6b59fe0f549120c16b7790c3489bc593", "size_in_bytes": 22120}, {"_path": "Library/bin/api-ms-win-core-timezone-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "003751ed79881bce98017b66206a2ba411321edd61fd51768779f29dfa99968d", "sha256_in_prefix": "003751ed79881bce98017b66206a2ba411321edd61fd51768779f29dfa99968d", "size_in_bytes": 22120}, {"_path": "Library/bin/api-ms-win-core-util-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "eb41c514f2660813fa6ac58a28bcd2adfb64552b945dfcad5123f51a1a71f863", "sha256_in_prefix": "eb41c514f2660813fa6ac58a28bcd2adfb64552b945dfcad5123f51a1a71f863", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-crt-conio-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "e7fc8d3956ce856b1ce0b8d16c10fa4c886a33717a64b818aa6d2492d7492429", "sha256_in_prefix": "e7fc8d3956ce856b1ce0b8d16c10fa4c886a33717a64b818aa6d2492d7492429", "size_in_bytes": 22120}, {"_path": "Library/bin/api-ms-win-crt-convert-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "4d80b38c49c9e507190b133e97c7b06ac926c5e1d93095bef8e35c51e7be4e3c", "sha256_in_prefix": "4d80b38c49c9e507190b133e97c7b06ac926c5e1d93095bef8e35c51e7be4e3c", "size_in_bytes": 26216}, {"_path": "Library/bin/api-ms-win-crt-environment-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "65eb6a1276426e0bffc0a7686770cae2fb15a0f819cab4b96003a292c483ec5d", "sha256_in_prefix": "65eb6a1276426e0bffc0a7686770cae2fb15a0f819cab4b96003a292c483ec5d", "size_in_bytes": 22120}, {"_path": "Library/bin/api-ms-win-crt-filesystem-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "61424c4c6cf665ef1c6e092a105721813d495ff17d81c809b505acf9ac0c575a", "sha256_in_prefix": "61424c4c6cf665ef1c6e092a105721813d495ff17d81c809b505acf9ac0c575a", "size_in_bytes": 22120}, {"_path": "Library/bin/api-ms-win-crt-heap-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "42fed4693a9f2ee8ebb29b34ac92aaef9ff070f609e0cbff74258f65ea53d666", "sha256_in_prefix": "42fed4693a9f2ee8ebb29b34ac92aaef9ff070f609e0cbff74258f65ea53d666", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-crt-locale-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "f2d099d580c733d3132ac3cd0179c7bfa0f1ed8f7bd063e411cd57e920510488", "sha256_in_prefix": "f2d099d580c733d3132ac3cd0179c7bfa0f1ed8f7bd063e411cd57e920510488", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-crt-math-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "8483828a6781dc3cc4a121e2a90f54abb6f6c42680a0634e02db8b736d16f877", "sha256_in_prefix": "8483828a6781dc3cc4a121e2a90f54abb6f6c42680a0634e02db8b736d16f877", "size_in_bytes": 30328}, {"_path": "Library/bin/api-ms-win-crt-multibyte-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "63c233965965c6eab235c0e1e7530788fee44d5cab910a2cd22d325334a3dce2", "sha256_in_prefix": "63c233965965c6eab235c0e1e7530788fee44d5cab910a2cd22d325334a3dce2", "size_in_bytes": 30328}, {"_path": "Library/bin/api-ms-win-crt-private-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "65e8380924c6b54147185cfd84deb0795c617b8c316d3767daa616f9fd88d6c0", "sha256_in_prefix": "65e8380924c6b54147185cfd84deb0795c617b8c316d3767daa616f9fd88d6c0", "size_in_bytes": 75368}, {"_path": "Library/bin/api-ms-win-crt-process-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "f426c73f187c4c3c6759514c11f752a1f8411a1f4392bbee984652e62d2e7296", "sha256_in_prefix": "f426c73f187c4c3c6759514c11f752a1f8411a1f4392bbee984652e62d2e7296", "size_in_bytes": 22136}, {"_path": "Library/bin/api-ms-win-crt-runtime-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "21c3d46f74f4249c81d723373da639ac2fb2733a626ec11310df49874663d2bc", "sha256_in_prefix": "21c3d46f74f4249c81d723373da639ac2fb2733a626ec11310df49874663d2bc", "size_in_bytes": 26216}, {"_path": "Library/bin/api-ms-win-crt-stdio-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "e4a6bd5d65d39da4424ab7828959cfeb7c362e29008bc63ecf16fb3b20001807", "sha256_in_prefix": "e4a6bd5d65d39da4424ab7828959cfeb7c362e29008bc63ecf16fb3b20001807", "size_in_bytes": 26216}, {"_path": "Library/bin/api-ms-win-crt-string-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "a1cf3656daa57afb840714f891e7f072ca56bf5838f525d4394c362ef5c8ed8f", "sha256_in_prefix": "a1cf3656daa57afb840714f891e7f072ca56bf5838f525d4394c362ef5c8ed8f", "size_in_bytes": 26216}, {"_path": "Library/bin/api-ms-win-crt-time-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "fc27f6061faf91d696a2b3685a3bfac4de49fbf78d578b9970e1ed21f683b209", "sha256_in_prefix": "fc27f6061faf91d696a2b3685a3bfac4de49fbf78d578b9970e1ed21f683b209", "size_in_bytes": 22120}, {"_path": "Library/bin/api-ms-win-crt-utility-l1-1-0.dll", "no_link": true, "path_type": "hardlink", "sha256": "788611c05ef16759ac57df231b25b413be33fccabbeb446caaf4b5cbedd8e1dc", "sha256_in_prefix": "788611c05ef16759ac57df231b25b413be33fccabbeb446caaf4b5cbedd8e1dc", "size_in_bytes": 22136}, {"_path": "Library/bin/concrt140.dll", "no_link": true, "path_type": "hardlink", "sha256": "1cccd0f5553d6c4eaf452c5524b4312db7edb206c5d06fdcb6052e9586f6c611", "sha256_in_prefix": "1cccd0f5553d6c4eaf452c5524b4312db7edb206c5d06fdcb6052e9586f6c611", "size_in_bytes": 322672}, {"_path": "Library/bin/msvcp140.dll", "no_link": true, "path_type": "hardlink", "sha256": "b99eb28a471311113f5c4109cb3c463f39cfd9bdb3b07f706204dedddb4516a1", "sha256_in_prefix": "b99eb28a471311113f5c4109cb3c463f39cfd9bdb3b07f706204dedddb4516a1", "size_in_bytes": 575568}, {"_path": "Library/bin/msvcp140_1.dll", "no_link": true, "path_type": "hardlink", "sha256": "576d2ab235e32acc129eda78a3b9a3d3e78b0c97a01940d962cb8502acd030d1", "sha256_in_prefix": "576d2ab235e32acc129eda78a3b9a3d3e78b0c97a01940d962cb8502acd030d1", "size_in_bytes": 35920}, {"_path": "Library/bin/msvcp140_2.dll", "no_link": true, "path_type": "hardlink", "sha256": "9c9e06d56add83e08f0c530de6f0a82c5bfbee2b6ffa8b71e7b0446ec08e8970", "sha256_in_prefix": "9c9e06d56add83e08f0c530de6f0a82c5bfbee2b6ffa8b71e7b0446ec08e8970", "size_in_bytes": 267888}, {"_path": "Library/bin/msvcp140_atomic_wait.dll", "no_link": true, "path_type": "hardlink", "sha256": "5ec85a18501af700e3ee748daed242b8e33040f090371a2759217cb71ff26469", "sha256_in_prefix": "5ec85a18501af700e3ee748daed242b8e33040f090371a2759217cb71ff26469", "size_in_bytes": 50288}, {"_path": "Library/bin/msvcp140_codecvt_ids.dll", "no_link": true, "path_type": "hardlink", "sha256": "a12581d37f291121346e8ba2fdfec64a3d72c07aec6350ea0cd2f7aac2f750a4", "sha256_in_prefix": "a12581d37f291121346e8ba2fdfec64a3d72c07aec6350ea0cd2f7aac2f750a4", "size_in_bytes": 31856}, {"_path": "Library/bin/ucrtbase.dll", "no_link": true, "path_type": "hardlink", "sha256": "91d027417ff2301b7135e864a5df6693488f8412ff87040f4897e0e03bc2577b", "sha256_in_prefix": "91d027417ff2301b7135e864a5df6693488f8412ff87040f4897e0e03bc2577b", "size_in_bytes": 1123944}, {"_path": "Library/bin/vccorlib140.dll", "no_link": true, "path_type": "hardlink", "sha256": "dca072ee3c9f5dae8c0de6b432600dd766c5d851a1a14a572185ed48ebcf37aa", "sha256_in_prefix": "dca072ee3c9f5dae8c0de6b432600dd766c5d851a1a14a572185ed48ebcf37aa", "size_in_bytes": 351824}, {"_path": "Library/bin/vcomp140.dll", "no_link": true, "path_type": "hardlink", "sha256": "e36a5c5e329bc7af35d4faa610a29aeee826a7810e06712f0f54e9b2cfe6a728", "sha256_in_prefix": "e36a5c5e329bc7af35d4faa610a29aeee826a7810e06712f0f54e9b2cfe6a728", "size_in_bytes": 192112}, {"_path": "Library/bin/vcruntime140.dll", "no_link": true, "path_type": "hardlink", "sha256": "052ad6a20d375957e82aa6a3c441ea548d89be0981516ca7eb306e063d5027f4", "sha256_in_prefix": "052ad6a20d375957e82aa6a3c441ea548d89be0981516ca7eb306e063d5027f4", "size_in_bytes": 120400}, {"_path": "Library/bin/vcruntime140_1.dll", "no_link": true, "path_type": "hardlink", "sha256": "6a99bc0128e0c7d6cbbf615fcc26909565e17d4ca3451b97f8987f9c6acbc6c8", "sha256_in_prefix": "6a99bc0128e0c7d6cbbf615fcc26909565e17d4ca3451b97f8987f9c6acbc6c8", "size_in_bytes": 49776}, {"_path": "Library/bin/vcruntime140_threads.dll", "no_link": true, "path_type": "hardlink", "sha256": "fcf24d7fdea131ec60e59e281fe4cebd0b5f54a49180c40bba43f859f7bc47f3", "sha256_in_prefix": "fcf24d7fdea131ec60e59e281fe4cebd0b5f54a49180c40bba43f859f7bc47f3", "size_in_bytes": 38480}, {"_path": "api-ms-win-core-console-l1-1-0.dll", "path_type": "hardlink", "sha256": "22b0d0412c274a04d11d7fd3f6545eff245e6f032e21b86d920c2844dd1007c6", "sha256_in_prefix": "22b0d0412c274a04d11d7fd3f6545eff245e6f032e21b86d920c2844dd1007c6", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-console-l1-2-0.dll", "path_type": "hardlink", "sha256": "0c60ddcfddc6e11d77354d5695b822881f37fb537192e61e62f2ebd703fb2119", "sha256_in_prefix": "0c60ddcfddc6e11d77354d5695b822881f37fb537192e61e62f2ebd703fb2119", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-datetime-l1-1-0.dll", "path_type": "hardlink", "sha256": "631a106755f13a78032d7c17cd19c5185fe89d93fda2ac108c4f53e27dcdecdb", "sha256_in_prefix": "631a106755f13a78032d7c17cd19c5185fe89d93fda2ac108c4f53e27dcdecdb", "size_in_bytes": 22120}, {"_path": "api-ms-win-core-debug-l1-1-0.dll", "path_type": "hardlink", "sha256": "1ce185afeea0a30a12b496d95df395c18bd0e99570c0ac3126758476d4b6aea0", "sha256_in_prefix": "1ce185afeea0a30a12b496d95df395c18bd0e99570c0ac3126758476d4b6aea0", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-errorhandling-l1-1-0.dll", "path_type": "hardlink", "sha256": "cdbc9ee30658188fd9af68ad52b5d8e7f59111191b0681ec2ed9095d9c85ebee", "sha256_in_prefix": "cdbc9ee30658188fd9af68ad52b5d8e7f59111191b0681ec2ed9095d9c85ebee", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-fibers-l1-1-0.dll", "path_type": "hardlink", "sha256": "938a52984e7e9ffeef350a794907639d453e346d5bdc0aec8c1360d040cc672a", "sha256_in_prefix": "938a52984e7e9ffeef350a794907639d453e346d5bdc0aec8c1360d040cc672a", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-file-l1-1-0.dll", "path_type": "hardlink", "sha256": "f36025fd0715ec893c112f06472072c565385b8c5fa675cce5b4a9158bfb87e9", "sha256_in_prefix": "f36025fd0715ec893c112f06472072c565385b8c5fa675cce5b4a9158bfb87e9", "size_in_bytes": 26232}, {"_path": "api-ms-win-core-file-l1-2-0.dll", "path_type": "hardlink", "sha256": "3d95961590fe6da5c569bcb0a54651488e70dd7b15c257e1b9faf8a3cc0e63e4", "sha256_in_prefix": "3d95961590fe6da5c569bcb0a54651488e70dd7b15c257e1b9faf8a3cc0e63e4", "size_in_bytes": 22120}, {"_path": "api-ms-win-core-file-l2-1-0.dll", "path_type": "hardlink", "sha256": "c6515fb573cd8190ebc401aab4646069066205ee9eeca548ae5ddbec3633336b", "sha256_in_prefix": "c6515fb573cd8190ebc401aab4646069066205ee9eeca548ae5ddbec3633336b", "size_in_bytes": 22120}, {"_path": "api-ms-win-core-handle-l1-1-0.dll", "path_type": "hardlink", "sha256": "523b4c1528aab62c5f8622e4e2c4a4ba0df43114098a05f0c58c69c716c42626", "sha256_in_prefix": "523b4c1528aab62c5f8622e4e2c4a4ba0df43114098a05f0c58c69c716c42626", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-heap-l1-1-0.dll", "path_type": "hardlink", "sha256": "4fff338c18ab8a1a37d1190e3b9edcca55afa86b0ba0f97d87c4c841e4e29678", "sha256_in_prefix": "4fff338c18ab8a1a37d1190e3b9edcca55afa86b0ba0f97d87c4c841e4e29678", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-interlocked-l1-1-0.dll", "path_type": "hardlink", "sha256": "841aa4632552c47b43d453968da2c8d0861b1eb776d530a4e985d0290516d6c8", "sha256_in_prefix": "841aa4632552c47b43d453968da2c8d0861b1eb776d530a4e985d0290516d6c8", "size_in_bytes": 22120}, {"_path": "api-ms-win-core-libraryloader-l1-1-0.dll", "path_type": "hardlink", "sha256": "e989c62edade6b3333d798e0481f4c2ec08f7d2a0c47acfcca2a869cd4b68a1f", "sha256_in_prefix": "e989c62edade6b3333d798e0481f4c2ec08f7d2a0c47acfcca2a869cd4b68a1f", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-localization-l1-2-0.dll", "path_type": "hardlink", "sha256": "945dacfe53f62d83acd0537a6712658558faafb18f68b76b88127db78482fd8f", "sha256_in_prefix": "945dacfe53f62d83acd0537a6712658558faafb18f68b76b88127db78482fd8f", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-memory-l1-1-0.dll", "path_type": "hardlink", "sha256": "a505bdf2e4dfd5120de230fd9d159ef75aa00fb3f98e24d259f5c0a456713c74", "sha256_in_prefix": "a505bdf2e4dfd5120de230fd9d159ef75aa00fb3f98e24d259f5c0a456713c74", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-namedpipe-l1-1-0.dll", "path_type": "hardlink", "sha256": "b26de5e517b05e10ee34fdf4996f82c465668670329e7f19d21f39a7e39011e7", "sha256_in_prefix": "b26de5e517b05e10ee34fdf4996f82c465668670329e7f19d21f39a7e39011e7", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-processenvironment-l1-1-0.dll", "path_type": "hardlink", "sha256": "32d11f07156248c7906027e0f17e93e51de848f136e6d3fd0d4f9d1ffb2c70da", "sha256_in_prefix": "32d11f07156248c7906027e0f17e93e51de848f136e6d3fd0d4f9d1ffb2c70da", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-processthreads-l1-1-0.dll", "path_type": "hardlink", "sha256": "562e2619e1e685080faf2122c12ae3c35202ce34ce8330d1ff0a3b566095fd38", "sha256_in_prefix": "562e2619e1e685080faf2122c12ae3c35202ce34ce8330d1ff0a3b566095fd38", "size_in_bytes": 22120}, {"_path": "api-ms-win-core-processthreads-l1-1-1.dll", "path_type": "hardlink", "sha256": "73fabc60a9b24c1eb65ec886a59a190046af5853800572df1d48634417a15729", "sha256_in_prefix": "73fabc60a9b24c1eb65ec886a59a190046af5853800572df1d48634417a15729", "size_in_bytes": 22120}, {"_path": "api-ms-win-core-profile-l1-1-0.dll", "path_type": "hardlink", "sha256": "2401cc9407ebb1fa60ddf520d422ec1eefec050dd9871554756c869c9b730558", "sha256_in_prefix": "2401cc9407ebb1fa60ddf520d422ec1eefec050dd9871554756c869c9b730558", "size_in_bytes": 22120}, {"_path": "api-ms-win-core-rtlsupport-l1-1-0.dll", "path_type": "hardlink", "sha256": "b5a61cd60ec9088ee27bf61d37c55abc9d6db3f722616d74fc191cf671a4902a", "sha256_in_prefix": "b5a61cd60ec9088ee27bf61d37c55abc9d6db3f722616d74fc191cf671a4902a", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-string-l1-1-0.dll", "path_type": "hardlink", "sha256": "7e97fbf5cee26ab01227d564f023337736310868c1cf23920e4dceeeb1c11701", "sha256_in_prefix": "7e97fbf5cee26ab01227d564f023337736310868c1cf23920e4dceeeb1c11701", "size_in_bytes": 22112}, {"_path": "api-ms-win-core-synch-l1-1-0.dll", "path_type": "hardlink", "sha256": "4f47aad2664ed21dd80d30ffd954a34503ffe2493bebb39da058d452212e75af", "sha256_in_prefix": "4f47aad2664ed21dd80d30ffd954a34503ffe2493bebb39da058d452212e75af", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-synch-l1-2-0.dll", "path_type": "hardlink", "sha256": "a885774d4a5419db2e9f7fbd0ac06f7244e046aa614cd6585ab22fc428f2c7ee", "sha256_in_prefix": "a885774d4a5419db2e9f7fbd0ac06f7244e046aa614cd6585ab22fc428f2c7ee", "size_in_bytes": 22136}, {"_path": "api-ms-win-core-sysinfo-l1-1-0.dll", "path_type": "hardlink", "sha256": "ac3b86a3e66c5ae2cb30d8a386b0574e6b59fe0f549120c16b7790c3489bc593", "sha256_in_prefix": "ac3b86a3e66c5ae2cb30d8a386b0574e6b59fe0f549120c16b7790c3489bc593", "size_in_bytes": 22120}, {"_path": "api-ms-win-core-timezone-l1-1-0.dll", "path_type": "hardlink", "sha256": "003751ed79881bce98017b66206a2ba411321edd61fd51768779f29dfa99968d", "sha256_in_prefix": "003751ed79881bce98017b66206a2ba411321edd61fd51768779f29dfa99968d", "size_in_bytes": 22120}, {"_path": "api-ms-win-core-util-l1-1-0.dll", "path_type": "hardlink", "sha256": "eb41c514f2660813fa6ac58a28bcd2adfb64552b945dfcad5123f51a1a71f863", "sha256_in_prefix": "eb41c514f2660813fa6ac58a28bcd2adfb64552b945dfcad5123f51a1a71f863", "size_in_bytes": 22136}, {"_path": "api-ms-win-crt-conio-l1-1-0.dll", "path_type": "hardlink", "sha256": "e7fc8d3956ce856b1ce0b8d16c10fa4c886a33717a64b818aa6d2492d7492429", "sha256_in_prefix": "e7fc8d3956ce856b1ce0b8d16c10fa4c886a33717a64b818aa6d2492d7492429", "size_in_bytes": 22120}, {"_path": "api-ms-win-crt-convert-l1-1-0.dll", "path_type": "hardlink", "sha256": "4d80b38c49c9e507190b133e97c7b06ac926c5e1d93095bef8e35c51e7be4e3c", "sha256_in_prefix": "4d80b38c49c9e507190b133e97c7b06ac926c5e1d93095bef8e35c51e7be4e3c", "size_in_bytes": 26216}, {"_path": "api-ms-win-crt-environment-l1-1-0.dll", "path_type": "hardlink", "sha256": "65eb6a1276426e0bffc0a7686770cae2fb15a0f819cab4b96003a292c483ec5d", "sha256_in_prefix": "65eb6a1276426e0bffc0a7686770cae2fb15a0f819cab4b96003a292c483ec5d", "size_in_bytes": 22120}, {"_path": "api-ms-win-crt-filesystem-l1-1-0.dll", "path_type": "hardlink", "sha256": "61424c4c6cf665ef1c6e092a105721813d495ff17d81c809b505acf9ac0c575a", "sha256_in_prefix": "61424c4c6cf665ef1c6e092a105721813d495ff17d81c809b505acf9ac0c575a", "size_in_bytes": 22120}, {"_path": "api-ms-win-crt-heap-l1-1-0.dll", "path_type": "hardlink", "sha256": "42fed4693a9f2ee8ebb29b34ac92aaef9ff070f609e0cbff74258f65ea53d666", "sha256_in_prefix": "42fed4693a9f2ee8ebb29b34ac92aaef9ff070f609e0cbff74258f65ea53d666", "size_in_bytes": 22136}, {"_path": "api-ms-win-crt-locale-l1-1-0.dll", "path_type": "hardlink", "sha256": "f2d099d580c733d3132ac3cd0179c7bfa0f1ed8f7bd063e411cd57e920510488", "sha256_in_prefix": "f2d099d580c733d3132ac3cd0179c7bfa0f1ed8f7bd063e411cd57e920510488", "size_in_bytes": 22136}, {"_path": "api-ms-win-crt-math-l1-1-0.dll", "path_type": "hardlink", "sha256": "8483828a6781dc3cc4a121e2a90f54abb6f6c42680a0634e02db8b736d16f877", "sha256_in_prefix": "8483828a6781dc3cc4a121e2a90f54abb6f6c42680a0634e02db8b736d16f877", "size_in_bytes": 30328}, {"_path": "api-ms-win-crt-multibyte-l1-1-0.dll", "path_type": "hardlink", "sha256": "63c233965965c6eab235c0e1e7530788fee44d5cab910a2cd22d325334a3dce2", "sha256_in_prefix": "63c233965965c6eab235c0e1e7530788fee44d5cab910a2cd22d325334a3dce2", "size_in_bytes": 30328}, {"_path": "api-ms-win-crt-private-l1-1-0.dll", "path_type": "hardlink", "sha256": "65e8380924c6b54147185cfd84deb0795c617b8c316d3767daa616f9fd88d6c0", "sha256_in_prefix": "65e8380924c6b54147185cfd84deb0795c617b8c316d3767daa616f9fd88d6c0", "size_in_bytes": 75368}, {"_path": "api-ms-win-crt-process-l1-1-0.dll", "path_type": "hardlink", "sha256": "f426c73f187c4c3c6759514c11f752a1f8411a1f4392bbee984652e62d2e7296", "sha256_in_prefix": "f426c73f187c4c3c6759514c11f752a1f8411a1f4392bbee984652e62d2e7296", "size_in_bytes": 22136}, {"_path": "api-ms-win-crt-runtime-l1-1-0.dll", "path_type": "hardlink", "sha256": "21c3d46f74f4249c81d723373da639ac2fb2733a626ec11310df49874663d2bc", "sha256_in_prefix": "21c3d46f74f4249c81d723373da639ac2fb2733a626ec11310df49874663d2bc", "size_in_bytes": 26216}, {"_path": "api-ms-win-crt-stdio-l1-1-0.dll", "path_type": "hardlink", "sha256": "e4a6bd5d65d39da4424ab7828959cfeb7c362e29008bc63ecf16fb3b20001807", "sha256_in_prefix": "e4a6bd5d65d39da4424ab7828959cfeb7c362e29008bc63ecf16fb3b20001807", "size_in_bytes": 26216}, {"_path": "api-ms-win-crt-string-l1-1-0.dll", "path_type": "hardlink", "sha256": "a1cf3656daa57afb840714f891e7f072ca56bf5838f525d4394c362ef5c8ed8f", "sha256_in_prefix": "a1cf3656daa57afb840714f891e7f072ca56bf5838f525d4394c362ef5c8ed8f", "size_in_bytes": 26216}, {"_path": "api-ms-win-crt-time-l1-1-0.dll", "path_type": "hardlink", "sha256": "fc27f6061faf91d696a2b3685a3bfac4de49fbf78d578b9970e1ed21f683b209", "sha256_in_prefix": "fc27f6061faf91d696a2b3685a3bfac4de49fbf78d578b9970e1ed21f683b209", "size_in_bytes": 22120}, {"_path": "api-ms-win-crt-utility-l1-1-0.dll", "path_type": "hardlink", "sha256": "788611c05ef16759ac57df231b25b413be33fccabbeb446caaf4b5cbedd8e1dc", "sha256_in_prefix": "788611c05ef16759ac57df231b25b413be33fccabbeb446caaf4b5cbedd8e1dc", "size_in_bytes": 22136}, {"_path": "concrt140.dll", "path_type": "hardlink", "sha256": "1cccd0f5553d6c4eaf452c5524b4312db7edb206c5d06fdcb6052e9586f6c611", "sha256_in_prefix": "1cccd0f5553d6c4eaf452c5524b4312db7edb206c5d06fdcb6052e9586f6c611", "size_in_bytes": 322672}, {"_path": "msvcp140.dll", "path_type": "hardlink", "sha256": "b99eb28a471311113f5c4109cb3c463f39cfd9bdb3b07f706204dedddb4516a1", "sha256_in_prefix": "b99eb28a471311113f5c4109cb3c463f39cfd9bdb3b07f706204dedddb4516a1", "size_in_bytes": 575568}, {"_path": "msvcp140_1.dll", "path_type": "hardlink", "sha256": "576d2ab235e32acc129eda78a3b9a3d3e78b0c97a01940d962cb8502acd030d1", "sha256_in_prefix": "576d2ab235e32acc129eda78a3b9a3d3e78b0c97a01940d962cb8502acd030d1", "size_in_bytes": 35920}, {"_path": "msvcp140_2.dll", "path_type": "hardlink", "sha256": "9c9e06d56add83e08f0c530de6f0a82c5bfbee2b6ffa8b71e7b0446ec08e8970", "sha256_in_prefix": "9c9e06d56add83e08f0c530de6f0a82c5bfbee2b6ffa8b71e7b0446ec08e8970", "size_in_bytes": 267888}, {"_path": "msvcp140_atomic_wait.dll", "path_type": "hardlink", "sha256": "5ec85a18501af700e3ee748daed242b8e33040f090371a2759217cb71ff26469", "sha256_in_prefix": "5ec85a18501af700e3ee748daed242b8e33040f090371a2759217cb71ff26469", "size_in_bytes": 50288}, {"_path": "msvcp140_codecvt_ids.dll", "path_type": "hardlink", "sha256": "a12581d37f291121346e8ba2fdfec64a3d72c07aec6350ea0cd2f7aac2f750a4", "sha256_in_prefix": "a12581d37f291121346e8ba2fdfec64a3d72c07aec6350ea0cd2f7aac2f750a4", "size_in_bytes": 31856}, {"_path": "ucrtbase.dll", "path_type": "hardlink", "sha256": "91d027417ff2301b7135e864a5df6693488f8412ff87040f4897e0e03bc2577b", "sha256_in_prefix": "91d027417ff2301b7135e864a5df6693488f8412ff87040f4897e0e03bc2577b", "size_in_bytes": 1123944}, {"_path": "vccorlib140.dll", "path_type": "hardlink", "sha256": "dca072ee3c9f5dae8c0de6b432600dd766c5d851a1a14a572185ed48ebcf37aa", "sha256_in_prefix": "dca072ee3c9f5dae8c0de6b432600dd766c5d851a1a14a572185ed48ebcf37aa", "size_in_bytes": 351824}, {"_path": "vcomp140.dll", "path_type": "hardlink", "sha256": "e36a5c5e329bc7af35d4faa610a29aeee826a7810e06712f0f54e9b2cfe6a728", "sha256_in_prefix": "e36a5c5e329bc7af35d4faa610a29aeee826a7810e06712f0f54e9b2cfe6a728", "size_in_bytes": 192112}, {"_path": "vcruntime140.dll", "path_type": "hardlink", "sha256": "052ad6a20d375957e82aa6a3c441ea548d89be0981516ca7eb306e063d5027f4", "sha256_in_prefix": "052ad6a20d375957e82aa6a3c441ea548d89be0981516ca7eb306e063d5027f4", "size_in_bytes": 120400}, {"_path": "vcruntime140_1.dll", "path_type": "hardlink", "sha256": "6a99bc0128e0c7d6cbbf615fcc26909565e17d4ca3451b97f8987f9c6acbc6c8", "sha256_in_prefix": "6a99bc0128e0c7d6cbbf615fcc26909565e17d4ca3451b97f8987f9c6acbc6c8", "size_in_bytes": 49776}, {"_path": "vcruntime140_threads.dll", "path_type": "hardlink", "sha256": "fcf24d7fdea131ec60e59e281fe4cebd0b5f54a49180c40bba43f859f7bc47f3", "sha256_in_prefix": "fcf24d7fdea131ec60e59e281fe4cebd0b5f54a49180c40bba43f859f7bc47f3", "size_in_bytes": 38480}], "paths_version": 1}, "requested_spec": "None", "sha256": "edfff335e41537c54dec03df71971803ec9fa5d036e1fdcb448fff0aea269508", "size": 1222969, "subdir": "win-64", "timestamp": 1744721168907, "track_features": "", "url": "https://repo.anaconda.com/pkgs/main/win-64/vs2015_runtime-14.42.34433-hbfb602d_5.conda", "version": "14.42.34433"}